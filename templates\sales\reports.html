{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "التقارير" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-chart-bar"></i> {% trans "التقارير والإحصائيات" %}
        </h1>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي المبيعات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_revenue|floatformat:2 }} {% trans "أوقية" %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "عدد الفواتير المدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ paid_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "إجمالي الفواتير" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "المنتجات قليلة المخزون" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ low_stock_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats Row -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "إجمالي المنتجات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-boxes fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                            {% trans "إجمالي الموردين" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_suppliers }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-truck fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-dark shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                            {% trans "فواتير الشراء" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_purchase_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "فواتير في الانتظار" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ pending_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Monthly Sales Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line"></i> {% trans "مبيعات آخر 6 أشهر" %}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="salesChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Payment Status Distribution -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie"></i> {% trans "توزيع حالات الدفع" %}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="paymentChart" width="400" height="400"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top Products -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-trophy"></i> {% trans "أفضل المنتجات مبيعاً" %}
                </h6>
            </div>
            <div class="card-body">
                {% if top_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{% trans "المنتج" %}</th>
                                    <th>{% trans "الفئة" %}</th>
                                    <th class="text-center">{% trans "المبيعات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <strong>{{ product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ product.price|floatformat:2 }} {% trans "أوقية" %}</small>
                                    </td>
                                    <td>{{ product.category.name }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-primary">{{ product.total_sold|default:0 }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">{% trans "لا توجد بيانات مبيعات حتى الآن" %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Top Customers -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users"></i> {% trans "أفضل العملاء" %}
                </h6>
            </div>
            <div class="card-body">
                {% if top_customers %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{% trans "العميل" %}</th>
                                    <th>{% trans "الهاتف" %}</th>
                                    <th class="text-center">{% trans "عدد الفواتير" %}</th>
                                    <th class="text-center">{% trans "إجمالي الإنفاق" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in top_customers %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <strong>{{ customer.name }}</strong>
                                        {% if customer.email %}
                                            <br>
                                            <small class="text-muted">{{ customer.email }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ customer.phone|default:"-" }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-success">{{ customer.total_invoices|default:0 }}</span>
                                    </td>
                                    <td class="text-center">
                                        <small>{{ customer.total_spent|floatformat:2|default:0 }} {% trans "أوقية" %}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">{% trans "لا توجد بيانات عملاء حتى الآن" %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Second Row: Suppliers and Low Stock -->
<div class="row">
    <!-- Top Suppliers -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-truck"></i> {% trans "أفضل الموردين" %}
                </h6>
            </div>
            <div class="card-body">
                {% if top_suppliers %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{% trans "المورد" %}</th>
                                    <th>{% trans "الهاتف" %}</th>
                                    <th class="text-center">{% trans "عدد المشتريات" %}</th>
                                    <th class="text-center">{% trans "إجمالي المبلغ" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in top_suppliers %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <strong>{{ supplier.name }}</strong>
                                        {% if supplier.email %}
                                            <br>
                                            <small class="text-muted">{{ supplier.email }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ supplier.phone|default:"-" }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ supplier.total_purchases|default:0 }}</span>
                                    </td>
                                    <td class="text-center">
                                        <small>{{ supplier.total_amount|floatformat:2|default:0 }} {% trans "أوقية" %}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">{% trans "لا توجد بيانات موردين حتى الآن" %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Low Stock Products -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle"></i> {% trans "منتجات قليلة المخزون" %}
                </h6>
            </div>
            <div class="card-body">
                {% if low_stock_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{% trans "المنتج" %}</th>
                                    <th>{% trans "الفئة" %}</th>
                                    <th class="text-center">{% trans "المخزون" %}</th>
                                    <th class="text-center">{% trans "الحالة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <strong>{{ product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ product.price|floatformat:2 }} {% trans "أوقية" %}</small>
                                    </td>
                                    <td>{{ product.category.name }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-warning">{{ product.stock_quantity }}</span>
                                    </td>
                                    <td class="text-center">
                                        {% if product.stock_quantity == 0 %}
                                            <span class="badge bg-danger">{% trans "نفد" %}</span>
                                        {% elif product.stock_quantity < 5 %}
                                            <span class="badge bg-warning">{% trans "قليل جداً" %}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{% trans "قليل" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">{% trans "جميع المنتجات لديها مخزون كافي" %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-download"></i> {% trans "تصدير التقارير" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-success w-100" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i> {% trans "تصدير إلى Excel" %}
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-danger w-100" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf"></i> {% trans "تصدير إلى PDF" %}
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-info w-100" onclick="window.print()">
                            <i class="fas fa-print"></i> {% trans "طباعة التقرير" %}
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-secondary w-100" onclick="location.reload()">
                            <i class="fas fa-sync"></i> {% trans "تحديث البيانات" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Sales Chart
const salesCtx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(salesCtx, {
    type: 'line',
    data: {
        labels: [
            {% for month in monthly_sales %}
                '{{ month.month }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{% trans "عدد الفواتير" %}',
            data: [
                {% for month in monthly_sales %}
                    {{ month.sales_count }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: '{% trans "قيمة المبيعات" %}',
            data: [
                {% for month in monthly_sales %}
                    {{ month.sales_amount }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(40, 167, 69)',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: false,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                beginAtZero: true,
                title: {
                    display: true,
                    text: '{% trans "عدد الفواتير" %}'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                beginAtZero: true,
                title: {
                    display: true,
                    text: '{% trans "قيمة المبيعات (أوقية)" %}'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// Payment Status Chart
const paymentCtx = document.getElementById('paymentChart').getContext('2d');
const paymentChart = new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: ['{% trans "مدفوعة" %}', '{% trans "في الانتظار" %}', '{% trans "جزئي" %}', '{% trans "متأخرة" %}'],
        datasets: [{
            data: [
                {{ paid_invoices }},
                {{ pending_invoices }},
                {% for stat in payment_stats %}
                    {% if stat.payment_status == 'partial' %}{{ stat.count }}{% endif %}
                {% empty %}0{% endfor %},
                {% for stat in payment_stats %}
                    {% if stat.payment_status == 'overdue' %}{{ stat.count }}{% endif %}
                {% empty %}0{% endfor %}
            ],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#17a2b8',
                '#dc3545'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});

// Export functions
function exportToExcel() {
    alert('{% trans "ميزة التصدير إلى Excel ستكون متاحة قريباً" %}');
}

function exportToPDF() {
    alert('{% trans "ميزة التصدير إلى PDF ستكون متاحة قريباً" %}');
}
</script>
{% endblock %}
