{% load i18n %}
{% load translation_extras %}

<!-- Lightning Fast Language Switcher - Pure HTML -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i> 
        {% get_current_language_code as current_lang %}
        {% if current_lang == 'ar' %}العربية{% else %}Français{% endif %}
    </a>
    <ul class="dropdown-menu">
        {% get_current_language_code as current_lang %}
        {% if current_lang == 'ar' %}
            <li>
                <a href="/set-language/?language=fr" class="dropdown-item lightning-switch">
                    <i class="fas fa-language"></i> Français
                </a>
            </li>
        {% else %}
            <li>
                <a href="/set-language/?language=ar" class="dropdown-item lightning-switch">
                    <i class="fas fa-language"></i> العربية
                </a>
            </li>
        {% endif %}
    </ul>
</li>

<style>
.lightning-switch {
    transition: all 0.1s ease !important;
}
.lightning-switch:hover {
    background-color: #f8f9fa !important;
    transform: translateX(2px);
}
.language-switcher .dropdown-toggle {
    transition: all 0.1s ease;
}
.language-switcher .dropdown-toggle:hover {
    background-color: rgba(255,255,255,0.1);
}
</style>
