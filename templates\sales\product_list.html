{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "قائمة المنتجات" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-box"></i> {% trans "قائمة المنتجات" %}
            </h1>
            <a href="{% url 'sales:product_add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {% trans "إضافة منتج جديد" %}
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <input type="text" name="search" class="form-control me-2" 
                   placeholder="{% trans 'البحث في المنتجات...' %}" 
                   value="{{ request.GET.search }}">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <select name="category" class="form-control me-2">
                <option value="">{% trans "جميع الفئات" %}</option>
                {% for category in categories %}
                    <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                {% endfor %}
            </select>
            <button type="submit" class="btn btn-outline-secondary">
                <i class="fas fa-filter"></i>
            </button>
        </form>
    </div>
</div>

<!-- Products Grid -->
<div class="row">
    {% if products %}
        {% for product in products %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                {% if product.image %}
                    <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                {% endif %}
                
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ product.name }}</h5>
                    <p class="card-text text-muted small">{{ product.category.name }}</p>
                    {% if product.description %}
                        <p class="card-text">{{ product.description|truncatewords:15 }}</p>
                    {% endif %}
                    
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="h5 text-primary mb-0">{{ product.price|floatformat:2 }} {% trans "أوقية" %}</span>
                            {% if product.is_in_stock %}
                                <span class="badge bg-success">{% trans "متوفر" %} ({{ product.stock_quantity }})</span>
                            {% else %}
                                <span class="badge bg-danger">{% trans "غير متوفر" %}</span>
                            {% endif %}
                        </div>
                        
                        <!-- Barcode Display -->
                        {% if product.barcode %}
                            <div class="text-center mb-2">
                                <small class="text-muted">{% trans "الباركود" %}: {{ product.barcode }}</small>
                            </div>
                        {% endif %}

                        <div class="btn-group w-100" role="group">
                            <a href="{% url 'sales:product_detail' product.pk %}"
                               class="btn btn-outline-info btn-sm" title="{% trans 'عرض' %}">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'sales:product_barcode' product.pk %}"
                               class="btn btn-outline-success btn-sm" title="{% trans 'الباركود' %}">
                                <i class="fas fa-barcode"></i>
                            </a>
                            <a href="{% url 'sales:product_edit' product.pk %}"
                               class="btn btn-outline-warning btn-sm" title="{% trans 'تعديل' %}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'sales:product_delete' product.pk %}"
                               class="btn btn-outline-danger btn-sm delete-confirm" title="{% trans 'حذف' %}">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "لا توجد منتجات حتى الآن" %}</h5>
                <p class="text-muted">{% trans "ابدأ بإضافة منتج جديد" %}</p>
                <a href="{% url 'sales:product_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {% trans "إضافة منتج جديد" %}
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if is_paginated %}
    <nav aria-label="{% trans 'تنقل الصفحات' %}">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">
                        {% trans "الأولى" %}
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">
                        {% trans "السابقة" %}
                    </a>
                </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">
                    {% trans "صفحة" %} {{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}
                </span>
            </li>

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">
                        {% trans "التالية" %}
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">
                        {% trans "الأخيرة" %}
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}
{% endblock %}
