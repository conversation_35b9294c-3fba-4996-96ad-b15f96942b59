"""
Enhanced Translation Middleware for Real-time Translation
Provides instant translation without .mo files
"""

from django.utils import translation
from django.conf import settings
from django.http import JsonResponse
from sales.translation_engine import TranslationEngine
import json
import re

class EnhancedTranslationMiddleware:
    """
    Enhanced middleware for real-time translation
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Get language from session or URL parameter
        language = self.get_language_from_request(request)
        
        # Validate and set language
        if language in ['ar', 'fr']:
            translation.activate(language)
            request.LANGUAGE_CODE = language
            request.current_language = language
            
            # Set direction
            request.direction = 'rtl' if language == 'ar' else 'ltr'
            
            # Store in session
            request.session['django_language'] = language
        else:
            # Default language
            language = settings.LANGUAGE_CODE
            translation.activate(language)
            request.LANGUAGE_CODE = language
            request.current_language = language
            request.direction = 'rtl' if language == 'ar' else 'ltr'
        
        # Process response
        response = self.get_response(request)
        
        # Add language headers
        response['X-Current-Language'] = language
        response['Content-Language'] = language
        response['X-Text-Direction'] = request.direction
        
        # Auto-translate response content if needed
        if hasattr(request, 'auto_translate') and request.auto_translate:
            response = self.translate_response_content(response, language)
        
        return response
    
    def get_language_from_request(self, request):
        """
        Get language from various sources
        """
        # 1. URL parameter (highest priority)
        if 'language' in request.GET:
            return request.GET.get('language')
        
        # 2. POST data (for language switching)
        if request.method == 'POST' and 'language' in request.POST:
            return request.POST.get('language')
        
        # 3. Session
        if 'django_language' in request.session:
            return request.session.get('django_language')
        
        # 4. Accept-Language header
        if hasattr(request, 'META') and 'HTTP_ACCEPT_LANGUAGE' in request.META:
            accept_lang = request.META['HTTP_ACCEPT_LANGUAGE']
            if 'fr' in accept_lang.lower():
                return 'fr'
            elif 'ar' in accept_lang.lower():
                return 'ar'
        
        # 5. Default
        return settings.LANGUAGE_CODE
    
    def translate_response_content(self, response, target_language):
        """
        Translate response content if it's HTML
        """
        if (response.get('Content-Type', '').startswith('text/html') and 
            target_language != settings.LANGUAGE_CODE):
            
            try:
                content = response.content.decode('utf-8')
                translated_content = TranslationEngine.translate_page_content(
                    content, target_language
                )
                response.content = translated_content.encode('utf-8')
                response['Content-Length'] = str(len(response.content))
            except Exception as e:
                # Log error but don't break the response
                pass
        
        return response

class AdminTranslationMiddleware:
    """
    Specialized middleware for Django Admin translation
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Only process admin requests
        if request.path.startswith('/admin/'):
            # Get language preference
            language = request.session.get('django_language', settings.LANGUAGE_CODE)
            
            # Check for language switch
            if 'language' in request.GET:
                new_language = request.GET.get('language')
                if new_language in ['ar', 'fr']:
                    language = new_language
                    request.session['django_language'] = language
            
            # Activate language
            translation.activate(language)
            request.LANGUAGE_CODE = language
            request.current_language = language
            
            # Set direction and other attributes
            if language == 'ar':
                request.direction = 'rtl'
                request.text_align = 'right'
                request.float_direction = 'right'
            else:
                request.direction = 'ltr'
                request.text_align = 'left'
                request.float_direction = 'left'
        
        response = self.get_response(request)
        
        # Add admin-specific headers
        if request.path.startswith('/admin/'):
            response['X-Admin-Language'] = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
            response['X-Admin-Direction'] = getattr(request, 'direction', 'ltr')
        
        return response

class TranslationAPIMiddleware:
    """
    Middleware to provide translation API endpoints
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Handle translation API requests
        if request.path == '/api/translate/':
            return self.handle_translation_api(request)
        
        if request.path == '/api/translations/':
            return self.handle_translations_list(request)
        
        return self.get_response(request)
    
    def handle_translation_api(self, request):
        """
        Handle single translation requests
        """
        if request.method == 'POST':
            try:
                data = json.loads(request.body)
                text = data.get('text', '')
                target_language = data.get('language', 'fr')
                
                translated = TranslationEngine.translate(text, target_language)
                
                return JsonResponse({
                    'success': True,
                    'original': text,
                    'translated': translated,
                    'language': target_language
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)
        
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    def handle_translations_list(self, request):
        """
        Handle bulk translation requests
        """
        if request.method == 'GET':
            language = request.GET.get('language', 'fr')
            translations = TranslationEngine.get_all_translations(language)
            
            return JsonResponse({
                'success': True,
                'language': language,
                'translations': translations,
                'count': len(translations)
            })
        
        return JsonResponse({'error': 'Method not allowed'}, status=405)

class SmartTranslationMiddleware:
    """
    Smart middleware that combines all translation features
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.enhanced_middleware = EnhancedTranslationMiddleware(get_response)
        self.admin_middleware = AdminTranslationMiddleware(get_response)
        self.api_middleware = TranslationAPIMiddleware(get_response)
    
    def __call__(self, request):
        # Route to appropriate middleware
        if request.path.startswith('/api/translate'):
            return self.api_middleware(request)
        elif request.path.startswith('/admin/'):
            return self.admin_middleware(request)
        else:
            return self.enhanced_middleware(request)

# Utility functions for templates
def get_current_language_info(request):
    """
    Get comprehensive language information
    """
    language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
    
    return {
        'code': language,
        'name': TranslationEngine.get_language_name(language),
        'direction': TranslationEngine.get_language_direction(language),
        'is_rtl': language == 'ar',
        'is_ltr': language != 'ar',
    }

def translate_for_request(request, text):
    """
    Translate text based on request language
    """
    language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
    if language == settings.LANGUAGE_CODE:
        return text
    return TranslationEngine.translate(text, language)
