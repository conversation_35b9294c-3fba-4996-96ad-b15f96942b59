#!/usr/bin/env python
"""Direct fix for missing tables using Django ORM"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
django.setup()

from django.db import connection

def create_tables_directly():
    """Create tables directly using Django connection"""
    try:
        print("🔧 إنشاء الجداول مباشرة...")
        
        with connection.cursor() as cursor:
            # Create supplier table
            print("📋 إنشاء جدول الموردين...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales_supplier (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    email VARCHAR(254) NULL,
                    phone VARCHAR(20) NULL,
                    address TEXT NULL,
                    contact_person VARCHAR(100) NULL,
                    tax_number VARCHAR(50) NULL,
                    payment_terms VARCHAR(100) NULL,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    date_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # Create purchase invoice table
            print("📋 إنشاء جدول فواتير الشراء...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales_purchaseinvoice (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_number VARCHAR(50) NOT NULL UNIQUE,
                    supplier_id INT NOT NULL,
                    user_id INT NOT NULL,
                    date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    date_due DATE NOT NULL,
                    payment_status VARCHAR(20) NOT NULL DEFAULT 'pending',
                    notes TEXT NULL,
                    discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
                    tax_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
                    INDEX idx_supplier_id (supplier_id),
                    INDEX idx_user_id (user_id),
                    FOREIGN KEY (supplier_id) REFERENCES sales_supplier(id),
                    FOREIGN KEY (user_id) REFERENCES auth_user(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # Create purchase invoice items table
            print("📋 إنشاء جدول عناصر فواتير الشراء...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales_purchaseinvoiceitem (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    purchase_invoice_id INT NOT NULL,
                    product_id INT NOT NULL,
                    quantity INT NOT NULL,
                    unit_price DECIMAL(10,2) NOT NULL,
                    INDEX idx_purchase_invoice_id (purchase_invoice_id),
                    INDEX idx_product_id (product_id),
                    FOREIGN KEY (purchase_invoice_id) REFERENCES sales_purchaseinvoice(id),
                    FOREIGN KEY (product_id) REFERENCES sales_product(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # Insert sample suppliers
            print("👥 إضافة موردين تجريبيين...")
            cursor.execute("""
                INSERT IGNORE INTO sales_supplier (name, email, phone, contact_person, is_active) VALUES
                ('شركة التوريدات الذهبية', '<EMAIL>', '+222-45-123456', 'أحمد ولد محمد', TRUE),
                ('مؤسسة النور للتجارة', '<EMAIL>', '+222-45-654321', 'فاطمة بنت أحمد', TRUE),
                ('شركة الصحراء للمواد الغذائية', '<EMAIL>', '+222-45-789012', 'محمد ولد عبدالله', TRUE)
            """)
            
            # Update migrations table
            print("📝 تحديث جدول migrations...")
            cursor.execute("""
                INSERT IGNORE INTO django_migrations (app, name, applied) VALUES 
                ('sales', '0003_add_suppliers_and_purchases', NOW())
            """)
            
            # Verify tables
            cursor.execute("SHOW TABLES LIKE 'sales_%'")
            tables = cursor.fetchall()
            
            print("\n✅ تم إنشاء الجداول بنجاح:")
            for table in tables:
                print(f"  ✅ {table[0]}")
                
            # Check data
            cursor.execute("SELECT COUNT(*) FROM sales_supplier")
            supplier_count = cursor.fetchone()[0]
            print(f"\n👥 عدد الموردين: {supplier_count}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

if __name__ == '__main__':
    success = create_tables_directly()
    if success:
        print("\n🎉 تم إصلاح المشكلة بنجاح!")
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("python manage.py runserver")
        print("\n🔗 اذهب إلى: http://127.0.0.1:8000/admin/sales/purchaseinvoice/")
    else:
        print("\n❌ فشل في إصلاح المشكلة")
    
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
