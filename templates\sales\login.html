{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المبيعات</title>

    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-card">
                    <div class="login-header">
                        <img src="{% static 'images/logo.jpg' %}" alt="Logo" class="logo">
                        <h3>نظام إدارة المبيعات</h3>
                        <p class="mb-0">تسجيل الدخول إلى حسابك</p>
                    </div>
                    
                    <div class="login-body">
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <p class="mb-0">{{ error }}</p>
                                    {% endfor %}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <form method="post">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> اسم المستخدم
                                </label>
                                <input type="text" class="form-control" id="{{ form.username.id_for_label }}"
                                       name="{{ form.username.name }}" value="{{ form.username.value|default:'' }}" required>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.password.id_for_label }}" class="form-label">
                                    <i class="fas fa-lock"></i> كلمة المرور
                                </label>
                                <input type="password" class="form-control" id="{{ form.password.id_for_label }}"
                                       name="{{ form.password.name }}" required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                                </button>
                            </div>
                        </form>
                        

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
