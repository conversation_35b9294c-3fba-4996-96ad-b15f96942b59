/**
 * Admin Translation System
 * نظام ترجمة إدارة الموقع
 */

// Admin translations dictionary
const adminTranslations = {
    'ar': {
        // Main admin interface
        'Django administration': 'إدارة الموقع',
        'Django site admin': 'إدارة الموقع',
        'Site administration': 'إدارة الموقع',
        'Administration': 'الإدارة',
        
        // Navigation
        'Home': 'الرئيسية',
        'Add': 'إضافة',
        'Change': 'تعديل',
        'Delete': 'حذف',
        'View': 'عرض',
        'Save': 'حفظ',
        'Save and continue editing': 'حفظ ومتابعة التعديل',
        'Save and add another': 'حفظ وإضافة آخر',
        'Cancel': 'إلغاء',
        'Search': 'بحث',
        'Filter': 'تصفية',
        'All': 'الكل',
        'Yes': 'نعم',
        'No': 'لا',
        'Today': 'اليوم',
        'Now': 'الآن',
        'Clear': 'مسح',
        'Choose': 'اختيار',
        'Remove': 'إزالة',
        'Show': 'عرض',
        'Hide': 'إخفاء',
        
        // User interface
        'Recent actions': 'الإجراءات الأخيرة',
        'My actions': 'إجراءاتي',
        'None available': 'غير متوفر',
        'Unknown content': 'محتوى غير معروف',
        'Log out': 'تسجيل الخروج',
        'Welcome': 'مرحباً',
        'View site': 'عرض الموقع',
        'Documentation': 'التوثيق',
        'Change password': 'تغيير كلمة المرور',
        
        // App and model names
        'SALES': 'نظام إدارة المبيعات',
        'نظام إدارة المبيعات': 'نظام إدارة المبيعات',
        'العملاء': 'العملاء',
        'المنتجات': 'المنتجات',
        'الفواتير': 'الفواتير',
        'التقارير': 'التقارير',
        'الموردين': 'الموردين',
        'الفئات': 'الفئات',
        'فواتير الشراء': 'فواتير الشراء',
        'المصادقة والتفويض': 'المصادقة والتفويض',
        'المجموعات': 'المجموعات',
        'المستخدمون': 'المستخدمون',
        
        // English to Arabic translations
        'Customers': 'العملاء',
        'Products': 'المنتجات',
        'Invoices': 'الفواتير',
        'Categories': 'الفئات',
        'Suppliers': 'الموردين',
        'Purchase invoices': 'فواتير الشراء',
        'Groups': 'المجموعات',
        'Users': 'المستخدمون',
        'Authentication and Authorization': 'المصادقة والتفويض',
        'AUTHENTICATION AND AUTHORIZATION': 'المصادقة والتفويض',
        
        // Form fields
        'Name': 'الاسم',
        'Email': 'البريد الإلكتروني',
        'Phone': 'رقم الهاتف',
        'Address': 'العنوان',
        'Description': 'الوصف',
        'Price': 'السعر',
        'Quantity': 'الكمية',
        'Date': 'التاريخ',
        'Status': 'الحالة',
        'Active': 'نشط',
        'Inactive': 'غير نشط',
        
        // Actions
        'Add customer': 'إضافة عميل',
        'Add product': 'إضافة منتج',
        'Add invoice': 'إضافة فاتورة',
        'Add category': 'إضافة فئة',
        'Add supplier': 'إضافة مورد',
        'Change customer': 'تعديل عميل',
        'Change product': 'تعديل منتج',
        'Change invoice': 'تعديل فاتورة',
        'Delete customer': 'حذف عميل',
        'Delete product': 'حذف منتج',
        'Delete invoice': 'حذف فاتورة',
        
        // Messages
        'Successfully added': 'تم الإضافة بنجاح',
        'Successfully changed': 'تم التعديل بنجاح',
        'Successfully deleted': 'تم الحذف بنجاح',
        'Are you sure?': 'هل أنت متأكد؟',
        'This action cannot be undone': 'لا يمكن التراجع عن هذا الإجراء',
        
        // Pagination
        'Previous': 'السابق',
        'Next': 'التالي',
        'First': 'الأول',
        'Last': 'الأخير',
        'Page': 'صفحة',
        'of': 'من',
        'Show all': 'عرض الكل',
        
        // Filters
        'By date': 'حسب التاريخ',
        'By status': 'حسب الحالة',
        'By category': 'حسب الفئة',
        'By user': 'حسب المستخدم',
        'Any date': 'أي تاريخ',
        'Past 7 days': 'آخر 7 أيام',
        'This month': 'هذا الشهر',
        'This year': 'هذا العام'
    },
    
    'fr': {
        // Main admin interface
        'Django administration': 'Administration Django',
        'Django site admin': 'Administration du site Django',
        'Site administration': 'Administration du site',
        'Administration': 'Administration',
        
        // Navigation
        'Home': 'Accueil',
        'Add': 'Ajouter',
        'Change': 'Modifier',
        'Delete': 'Supprimer',
        'View': 'Voir',
        'Save': 'Enregistrer',
        'Save and continue editing': 'Enregistrer et continuer les modifications',
        'Save and add another': 'Enregistrer et ajouter un autre',
        'Cancel': 'Annuler',
        'Search': 'Rechercher',
        'Filter': 'Filtrer',
        'All': 'Tous',
        'Yes': 'Oui',
        'No': 'Non',
        'Today': 'Aujourd\'hui',
        'Now': 'Maintenant',
        'Clear': 'Effacer',
        'Choose': 'Choisir',
        'Remove': 'Supprimer',
        'Show': 'Afficher',
        'Hide': 'Masquer',
        
        // User interface
        'Recent actions': 'Actions récentes',
        'My actions': 'Mes actions',
        'None available': 'Aucun disponible',
        'Unknown content': 'Contenu inconnu',
        'Log out': 'Se déconnecter',
        'Welcome': 'Bienvenue',
        'View site': 'Voir le site',
        'Documentation': 'Documentation',
        'Change password': 'Changer le mot de passe',
        
        // App and model names
        'SALES': 'SYSTÈME DE GESTION DES VENTES',
        'نظام إدارة المبيعات': 'Système de Gestion des Ventes',
        'العملاء': 'Clients',
        'المنتجات': 'Produits',
        'الفواتير': 'Factures',
        'التقارير': 'Rapports',
        'الموردين': 'Fournisseurs',
        'الفئات': 'Catégories',
        'فواتير الشراء': 'Factures d\'Achat',
        'المصادقة والتفويض': 'Authentification et Autorisation',
        'المجموعات': 'Groupes',
        'المستخدمون': 'Utilisateurs',
        
        // English to French translations
        'Customers': 'Clients',
        'Products': 'Produits',
        'Invoices': 'Factures',
        'Categories': 'Catégories',
        'Suppliers': 'Fournisseurs',
        'Purchase invoices': 'Factures d\'Achat',
        'Groups': 'Groupes',
        'Users': 'Utilisateurs',
        'Authentication and Authorization': 'Authentification et Autorisation',
        'AUTHENTICATION AND AUTHORIZATION': 'AUTHENTIFICATION ET AUTORISATION',
        
        // Form fields
        'Name': 'Nom',
        'Email': 'Email',
        'Phone': 'Téléphone',
        'Address': 'Adresse',
        'Description': 'Description',
        'Price': 'Prix',
        'Quantity': 'Quantité',
        'Date': 'Date',
        'Status': 'Statut',
        'Active': 'Actif',
        'Inactive': 'Inactif',
        
        // Actions
        'Add customer': 'Ajouter un client',
        'Add product': 'Ajouter un produit',
        'Add invoice': 'Ajouter une facture',
        'Add category': 'Ajouter une catégorie',
        'Add supplier': 'Ajouter un fournisseur',
        'Change customer': 'Modifier le client',
        'Change product': 'Modifier le produit',
        'Change invoice': 'Modifier la facture',
        'Delete customer': 'Supprimer le client',
        'Delete product': 'Supprimer le produit',
        'Delete invoice': 'Supprimer la facture',
        
        // Messages
        'Successfully added': 'Ajouté avec succès',
        'Successfully changed': 'Modifié avec succès',
        'Successfully deleted': 'Supprimé avec succès',
        'Are you sure?': 'Êtes-vous sûr?',
        'This action cannot be undone': 'Cette action ne peut pas être annulée',
        
        // Pagination
        'Previous': 'Précédent',
        'Next': 'Suivant',
        'First': 'Premier',
        'Last': 'Dernier',
        'Page': 'Page',
        'of': 'de',
        'Show all': 'Tout afficher',
        
        // Filters
        'By date': 'Par date',
        'By status': 'Par statut',
        'By category': 'Par catégorie',
        'By user': 'Par utilisateur',
        'Any date': 'Toute date',
        'Past 7 days': '7 derniers jours',
        'This month': 'Ce mois',
        'This year': 'Cette année'
    }
};

// Function to switch admin language
function switchAdminLanguage(language) {
    // Set language in session via AJAX
    fetch('/set-language/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: 'language=' + language
    }).then(() => {
        // Reload the page to apply language changes
        window.location.reload();
    }).catch(error => {
        console.error('Error switching language:', error);
        // Fallback: redirect with language parameter
        window.location.href = window.location.pathname + '?language=' + language;
    });
}

// Function to translate admin page
function translateAdminPage(language) {
    const translations = adminTranslations[language];
    if (!translations) return;

    // Translate common elements
    document.querySelectorAll('*').forEach(function(element) {
        // Skip script and style elements
        if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE') return;
        
        // Translate text nodes
        for (let node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
                const text = node.textContent.trim();
                if (text && translations[text]) {
                    node.textContent = translations[text];
                }
            }
        }
        
        // Translate attributes
        ['title', 'placeholder', 'alt'].forEach(attr => {
            const value = element.getAttribute(attr);
            if (value && translations[value]) {
                element.setAttribute(attr, translations[value]);
            }
        });
    });

    // Translate specific admin elements
    translateAdminSpecificElements(language, translations);
}

// Function to translate admin-specific elements
function translateAdminSpecificElements(language, translations) {
    // Translate app labels
    document.querySelectorAll('.app-label').forEach(function(element) {
        const text = element.textContent.trim();
        if (translations[text]) {
            element.textContent = translations[text];
        }
    });

    // Translate model names
    document.querySelectorAll('.model-label').forEach(function(element) {
        const text = element.textContent.trim();
        if (translations[text]) {
            element.textContent = translations[text];
        }
    });

    // Translate section headers
    document.querySelectorAll('h2, h3, .section h2').forEach(function(element) {
        const text = element.textContent.trim();
        if (translations[text]) {
            element.textContent = translations[text];
        }
    });

    // Translate table headers
    document.querySelectorAll('th').forEach(function(element) {
        const text = element.textContent.trim();
        if (translations[text]) {
            element.textContent = translations[text];
        }
    });

    // Translate links and buttons
    document.querySelectorAll('a, button, input[type="submit"]').forEach(function(element) {
        const text = element.textContent.trim();
        if (text && translations[text]) {
            element.textContent = translations[text];
        }
        
        // Also check value attribute for inputs
        if (element.tagName === 'INPUT' && element.value && translations[element.value]) {
            element.value = translations[element.value];
        }
    });
}

// Initialize translation system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const currentLang = document.documentElement.getAttribute('lang') || 'ar';
    const direction = currentLang === 'ar' ? 'rtl' : 'ltr';
    
    // Set document direction
    document.documentElement.setAttribute('dir', direction);
    document.documentElement.setAttribute('lang', currentLang);
    
    // Apply translations
    if (adminTranslations[currentLang]) {
        translateAdminPage(currentLang);
    }
    
    // Update language switcher
    const currentLanguageSpan = document.getElementById('current-language');
    if (currentLanguageSpan) {
        currentLanguageSpan.textContent = currentLang === 'ar' ? 'العربية' : 'Français';
    }
});
