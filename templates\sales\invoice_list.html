{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "قائمة الفواتير" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-file-invoice"></i> {% trans "قائمة الفواتير" %}
            </h1>
            <a href="{% url 'sales:invoice_add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {% trans "إنشاء فاتورة جديدة" %}
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <input type="text" name="search" class="form-control me-2" 
                   placeholder="{% trans 'البحث في الفواتير...' %}" 
                   value="{{ request.GET.search }}">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <select name="status" class="form-control me-2">
                <option value="">{% trans "جميع الحالات" %}</option>
                <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>
                    {% trans "في الانتظار" %}
                </option>
                <option value="paid" {% if request.GET.status == 'paid' %}selected{% endif %}>
                    {% trans "مدفوعة" %}
                </option>
                <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>
                    {% trans "ملغاة" %}
                </option>
            </select>
            <button type="submit" class="btn btn-outline-secondary">
                <i class="fas fa-filter"></i>
            </button>
        </form>
    </div>
</div>

<!-- Invoices Table -->
<div class="card shadow">
    <div class="card-body">
        {% if invoices %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <th>{% trans "العميل" %}</th>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "المبلغ الإجمالي" %}</th>
                            <th>{% trans "حالة الدفع" %}</th>
                            <th>{% trans "المستخدم" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr>
                            <td>
                                <a href="{% url 'sales:invoice_detail' invoice.pk %}" class="text-decoration-none">
                                    <strong>{{ invoice.invoice_number }}</strong>
                                </a>
                            </td>
                            <td>{{ invoice.customer.name }}</td>
                            <td>{{ invoice.date_created|date:"Y-m-d H:i" }}</td>
                            <td>
                                <span class="text-primary fw-bold">
                                    {{ invoice.total_amount|floatformat:2 }} {% trans "أوقية" %}
                                </span>
                            </td>
                            <td>
                                {% if invoice.payment_status == 'paid' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> {% trans "مدفوعة" %}
                                    </span>
                                {% elif invoice.payment_status == 'pending' %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock"></i> {% trans "في الانتظار" %}
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times"></i> {% trans "ملغاة" %}
                                    </span>
                                {% endif %}
                            </td>
                            <td>{{ invoice.user.username }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'sales:invoice_detail' invoice.pk %}" 
                                       class="btn btn-sm btn-outline-info" title="{% trans 'عرض' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:invoice_print' invoice.pk %}" 
                                       class="btn btn-sm btn-outline-secondary" title="{% trans 'طباعة' %}" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    {% if invoice.payment_status == 'pending' %}
                                        <a href="{% url 'sales:invoice_edit' invoice.pk %}" 
                                           class="btn btn-sm btn-outline-warning" title="{% trans 'تعديل' %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    {% endif %}
                                    <a href="{% url 'sales:invoice_delete' invoice.pk %}" 
                                       class="btn btn-sm btn-outline-danger delete-confirm" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Summary Statistics -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h5>{{ invoices|length }}</h5>
                            <small>{% trans "إجمالي الفواتير" %}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5>
                                {% for invoice in invoices %}
                                    {% if invoice.payment_status == 'paid' %}{{ forloop.counter0|add:1 }}{% endif %}
                                {% empty %}0{% endfor %}
                            </h5>
                            <small>{% trans "مدفوعة" %}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5>
                                {% for invoice in invoices %}
                                    {% if invoice.payment_status == 'pending' %}{{ forloop.counter0|add:1 }}{% endif %}
                                {% empty %}0{% endfor %}
                            </h5>
                            <small>{% trans "في الانتظار" %}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h5>
                                {% widthratio invoices|length 1 1 %}
                                {% for invoice in invoices %}
                                    {% if forloop.first %}{{ invoice.total_amount|floatformat:0 }}{% endif %}
                                {% endfor %}
                            </h5>
                            <small>{% trans "إجمالي المبيعات" %}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="{% trans 'تنقل الصفحات' %}" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">
                                    {% trans "الأولى" %}
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">
                                    {% trans "السابقة" %}
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                {% trans "صفحة" %} {{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">
                                    {% trans "التالية" %}
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">
                                    {% trans "الأخيرة" %}
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "لا توجد فواتير حتى الآن" %}</h5>
                <p class="text-muted">{% trans "ابدأ بإنشاء فاتورة جديدة" %}</p>
                <a href="{% url 'sales:invoice_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {% trans "إنشاء فاتورة جديدة" %}
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh every 30 seconds for real-time updates
    setTimeout(function() {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}
