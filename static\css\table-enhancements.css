/* Enhanced Table Styling for Better Text Visibility */
/* تحسينات الجداول لوضوح أفضل للنصوص */

/* Global table text improvements */
.table-enhanced {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    background-color: #ffffff;
}

/* Enhanced header styling */
.table-enhanced thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff !important;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1.2px;
    padding: 20px 16px;
    border: none;
    text-align: center;
    position: relative;
}

.table-enhanced thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255,255,255,0.3);
}

/* Enhanced body cell styling */
.table-enhanced tbody td {
    background-color: #ffffff !important;
    color: #2c3e50 !important;
    font-weight: 600;
    font-size: 15px;
    padding: 16px;
    border-bottom: 1px solid #e8ecf0;
    text-align: center;
    vertical-align: middle;
    transition: all 0.3s ease;
}

/* Alternating row colors */
.table-enhanced tbody tr:nth-child(even) td {
    background-color: #f8f9fb !important;
}

/* Hover effects */
.table-enhanced tbody tr:hover td {
    background-color: #e3f2fd !important;
    color: #1565c0 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Specific data type styling */
.table-enhanced .data-number {
    color: #e74c3c !important;
    font-weight: 700;
    font-size: 16px;
    font-family: 'Courier New', monospace;
}

.table-enhanced .data-price {
    color: #27ae60 !important;
    font-weight: 700;
    font-size: 16px;
}

.table-enhanced .data-text {
    color: #8e44ad !important;
    font-weight: 600;
}

.table-enhanced .data-code {
    color: #3498db !important;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

/* Dark theme variant */
.table-enhanced.dark {
    background-color: #2c3e50;
}

.table-enhanced.dark thead th {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    color: #ecf0f1 !important;
}

.table-enhanced.dark tbody td {
    background-color: #34495e !important;
    color: #ecf0f1 !important;
    border-bottom: 1px solid #4a6741;
}

.table-enhanced.dark tbody tr:nth-child(even) td {
    background-color: #3c5a78 !important;
}

.table-enhanced.dark tbody tr:hover td {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
}

/* RTL Support */
[dir="rtl"] .table-enhanced th,
[dir="rtl"] .table-enhanced td {
    text-align: right;
}

[dir="ltr"] .table-enhanced th,
[dir="ltr"] .table-enhanced td {
    text-align: left;
}

/* Responsive design */
@media (max-width: 768px) {
    .table-enhanced {
        font-size: 12px;
    }
    
    .table-enhanced thead th {
        padding: 12px 8px;
        font-size: 10px;
    }
    
    .table-enhanced tbody td {
        padding: 10px 6px;
        font-size: 13px;
    }
}

/* Print styles */
@media print {
    .table-enhanced {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .table-enhanced thead th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .table-enhanced tbody td {
        color: #000 !important;
        background: #fff !important;
    }
}

/* Animation for loading states */
.table-enhanced.loading {
    opacity: 0.7;
    pointer-events: none;
}

.table-enhanced.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Status indicators */
.table-enhanced .status-active {
    color: #27ae60 !important;
    font-weight: 700;
}

.table-enhanced .status-inactive {
    color: #e74c3c !important;
    font-weight: 700;
}

.table-enhanced .status-pending {
    color: #f39c12 !important;
    font-weight: 700;
}

/* Badge styling within tables */
.table-enhanced .badge {
    font-size: 11px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Action buttons in tables */
.table-enhanced .btn-group .btn {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 4px;
    margin: 0 2px;
}

/* Sortable column indicators */
.table-enhanced .sortable {
    cursor: pointer;
    position: relative;
}

.table-enhanced .sortable:hover {
    background-color: rgba(255,255,255,0.1);
}

.table-enhanced .sortable::after {
    content: '⇅';
    position: absolute;
    right: 8px;
    opacity: 0.5;
}

.table-enhanced .sortable.asc::after {
    content: '↑';
    opacity: 1;
}

.table-enhanced .sortable.desc::after {
    content: '↓';
    opacity: 1;
}
