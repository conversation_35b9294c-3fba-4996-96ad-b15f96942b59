{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "قائمة العملاء" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-users"></i> {% trans "قائمة العملاء" %}
            </h1>
            <a href="{% url 'sales:customer_add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {% trans "إضافة عميل جديد" %}
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <input type="text" name="search" class="form-control me-2" 
                   placeholder="{% trans 'البحث في العملاء...' %}" 
                   value="{{ request.GET.search }}">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card shadow">
    <div class="card-body">
        {% if customers %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "الاسم" %}</th>
                            <th>{% trans "البريد الإلكتروني" %}</th>
                            <th>{% trans "رقم الهاتف" %}</th>
                            <th>{% trans "تاريخ الإنشاء" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>
                                <a href="{% url 'sales:customer_detail' customer.pk %}" class="text-decoration-none">
                                    <strong>{{ customer.name }}</strong>
                                </a>
                            </td>
                            <td>{{ customer.email|default:"-" }}</td>
                            <td>{{ customer.phone|default:"-" }}</td>
                            <td>{{ customer.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'sales:customer_detail' customer.pk %}" 
                                       class="btn btn-sm btn-outline-info" title="{% trans 'عرض' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:customer_edit' customer.pk %}" 
                                       class="btn btn-sm btn-outline-warning" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'sales:customer_delete' customer.pk %}" 
                                       class="btn btn-sm btn-outline-danger delete-confirm" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="{% trans 'تنقل الصفحات' %}">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                                    {% trans "الأولى" %}
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                                    {% trans "السابقة" %}
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                {% trans "صفحة" %} {{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                                    {% trans "التالية" %}
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                                    {% trans "الأخيرة" %}
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "لا توجد عملاء حتى الآن" %}</h5>
                <p class="text-muted">{% trans "ابدأ بإضافة عميل جديد" %}</p>
                <a href="{% url 'sales:customer_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {% trans "إضافة عميل جديد" %}
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
