{% load static %}
{% load translation_extras %}

<!-- Enhanced Language Switcher with Both Buttons Always Visible -->
<div class="enhanced-lang-switcher" id="enhanced-switcher">
    <div class="switcher-container">
        <!-- Arabic Button -->
        <div class="lang-btn {% if request.LANGUAGE_CODE == 'ar' %}active{% endif %}" 
             data-lang="ar" data-name="العربية" onclick="switchToLanguage('ar', 'العربية')">
            <div class="lang-flag">🇲🇷</div>
            <div class="lang-text">
                <span class="lang-name">العربية</span>
                <span class="lang-code">AR</span>
            </div>
            {% if request.LANGUAGE_CODE == 'ar' %}
                <div class="active-indicator">
                    <i class="fas fa-check-circle"></i>
                </div>
            {% endif %}
        </div>
        
        <!-- Separator -->
        <div class="lang-separator">
            <i class="fas fa-exchange-alt"></i>
        </div>
        
        <!-- French Button -->
        <div class="lang-btn {% if request.LANGUAGE_CODE == 'fr' %}active{% endif %}" 
             data-lang="fr" data-name="Français" onclick="switchToLanguage('fr', 'Français')">
            <div class="lang-flag">🇫🇷</div>
            <div class="lang-text">
                <span class="lang-name">Français</span>
                <span class="lang-code">FR</span>
            </div>
            {% if request.LANGUAGE_CODE == 'fr' %}
                <div class="active-indicator">
                    <i class="fas fa-check-circle"></i>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Translation Status -->
    <div class="translation-status-mini" id="translation-status-mini">
        <i class="fas fa-sync fa-spin"></i>
        <span>جاري التبديل...</span>
    </div>
</div>

<style>
.enhanced-lang-switcher {
    position: fixed;
    top: 20px;
    {% if request.direction == 'rtl' %}
        left: 20px;
    {% else %}
        right: 20px;
    {% endif %}
    z-index: 10000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.switcher-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 8px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    border: 1px solid rgba(255,255,255,0.3);
    gap: 5px;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: transparent;
    border: 2px solid transparent;
    position: relative;
    min-width: 100px;
}

.lang-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.lang-btn.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-color: rgba(255,255,255,0.3);
    cursor: default;
}

.lang-btn.active:hover {
    transform: none;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.lang-flag {
    font-size: 20px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.lang-btn.active .lang-flag {
    background: rgba(255,255,255,0.2);
}

.lang-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
}

.lang-name {
    font-weight: 600;
    font-size: 14px;
    line-height: 1;
}

.lang-code {
    font-size: 11px;
    opacity: 0.8;
    font-weight: 500;
    line-height: 1;
}

.lang-separator {
    color: #667eea;
    font-size: 16px;
    margin: 0 5px;
    opacity: 0.7;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.active-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.translation-status-mini {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 10px;
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 8px 15px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    display: none;
    backdrop-filter: blur(10px);
    white-space: nowrap;
}

.translation-status-mini.show {
    display: flex;
    align-items: center;
    gap: 8px;
}

.translation-status-mini.success {
    background: rgba(40, 167, 69, 0.9);
}

.translation-status-mini.error {
    background: rgba(220, 53, 69, 0.9);
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-lang-switcher {
        top: 10px;
        {% if request.direction == 'rtl' %}
            left: 10px;
        {% else %}
            right: 10px;
        {% endif %}
    }
    
    .lang-btn {
        min-width: 80px;
        padding: 8px 12px;
    }
    
    .lang-name {
        font-size: 12px;
    }
    
    .lang-code {
        font-size: 10px;
    }
}

/* Animation for page transition */
.page-translating {
    opacity: 0.8;
    transition: opacity 0.5s ease;
}

.page-translating * {
    pointer-events: none;
}
</style>

<script>
// Enhanced language switching function
function switchToLanguage(language, languageName) {
    // Don't switch if it's already the current language
    if (document.querySelector(`[data-lang="${language}"]`).classList.contains('active')) {
        return;
    }
    
    // Show status
    showMiniStatus('جاري التبديل... / Changement...');
    
    // Add page transition effect
    document.body.classList.add('page-translating');
    
    // Switch language
    fetch('/set-language-no-csrf/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'language=' + encodeURIComponent(language)
    })
    .then(response => {
        if (response.ok) {
            showMiniStatus('تم بنجاح / Succès', 'success');
            
            // Quick content translation preview
            translateVisibleContent(language).then(() => {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            });
        } else {
            throw new Error('Language switch failed');
        }
    })
    .catch(error => {
        console.error('Language switch error:', error);
        showMiniStatus('خطأ / Erreur', 'error');
        setTimeout(() => {
            hideMiniStatus();
            document.body.classList.remove('page-translating');
        }, 2000);
    });
}

// Show mini status
function showMiniStatus(message, type = 'loading') {
    const statusElement = document.getElementById('translation-status-mini');
    if (!statusElement) return;
    
    const textElement = statusElement.querySelector('span');
    const iconElement = statusElement.querySelector('i');
    
    if (textElement) textElement.textContent = message;
    statusElement.className = `translation-status-mini show ${type}`;
    
    if (iconElement) {
        if (type === 'loading') {
            iconElement.className = 'fas fa-sync fa-spin';
        } else if (type === 'success') {
            iconElement.className = 'fas fa-check';
        } else if (type === 'error') {
            iconElement.className = 'fas fa-times';
        }
    }
}

// Hide mini status
function hideMiniStatus() {
    const statusElement = document.getElementById('translation-status-mini');
    if (statusElement) {
        statusElement.classList.remove('show');
    }
}

// Quick content translation for preview
async function translateVisibleContent(targetLanguage) {
    try {
        // Get key elements for quick translation
        const keyElements = document.querySelectorAll('h1, h2, .card-title, .nav-link, .btn');
        const textsToTranslate = [];
        const elementMap = new Map();
        
        keyElements.forEach(element => {
            const text = element.textContent.trim();
            if (text && text.length > 1 && text.length < 50 && !text.match(/^\d+$/)) {
                textsToTranslate.push(text);
                elementMap.set(text, element);
            }
        });
        
        if (textsToTranslate.length > 0) {
            const response = await fetch('/api/batch-translate/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    texts: textsToTranslate.slice(0, 10),
                    target_language: targetLanguage,
                    source_language: targetLanguage === 'ar' ? 'fr' : 'ar'
                })
            });
            
            const data = await response.json();
            
            if (data.success && data.translations) {
                data.translations.forEach((translation, index) => {
                    const element = elementMap.get(translation.original);
                    if (element) {
                        setTimeout(() => {
                            element.style.transition = 'opacity 0.3s ease';
                            element.style.opacity = '0.6';
                            setTimeout(() => {
                                element.textContent = translation.translated;
                                element.style.opacity = '1';
                            }, 150);
                        }, index * 100);
                    }
                });
            }
        }
    } catch (error) {
        console.error('Quick translation error:', error);
    }
}

// Get CSRF token with multiple fallback methods
function getCSRFToken() {
    let token = null;

    // Method 1: From meta tag
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        token = metaToken.getAttribute('content');
        console.log('CSRF from meta tag:', token);
    }

    // Method 2: From hidden input
    if (!token) {
        const inputToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (inputToken) {
            token = inputToken.value;
            console.log('CSRF from input:', token);
        }
    }

    // Method 3: From cookie
    if (!token) {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                token = value;
                console.log('CSRF from cookie:', token);
                break;
            }
        }
    }

    // Method 4: Try to get from Django's built-in function if available
    if (!token && typeof window.django !== 'undefined' && window.django.csrf) {
        token = window.django.csrf.get_token();
        console.log('CSRF from Django:', token);
    }

    console.log('Final CSRF token:', token);
    return token || '';
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            }
        });
        
        btn.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
});
</script>
