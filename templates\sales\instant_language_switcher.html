{% load i18n %}
{% load translation_extras %}

<!-- Instant Language Switcher -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle language-indicator" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i> 
        {% get_current_language_code as current_lang %}
        <span id="current-lang-text">
            {% if current_lang == 'ar' %}العربية{% else %}Français{% endif %}
        </span>
    </a>
    <ul class="dropdown-menu">
        {% get_available_languages as LANGUAGES %}
        {% get_current_language_code as current_lang %}
        {% for lang_code, lang_name in LANGUAGES %}
            {% if lang_code != current_lang %}
                <li>
                    <a href="#" class="dropdown-item instant-lang-switch" data-language="{{ lang_code }}" data-name="{{ lang_name }}">
                        <i class="fas fa-language"></i> {{ lang_name }}
                    </a>
                </li>
            {% endif %}
        {% endfor %}
    </ul>
</li>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Instant language switching with AJAX
    document.querySelectorAll('.instant-lang-switch').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const language = this.getAttribute('data-language');
            const langName = this.getAttribute('data-name');
            const currentLangText = document.getElementById('current-lang-text');
            
            // Immediate visual feedback
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
            
            // Update current language display immediately
            currentLangText.textContent = langName;
            
            // Send AJAX request
            fetch('/set-language/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: 'language=' + encodeURIComponent(language)
            })
            .then(response => {
                if (response.ok) {
                    // Reload page to apply translations
                    window.location.reload();
                } else {
                    // Restore on error
                    this.innerHTML = originalText;
                    currentLangText.textContent = language === 'ar' ? 'Français' : 'العربية';
                }
            })
            .catch(error => {
                console.error('Language switch error:', error);
                // Fallback: reload page anyway
                window.location.reload();
            });
        });
    });
});
</script>

<style>
.instant-lang-switch {
    transition: all 0.2s ease;
}
.instant-lang-switch:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}
.language-indicator {
    transition: all 0.3s ease;
}
</style>
