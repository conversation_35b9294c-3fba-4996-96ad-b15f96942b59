# 🔧 حل مشكلة Foreign Key Constraint

## ❌ المشكلة:
```
#1005 - Ne peut créer la table `sales_system`.`sales_purchaseinvoiceitem` 
(Errcode: 150 "Foreign key constraint is incorrectly formed")
```

## ✅ الحل المحدث:

### 🥇 الحل الأول - phpMyAdmin (مبسط):

1. **افتح phpMyAdmin**: http://localhost/phpmyadmin/
2. **اختر قاعدة البيانات**: `sales_system`
3. **انقر تبويب SQL**
4. **انسخ محتوى ملف `SIMPLE_FIX.sql`** (بدون Foreign Keys)
5. **الصق والنقر Go**

### 🥈 الحل الثاني - Python محدث:

```cmd
pip install PyMySQL
python simple_fix.py
```

### 🥉 الحل الثالث - إنشاء الجداول خطوة بخطوة:

#### الخطوة 1 - إنشاء جدول الموردين:
```sql
USE sales_system;

CREATE TABLE sales_supplier (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    email VARCHAR(254) NULL,
    phone VARCHAR(20) NULL,
    address TEXT NULL,
    contact_person VARCHAR(100) NULL,
    tax_number VARCHAR(50) NULL,
    payment_terms VARCHAR(100) NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### الخطوة 2 - إنشاء جدول فواتير الشراء:
```sql
CREATE TABLE sales_purchaseinvoice (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_id INT NOT NULL,
    user_id INT NOT NULL,
    date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_due DATE NOT NULL,
    payment_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    notes TEXT NULL,
    discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    tax_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### الخطوة 3 - إنشاء جدول عناصر فواتير الشراء:
```sql
CREATE TABLE sales_purchaseinvoiceitem (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### الخطوة 4 - إضافة بيانات تجريبية:
```sql
INSERT INTO sales_supplier (name, email, phone, contact_person, is_active) VALUES
('شركة التوريدات الذهبية', '<EMAIL>', '+222-45-123456', 'أحمد ولد محمد', TRUE),
('مؤسسة النور للتجارة', '<EMAIL>', '+222-45-654321', 'فاطمة بنت أحمد', TRUE),
('شركة الصحراء للمواد الغذائية', '<EMAIL>', '+222-45-789012', 'محمد ولد عبدالله', TRUE);
```

## 🎯 بعد تطبيق أي حل:

1. **تشغيل النظام**:
```cmd
python manage.py runserver
```

2. **اختبار النظام**:
```
http://127.0.0.1:8000/admin/sales/purchaseinvoice/add/
```

## 🔍 التحقق من النجاح:

في phpMyAdmin، نفذ هذا الكود:
```sql
USE sales_system;
SHOW TABLES LIKE 'sales_%';
SELECT COUNT(*) FROM sales_supplier;
```

يجب أن ترى:
- ✅ sales_supplier
- ✅ sales_purchaseinvoice  
- ✅ sales_purchaseinvoiceitem
- ✅ 3 موردين

---

**💡 السبب في المشكلة**: Foreign Key constraints تحتاج إلى أن تكون الجداول المرجعية موجودة أولاً.

**✅ الحل**: إنشاء الجداول بدون Foreign Keys أولاً، ثم إضافتها لاحقاً إذا لزم الأمر.
