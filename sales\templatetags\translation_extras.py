"""
Custom template tags for enhanced translation support
"""
from django import template
from django.utils import translation
from django.conf import settings
from sales.middleware import TranslationFallbackMiddleware

register = template.Library()

@register.simple_tag(takes_context=True)
def trans_fallback(context, text):
    """
    Enhanced translation tag with fallback support and Unicode error handling
    """
    request = context.get('request')
    current_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)

    # Ensure text is properly encoded
    try:
        if isinstance(text, bytes):
            text = text.decode('utf-8', errors='ignore')
        elif not isinstance(text, str):
            text = str(text)
    except (UnicodeDecodeError, UnicodeEncodeError):
        return text  # Return original if encoding fails

    # Use our custom translations directly for better reliability
    if current_language != settings.LANGUAGE_CODE:
        try:
            fallback_translation = TranslationFallbackMiddleware.get_translation(text, current_language)
            if fallback_translation != text:
                return fallback_translation
        except (UnicodeDecodeError, UnicodeEncodeError, Exception):
            pass

    # Return original text if no translation found
    return text

@register.simple_tag(takes_context=True)
def get_language_name(context):
    """Get current language display name"""
    request = context.get('request')
    current_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
    
    language_names = {
        'ar': 'العربية',
        'fr': 'Français',
        'en': 'English'
    }
    
    return language_names.get(current_language, current_language)

@register.simple_tag
def get_available_languages():
    """Get list of available languages"""
    return settings.LANGUAGES

@register.simple_tag(takes_context=True)
def get_current_language_code(context):
    """Get current language code"""
    request = context.get('request')
    if request:
        # Try session first
        language = request.session.get('django_language')
        if language:
            return language
        # Try request attributes
        language = getattr(request, 'current_language', None)
        if language:
            return language
        language = getattr(request, 'LANGUAGE_CODE', None)
        if language:
            return language
    # Default fallback
    return settings.LANGUAGE_CODE

@register.filter
def translate_status(status):
    """Translate status values"""
    translations = {
        'ar': {
            'paid': 'مدفوعة',
            'pending': 'في الانتظار',
            'cancelled': 'ملغاة',
            'active': 'نشط',
            'inactive': 'غير نشط',
        },
        'fr': {
            'paid': 'Payée',
            'pending': 'En Attente',
            'cancelled': 'Annulée',
            'active': 'Actif',
            'inactive': 'Inactif',
        }
    }
    
    current_language = translation.get_language()
    if current_language in translations:
        return translations[current_language].get(status, status)
    
    return status

@register.inclusion_tag('sales/language_switcher.html', takes_context=True)
def language_switcher(context):
    """Render language switcher component"""
    request = context.get('request')
    current_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)

    return {
        'current_language': current_language,
        'available_languages': settings.LANGUAGES,
        'request': request,
    }

@register.simple_tag
def get_translation_dict():
    """Get all translations as JSON for JavaScript"""
    import json

    translations = {
        'ar': {
            # Navigation - French to Arabic
            'Tableau de Bord': 'لوحة التحكم',
            'Clients': 'العملاء',
            'Produits': 'المنتجات',
            'Fournisseurs': 'الموردين',
            'Factures': 'الفواتير',
            'Rapports': 'التقارير',
            'Système de Gestion des Ventes': 'نظام إدارة المبيعات',

            # Navigation - Arabic (keep same)
            'نظام إدارة المبيعات': 'نظام إدارة المبيعات',
            'لوحة التحكم': 'لوحة التحكم',
            'العملاء': 'العملاء',
            'المنتجات': 'المنتجات',
            'الفواتير': 'الفواتير',
            'التقارير': 'التقارير',
            'الموردين': 'الموردين',
            'الفئات': 'الفئات',
            'قائمة العملاء': 'قائمة العملاء',
            'إضافة عميل': 'إضافة عميل',
            'قائمة المنتجات': 'قائمة المنتجات',
            'إضافة منتج جديد': 'إضافة منتج جديد',
            'قائمة الفواتير': 'قائمة الفواتير',
            'إضافة فاتورة جديدة': 'إضافة فاتورة جديدة',
            'فواتير البيع': 'فواتير البيع',
            'فواتير الشراء': 'فواتير الشراء',
            'قائمة فواتير الشراء': 'قائمة فواتير الشراء',
            'إضافة فاتورة شراء': 'إضافة فاتورة شراء',
            'قائمة الموردين': 'قائمة الموردين',
            'إضافة مورد جديد': 'إضافة مورد جديد',
            'إضافة فئة': 'إضافة فئة',
            'التقارير العامة': 'التقارير العامة',
            'التقارير المتقدمة': 'التقارير المتقدمة',
            'لوحة الإدارة': 'لوحة الإدارة',
            'تسجيل الخروج': 'تسجيل الخروج',
            'جميع الحقوق محفوظة': 'جميع الحقوق محفوظة'
        },
        'fr': {
            # Navigation - Arabic to French
            'نظام إدارة المبيعات': 'Système de Gestion des Ventes',
            'لوحة التحكم': 'Tableau de Bord',
            'العملاء': 'Clients',
            'المنتجات': 'Produits',
            'الفواتير': 'Factures',
            'التقارير': 'Rapports',
            'الموردين': 'Fournisseurs',
            'الفئات': 'Catégories',

            # Navigation - French (keep same)
            'Tableau de Bord': 'Tableau de Bord',
            'Clients': 'Clients',
            'Produits': 'Produits',
            'Fournisseurs': 'Fournisseurs',
            'Factures': 'Factures',
            'Rapports': 'Rapports',
            'Système de Gestion des Ventes': 'Système de Gestion des Ventes',
            'قائمة العملاء': 'Liste des Clients',
            'إضافة عميل': 'Ajouter un Client',
            'قائمة المنتجات': 'Liste des Produits',
            'إضافة منتج جديد': 'Ajouter un Nouveau Produit',
            'قائمة الفواتير': 'Liste des Factures',
            'إضافة فاتورة جديدة': 'Ajouter une Nouvelle Facture',
            'فواتير البيع': 'Factures de Vente',
            'فواتير الشراء': 'Factures d\'Achat',
            'قائمة فواتير الشراء': 'Liste des Factures d\'Achat',
            'إضافة فاتورة شراء': 'Ajouter une Facture d\'Achat',
            'قائمة الموردين': 'Liste des Fournisseurs',
            'إضافة مورد جديد': 'Ajouter un Nouveau Fournisseur',
            'إضافة فئة': 'Ajouter une Catégorie',
            'التقارير العامة': 'Rapports Généraux',
            'التقارير المتقدمة': 'Rapports Avancés',
            'لوحة الإدارة': 'Panneau d\'Administration',
            'تسجيل الخروج': 'Déconnexion',
            'جميع الحقوق محفوظة': 'Tous droits réservés'
        }
    }

    return json.dumps(translations)
