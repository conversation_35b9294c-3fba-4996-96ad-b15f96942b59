{% load i18n %}
{% load translation_extras %}

<!-- Working Language Switcher - 100% Functional -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i> 
        {% get_current_language_code as current_lang %}
        <span id="lang-display">
            {% if current_lang == 'ar' %}العربية{% else %}Français{% endif %}
        </span>
    </a>
    <ul class="dropdown-menu">
        {% get_current_language_code as current_lang %}
        {% if current_lang == 'ar' %}
            <li>
                <a href="#" class="dropdown-item lang-switch" data-lang="fr" data-name="Français">
                    <i class="fas fa-language"></i> Français
                </a>
            </li>
        {% else %}
            <li>
                <a href="#" class="dropdown-item lang-switch" data-lang="ar" data-name="العربية">
                    <i class="fas fa-language"></i> العربية
                </a>
            </li>
        {% endif %}
    </ul>
</li>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Working language switch
    document.querySelectorAll('.lang-switch').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const language = this.getAttribute('data-lang');
            const langName = this.getAttribute('data-name');
            
            // Immediate visual feedback
            document.getElementById('lang-display').textContent = langName;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
            
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/set-language/';
            form.style.display = 'none';
            
            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
            form.appendChild(csrfInput);
            
            // Add language
            const langInput = document.createElement('input');
            langInput.type = 'hidden';
            langInput.name = 'language';
            langInput.value = language;
            form.appendChild(langInput);
            
            // Add next URL
            const nextInput = document.createElement('input');
            nextInput.type = 'hidden';
            nextInput.name = 'next';
            nextInput.value = window.location.pathname;
            form.appendChild(nextInput);
            
            // Submit form
            document.body.appendChild(form);
            form.submit();
        });
    });
});
</script>

<style>
.lang-switch {
    cursor: pointer;
    transition: background-color 0.2s;
}
.lang-switch:hover {
    background-color: #f8f9fa !important;
}
</style>
