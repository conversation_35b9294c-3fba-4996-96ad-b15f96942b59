{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "التقارير المتقدمة" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-chart-line"></i> {% trans "التقارير المتقدمة" %}
        </h1>
    </div>
</div>

<!-- Report Type Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow report-filter-card">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-filter"></i> {% trans "اختيار نوع التقرير" %}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" id="reportForm">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="reportType" class="form-label">{% trans "نوع التقرير" %}</label>
                            <select class="form-select" id="reportType" name="type" onchange="toggleDateInputs()">
                                <option value="daily" {% if report_type == 'daily' %}selected{% endif %}>{% trans "يومي" %}</option>
                                <option value="weekly" {% if report_type == 'weekly' %}selected{% endif %}>{% trans "أسبوعي" %}</option>
                                <option value="monthly" {% if report_type == 'monthly' %}selected{% endif %}>{% trans "شهري" %}</option>
                                <option value="custom" {% if report_type == 'custom' %}selected{% endif %}>{% trans "فترة مخصصة" %}</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-3" id="startDateDiv">
                            <label for="startDate" class="form-label">{% trans "من تاريخ" %}</label>
                            <input type="date" class="form-control" id="startDate" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        
                        <div class="col-md-3 mb-3" id="endDateDiv" style="display: none;">
                            <label for="endDate" class="form-label">{% trans "إلى تاريخ" %}</label>
                            <input type="date" class="form-control" id="endDate" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> {% trans "عرض التقرير" %}
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportReport()">
                                <i class="fas fa-download"></i> {% trans "تصدير" %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Report Period Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert report-period-info">
            <i class="fas fa-info-circle"></i>
            <strong>{% trans "فترة التقرير" %}:</strong>
            {{ start_date|date:"d/m/Y" }}
            {% if start_date != end_date %}
                {% trans "إلى" %} {{ end_date|date:"d/m/Y" }}
            {% endif %}
            ({{ report_type|capfirst }})
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow stats-card h-100">
            <div class="card-body text-center">
                <div class="stats-label text-primary">
                    {% trans "إجمالي المبيعات" %}
                </div>
                <div class="stats-value text-gray-800">
                    {{ total_sales|floatformat:2 }}
                </div>
                <small class="text-muted">{% trans "أوقية" %}</small>
                <div class="mt-2">
                    <i class="fas fa-dollar-sign fa-2x text-primary opacity-25"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "عدد الفواتير" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "متوسط قيمة الفاتورة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ avg_invoice|floatformat:2 }} {% trans "أوقية" %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calculator fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "فواتير مدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ paid_invoices }} / {{ total_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
{% if daily_data %}
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-area"></i> {% trans "المبيعات اليومية" %}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="dailySalesChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie"></i> {% trans "نسبة الدفع" %}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="paymentRatioChart" width="400" height="400"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Tables Row -->
<div class="row">
    <!-- Top Products in Period -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-trophy"></i> {% trans "أفضل المنتجات في الفترة" %}
                </h6>
            </div>
            <div class="card-body">
                {% if top_products_period %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{% trans "المنتج" %}</th>
                                    <th class="text-center">{% trans "الكمية المباعة" %}</th>
                                    <th class="text-center">{% trans "الإيرادات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products_period %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <strong>{{ product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ product.category.name }}</small>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-primary">{{ product.total_sold }}</span>
                                    </td>
                                    <td class="text-center">
                                        <small>{{ product.total_revenue|floatformat:2 }} {% trans "أوقية" %}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">{% trans "لا توجد مبيعات في هذه الفترة" %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Top Customers in Period -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users"></i> {% trans "أفضل العملاء في الفترة" %}
                </h6>
            </div>
            <div class="card-body">
                {% if top_customers_period %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{% trans "العميل" %}</th>
                                    <th class="text-center">{% trans "عدد الفواتير" %}</th>
                                    <th class="text-center">{% trans "إجمالي الإنفاق" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in top_customers_period %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <strong>{{ customer.name }}</strong>
                                        {% if customer.phone %}
                                            <br>
                                            <small class="text-muted">{{ customer.phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-success">{{ customer.total_invoices }}</span>
                                    </td>
                                    <td class="text-center">
                                        <small>{{ customer.total_spent|floatformat:2 }} {% trans "أوقية" %}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">{% trans "لا توجد مبيعات في هذه الفترة" %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-tools"></i> {% trans "إجراءات سريعة" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-success w-100" onclick="printReport()">
                            <i class="fas fa-print"></i> {% trans "طباعة التقرير" %}
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-info w-100" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf"></i> {% trans "تصدير PDF" %}
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-warning w-100" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i> {% trans "تصدير Excel" %}
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'sales:reports' %}" class="btn btn-secondary w-100">
                            <i class="fas fa-arrow-left"></i> {% trans "التقارير العامة" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Toggle date inputs based on report type
function toggleDateInputs() {
    const reportType = document.getElementById('reportType').value;
    const startDateDiv = document.getElementById('startDateDiv');
    const endDateDiv = document.getElementById('endDateDiv');

    if (reportType === 'custom') {
        startDateDiv.style.display = 'block';
        endDateDiv.style.display = 'block';
        document.getElementById('startDate').required = true;
        document.getElementById('endDate').required = true;
    } else {
        startDateDiv.style.display = 'block';
        endDateDiv.style.display = 'none';
        document.getElementById('startDate').required = false;
        document.getElementById('endDate').required = false;
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleDateInputs();
});

{% if daily_data %}
// Daily Sales Chart
const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
const dailySalesChart = new Chart(dailySalesCtx, {
    type: 'line',
    data: {
        labels: [
            {% for day in daily_data %}
                '{{ day.date_display }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{% trans "عدد الفواتير" %}',
            data: [
                {% for day in daily_data %}
                    {{ day.invoices_count }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: '{% trans "قيمة المبيعات" %}',
            data: [
                {% for day in daily_data %}
                    {{ day.sales_amount }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(40, 167, 69)',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: false,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                beginAtZero: true,
                title: {
                    display: true,
                    text: '{% trans "عدد الفواتير" %}'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                beginAtZero: true,
                title: {
                    display: true,
                    text: '{% trans "قيمة المبيعات (أوقية)" %}'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// Payment Ratio Chart
const paymentRatioCtx = document.getElementById('paymentRatioChart').getContext('2d');
const paymentRatioChart = new Chart(paymentRatioCtx, {
    type: 'doughnut',
    data: {
        labels: ['{% trans "مدفوعة" %}', '{% trans "في الانتظار" %}'],
        datasets: [{
            data: [{{ paid_invoices }}, {{ pending_invoices }}],
            backgroundColor: [
                '#28a745',
                '#ffc107'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});
{% endif %}

// Export functions
function exportReport() {
    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    let url = '{% url "sales:advanced_reports" %}?type=' + reportType;
    if (startDate) url += '&start_date=' + startDate;
    if (endDate) url += '&end_date=' + endDate;
    url += '&export=true';

    window.open(url, '_blank');
}

function printReport() {
    window.print();
}

function exportToPDF() {
    alert('{% trans "ميزة التصدير إلى PDF ستكون متاحة قريباً" %}');
}

function exportToExcel() {
    alert('{% trans "ميزة التصدير إلى Excel ستكون متاحة قريباً" %}');
}

// Auto-submit form when dates change
document.getElementById('startDate').addEventListener('change', function() {
    if (document.getElementById('reportType').value !== 'custom') {
        document.getElementById('reportForm').submit();
    }
});

document.getElementById('endDate').addEventListener('change', function() {
    if (document.getElementById('reportType').value === 'custom') {
        document.getElementById('reportForm').submit();
    }
});
</script>

<!-- Print Styles -->
<style>
@media print {
    .no-print, .btn, .card-header, .alert {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
        break-inside: avoid;
    }

    .card-body {
        padding: 0.5rem !important;
    }

    .table {
        font-size: 0.8rem;
    }

    .chart-container {
        height: 200px !important;
    }
}
</style>
{% endblock %}
