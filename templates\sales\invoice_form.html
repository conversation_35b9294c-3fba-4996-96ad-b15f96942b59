{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "تعديل الفاتورة" %} - {{ object.invoice_number }}
    {% else %}
        {% trans "إنشاء فاتورة جديدة" %}
    {% endif %}
    - {{ block.super }}
{% endblock %}

{% block extra_css %}
<style>
    .invoice-item-row {
        background-color: #f8f9fa;
    }

    .invoice-item-row:hover {
        background-color: #e9ecef;
    }

    .total-cell {
        font-weight: bold;
        color: #28a745;
    }

    .product-card {
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
    }

    .product-card:hover {
        border-color: #007bff;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 123, 255, 0.075);
        transform: translateY(-1px);
    }

    .search-product-row {
        border-left: none;
    }

    .input-group .form-control {
        border-right: none;
    }

    .input-group .btn {
        border-left: none;
    }

    #productSearchModal .modal-body {
        max-height: 500px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">{% trans "لوحة التحكم" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'sales:invoice_list' %}">{% trans "الفواتير" %}</a></li>
                <li class="breadcrumb-item active">
                    {% if object %}
                        {% trans "تعديل الفاتورة" %}
                    {% else %}
                        {% trans "إنشاء فاتورة جديدة" %}
                    {% endif %}
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice"></i>
                    {% if object %}
                        {% trans "تعديل الفاتورة" %}: {{ object.invoice_number }}
                    {% else %}
                        {% trans "إنشاء فاتورة جديدة" %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="invoice-form" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.customer.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> {{ form.customer.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    {{ form.customer }}
                                    <button type="button" class="btn btn-success" id="add-customer-btn" title="إضافة عميل جديد">
                                        <i class="fas fa-plus"></i> عميل جديد
                                    </button>
                                </div>
                                {% if form.customer.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.customer.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.date_due.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar"></i> {{ form.date_due.label }}
                                </label>
                                {{ form.date_due }}
                                {% if form.date_due.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.date_due.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.payment_status.id_for_label }}" class="form-label">
                                    <i class="fas fa-credit-card"></i> {{ form.payment_status.label }}
                                </label>
                                {{ form.payment_status }}
                                {% if form.payment_status.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.payment_status.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.discount_percentage.id_for_label }}" class="form-label">
                                    <i class="fas fa-percentage"></i> {{ form.discount_percentage.label }}
                                </label>
                                {{ form.discount_percentage }}
                                {% if form.discount_percentage.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.discount_percentage.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.tax_percentage.id_for_label }}" class="form-label">
                                    <i class="fas fa-receipt"></i> {{ form.tax_percentage.label }}
                                </label>
                                {{ form.tax_percentage }}
                                {% if form.tax_percentage.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.tax_percentage.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-list"></i> عناصر الفاتورة
                            </h6>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-info" id="search-product-btn">
                                    <i class="fas fa-search"></i> البحث عن منتج
                                </button>
                                <button type="button" class="btn btn-sm btn-success" id="add-item">
                                    <i class="fas fa-plus"></i> إضافة عنصر
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            {{ formset.management_form }}
                            
                            <div class="table-responsive">
                                <table class="table table-bordered" id="invoice-items">
                                    <thead>
                                        <tr>
                                            <th>{% trans "المنتج" %}</th>
                                            <th>{% trans "الكمية" %}</th>
                                            <th>{% trans "سعر الوحدة" %}</th>
                                            <th>{% trans "الإجمالي" %}</th>
                                            <th>{% trans "إجراءات" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for form in formset %}
                                            <tr class="invoice-item-row">
                                                {% for hidden in form.hidden_fields %}
                                                    {{ hidden }}
                                                {% endfor %}
                                                
                                                <td>
                                                    <div class="input-group">
                                                        {{ form.product }}
                                                        <button type="button" class="btn btn-outline-info btn-sm search-product-row" title="البحث عن منتج">
                                                            <i class="fas fa-search"></i>
                                                        </button>
                                                    </div>
                                                    {% if form.product.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ form.product.errors.0 }}
                                                        </div>
                                                    {% endif %}
                                                </td>
                                                
                                                <td>
                                                    {{ form.quantity }}
                                                    {% if form.quantity.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ form.quantity.errors.0 }}
                                                        </div>
                                                    {% endif %}
                                                </td>
                                                
                                                <td>
                                                    {{ form.unit_price }}
                                                    {% if form.unit_price.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ form.unit_price.errors.0 }}
                                                        </div>
                                                    {% endif %}
                                                </td>
                                                
                                                <td class="total-cell">0.00</td>
                                                
                                                <td>
                                                    {% if not forloop.first %}
                                                        <button type="button" class="btn btn-danger btn-sm remove-item">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    {% endif %}
                                                    {% if form.DELETE %}
                                                        {{ form.DELETE }}
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Totals -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-calculator"></i> {% trans "إجمالي الفاتورة" %}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 offset-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>{% trans "المجموع الفرعي" %}:</strong></td>
                                            <td class="text-end"><span id="subtotal">0.00</span> {% trans "أوقية" %}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "الخصم" %}:</strong></td>
                                            <td class="text-end"><span id="discount-amount">0.00</span> {% trans "أوقية" %}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{% trans "الضريبة" %}:</strong></td>
                                            <td class="text-end"><span id="tax-amount">0.00</span> {% trans "أوقية" %}</td>
                                        </tr>
                                        <tr class="table-primary">
                                            <td><strong>{% trans "الإجمالي النهائي" %}:</strong></td>
                                            <td class="text-end"><strong><span id="total-amount">0.00</span> {% trans "أوقية" %}</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            <i class="fas fa-sticky-note"></i> {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.notes.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'sales:invoice_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "إلغاء" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if object %}
                                {% trans "تحديث الفاتورة" %}
                            {% else %}
                                {% trans "حفظ الفاتورة" %}
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize invoice form
    initializeInvoiceForm();

    // Calculate initial totals
    calculateInvoiceTotal();

    // Add Customer Button
    $('#add-customer-btn').on('click', function() {
        var customerWindow = window.open('{% url "sales:customer_add" %}', 'addCustomer', 'width=800,height=600,scrollbars=yes');

        // Listen for customer added
        window.addEventListener('message', function(event) {
            if (event.data.type === 'customerAdded') {
                // Refresh customer dropdown
                refreshCustomerDropdown(event.data.customer);
                customerWindow.close();
            }
        });
    });

    // Search Product Button (main)
    $('#search-product-btn').on('click', function() {
        showProductSearchModal();
    });

    // Search Product Button (row)
    $(document).on('click', '.search-product-row', function() {
        var row = $(this).closest('tr');
        showProductSearchModal(row);
    });
});

// Function to refresh customer dropdown
function refreshCustomerDropdown(newCustomer) {
    var customerSelect = $('#{{ form.customer.id_for_label }}');

    // Add new customer to dropdown
    if (newCustomer) {
        customerSelect.append(new Option(newCustomer.name, newCustomer.id, true, true));
        customerSelect.trigger('change');
    } else {
        // Refresh all options via AJAX
        $.ajax({
            url: '{% url "sales:ajax_customers" %}',
            success: function(data) {
                customerSelect.empty();
                customerSelect.append('<option value="">اختر عميل...</option>');
                $.each(data.customers, function(index, customer) {
                    customerSelect.append(new Option(customer.name, customer.id));
                });
            }
        });
    }
}

// Function to show product search modal
function showProductSearchModal(targetRow) {
    var modal = $(`
        <div class="modal fade" id="productSearchModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">البحث عن منتج</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="productSearchInput" placeholder="ابحث عن منتج...">
                        </div>
                        <div id="productSearchResults">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">جاري البحث...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `);

    $('body').append(modal);
    modal.modal('show');

    // Load all products initially
    loadProducts('', targetRow);

    // Search functionality
    $('#productSearchInput').on('keyup', function() {
        var query = $(this).val();
        loadProducts(query, targetRow);
    });

    // Clean up modal on close
    modal.on('hidden.bs.modal', function() {
        modal.remove();
    });
}

// Function to load products
function loadProducts(query, targetRow) {
    $.ajax({
        url: '{% url "sales:ajax_products" %}',
        data: { 'search': query },
        success: function(data) {
            var resultsHtml = '';

            if (data.products.length > 0) {
                resultsHtml = '<div class="row">';
                $.each(data.products, function(index, product) {
                    resultsHtml += `
                        <div class="col-md-6 mb-3">
                            <div class="card product-card" style="cursor: pointer;" data-product-id="${product.id}" data-product-name="${product.name}" data-product-price="${product.price}">
                                <div class="card-body">
                                    <h6 class="card-title">${product.name}</h6>
                                    <p class="card-text small text-muted">${product.category}</p>
                                    <div class="d-flex justify-content-between">
                                        <span class="text-primary fw-bold">${product.price} أوقية</span>
                                        <span class="badge ${product.stock > 0 ? 'bg-success' : 'bg-danger'}">
                                            ${product.stock > 0 ? 'متوفر (' + product.stock + ')' : 'غير متوفر'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                resultsHtml += '</div>';
            } else {
                resultsHtml = '<div class="text-center text-muted">لا توجد منتجات</div>';
            }

            $('#productSearchResults').html(resultsHtml);

            // Handle product selection
            $('.product-card').on('click', function() {
                var productId = $(this).data('product-id');
                var productName = $(this).data('product-name');
                var productPrice = $(this).data('product-price');

                if (targetRow) {
                    // Set product in specific row
                    targetRow.find('.product-select').val(productId);
                    targetRow.find('.price-input').val(productPrice);
                    calculateRowTotal(targetRow);
                } else {
                    // Add new row with selected product
                    addNewInvoiceItem(productId, productName, productPrice);
                }

                calculateInvoiceTotal();
                $('#productSearchModal').modal('hide');
            });
        }
    });
}

// Function to add new invoice item
function addNewInvoiceItem(productId, productName, productPrice) {
    // This function should add a new row to the invoice items table
    // Implementation depends on your formset structure
    console.log('Adding product:', productId, productName, productPrice);
}
</script>
{% endblock %}
