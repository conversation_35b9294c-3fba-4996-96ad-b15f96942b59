"""
Enhanced Translation Engine for Sales Management System
Provides real-time translation without .mo files
"""

import json
import re
from django.utils import translation
from django.conf import settings

class TranslationEngine:
    """
    Advanced bidirectional translation engine for Arabic-French
    Supports complete translation in both directions
    """

    # Comprehensive bidirectional translation dictionary
    TRANSLATIONS = {
        'ar_to_fr': {
            # System and Navigation
            'نظام إدارة المبيعات': 'Système de Gestion des Ventes',
            'لوحة التحكم': 'Tableau de Bord',
            'العملاء': 'Clients',
            'المنتجات': 'Produits',
            'الفواتير': 'Factures',
            'التقارير': 'Rapports',
            'الموردين': 'Fournisseurs',
            'الفئات': 'Catégories',
            'الإعدادات': 'Paramètres',
            'تسجيل الخروج': 'Déconnexion',
            'تسجيل الدخول': 'Connexion',
            
            # Dashboard Statistics
            'إجمالي العملاء': 'Total des Clients',
            'إجمالي المنتجات': 'Total des Produits',
            'إجمالي الفواتير': 'Total des Factures',
            'الفواتير المعلقة': 'Factures en Attente',
            'الفواتير الأخيرة': 'Factures Récentes',
            'منتجات قليلة المخزون': 'Produits à Stock Faible',
            'إجراءات سريعة': 'Actions Rapides',
            'جميع المنتجات متوفرة في المخزون': 'Tous les produits sont disponibles en stock',
            
            # Actions
            'إضافة': 'Ajouter',
            'تعديل': 'Modifier',
            'حذف': 'Supprimer',
            'عرض': 'Voir',
            'بحث': 'Rechercher',
            'حفظ': 'Enregistrer',
            'إلغاء': 'Annuler',
            'طباعة': 'Imprimer',
            'تحديث': 'Mettre à Jour',
            'إنشاء': 'Créer',
            'تصدير': 'Exporter',
            'استيراد': 'Importer',
            
            # Quick Actions
            'إنشاء فاتورة جديدة': 'Créer une Nouvelle Facture',
            'إضافة عميل جديد': 'Ajouter un Nouveau Client',
            'إضافة منتج جديد': 'Ajouter un Nouveau Produit',
            'عرض التقارير': 'Voir les Rapports',
            'إدارة المخزون': 'Gestion des Stocks',
            'عرض الكل': 'Voir Tout',
            
            # Form Fields
            'الاسم': 'Nom',
            'البريد الإلكتروني': 'Email',
            'رقم الهاتف': 'Téléphone',
            'العنوان': 'Adresse',
            'الوصف': 'Description',
            'السعر': 'Prix',
            'الكمية': 'Quantité',
            'التاريخ': 'Date',
            'الحالة': 'Statut',
            'الملاحظات': 'Notes',
            
            # Product Fields
            'اسم المنتج': 'Nom du Produit',
            'الكمية في المخزن': 'Quantité en Stock',
            'صورة المنتج': 'Image du Produit',
            'الباركود': 'Code-barres',
            'سعر الوحدة': 'Prix Unitaire',
            
            # Invoice Fields
            'رقم الفاتورة': 'Numéro de Facture',
            'العميل': 'Client',
            'تاريخ الاستحقاق': 'Date d\'Échéance',
            'حالة الدفع': 'Statut de Paiement',
            'المبلغ الإجمالي': 'Montant Total',
            'نسبة الخصم': 'Pourcentage de Remise',
            'نسبة الضريبة': 'Pourcentage de Taxe',
            
            # Status Values
            'نشط': 'Actif',
            'غير نشط': 'Inactif',
            'متوفر': 'Disponible',
            'غير متوفر': 'Non Disponible',
            'مدفوعة': 'Payée',
            'في الانتظار': 'En Attente',
            'ملغاة': 'Annulée',
            'مكتملة': 'Complétée',
            
            # Messages
            'تم الحفظ بنجاح': 'Enregistré avec succès',
            'تم التحديث بنجاح': 'Mis à jour avec succès',
            'تم الحذف بنجاح': 'Supprimé avec succès',
            'حدث خطأ': 'Une erreur s\'est produite',
            'لا توجد بيانات': 'Aucune donnée',
            'تأكيد الحذف': 'Confirmer la suppression',
            'هل أنت متأكد؟': 'Êtes-vous sûr ?',
            
            # Empty States
            'لا توجد عملاء حتى الآن': 'Aucun client pour le moment',
            'لا توجد منتجات حتى الآن': 'Aucun produit pour le moment',
            'لا توجد فواتير حتى الآن': 'Aucune facture pour le moment',
            'ابدأ بإضافة عميل جديد': 'Commencez par ajouter un nouveau client',
            'ابدأ بإضافة منتج جديد': 'Commencez par ajouter un nouveau produit',
            'ابدأ بإنشاء فاتورة جديدة': 'Commencez par créer une nouvelle facture',
            
            # Pagination
            'الصفحة': 'Page',
            'من': 'de',
            'إلى': 'à',
            'التالي': 'Suivant',
            'السابق': 'Précédent',
            'الأولى': 'Première',
            'الأخيرة': 'Dernière',
            
            # Currency and Numbers
            'أوقية': 'Ouguiya',
            'MRU': 'MRU',
            'UM': 'UM',
            
            # Reports
            'التقارير المتقدمة': 'Rapports Avancés',
            'تقرير يومي': 'Rapport Quotidien',
            'تقرير أسبوعي': 'Rapport Hebdomadaire',
            'تقرير شهري': 'Rapport Mensuel',
            'فترة مخصصة': 'Période Personnalisée',
            'من تاريخ': 'Date de Début',
            'إلى تاريخ': 'Date de Fin',
            'عرض التقرير': 'Afficher le Rapport',
            'طباعة التقرير': 'Imprimer le Rapport',
            'تصدير PDF': 'Exporter en PDF',
            
            # Time and Dates
            'اليوم': 'Aujourd\'hui',
            'أمس': 'Hier',
            'هذا الأسبوع': 'Cette Semaine',
            'هذا الشهر': 'Ce Mois',
            'هذا العام': 'Cette Année',
            
            # Common Words
            'نعم': 'Oui',
            'لا': 'Non',
            'موافق': 'OK',
            'إغلاق': 'Fermer',
            'فتح': 'Ouvrir',
            'جديد': 'Nouveau',
            'قديم': 'Ancien',
            'كل': 'Tout',
            'بعض': 'Quelques',
            'لا شيء': 'Rien',
        },

        'fr_to_ar': {
            # System and Navigation - French to Arabic
            'Système de Gestion des Ventes': 'نظام إدارة المبيعات',
            'Tableau de Bord': 'لوحة التحكم',
            'Clients': 'العملاء',
            'Produits': 'المنتجات',
            'Factures': 'الفواتير',
            'Rapports': 'التقارير',
            'Fournisseurs': 'الموردين',
            'Catégories': 'الفئات',
            'Paramètres': 'الإعدادات',
            'Déconnexion': 'تسجيل الخروج',
            'Connexion': 'تسجيل الدخول',

            # Dashboard Statistics - French to Arabic
            'Total des Clients': 'إجمالي العملاء',
            'Total des Produits': 'إجمالي المنتجات',
            'Total des Factures': 'إجمالي الفواتير',
            'Factures en Attente': 'الفواتير المعلقة',
            'Factures Récentes': 'الفواتير الأخيرة',
            'Produits à Stock Faible': 'منتجات قليلة المخزون',
            'Actions Rapides': 'إجراءات سريعة',
            'Tous les produits sont disponibles en stock': 'جميع المنتجات متوفرة في المخزون',

            # Actions - French to Arabic
            'Ajouter': 'إضافة',
            'Modifier': 'تعديل',
            'Supprimer': 'حذف',
            'Voir': 'عرض',
            'Rechercher': 'بحث',
            'Enregistrer': 'حفظ',
            'Annuler': 'إلغاء',
            'Imprimer': 'طباعة',
            'Mettre à Jour': 'تحديث',
            'Créer': 'إنشاء',
            'Exporter': 'تصدير',
            'Importer': 'استيراد',

            # Quick Actions - French to Arabic
            'Créer une Nouvelle Facture': 'إنشاء فاتورة جديدة',
            'Ajouter un Nouveau Client': 'إضافة عميل جديد',
            'Ajouter un Nouveau Produit': 'إضافة منتج جديد',
            'Voir les Rapports': 'عرض التقارير',
            'Gestion des Stocks': 'إدارة المخزون',
            'Voir Tout': 'عرض الكل',

            # Form Fields - French to Arabic
            'Nom': 'الاسم',
            'Email': 'البريد الإلكتروني',
            'Téléphone': 'رقم الهاتف',
            'Adresse': 'العنوان',
            'Description': 'الوصف',
            'Prix': 'السعر',
            'Quantité': 'الكمية',
            'Date': 'التاريخ',
            'Statut': 'الحالة',
            'Notes': 'الملاحظات',

            # Product Fields - French to Arabic
            'Nom du Produit': 'اسم المنتج',
            'Quantité en Stock': 'الكمية في المخزن',
            'Image du Produit': 'صورة المنتج',
            'Code-barres': 'الباركود',
            'Prix Unitaire': 'سعر الوحدة',

            # Invoice Fields - French to Arabic
            'Numéro de Facture': 'رقم الفاتورة',
            'Client': 'العميل',
            'Date d\'Échéance': 'تاريخ الاستحقاق',
            'Statut de Paiement': 'حالة الدفع',
            'Montant Total': 'المبلغ الإجمالي',
            'Pourcentage de Remise': 'نسبة الخصم',
            'Pourcentage de Taxe': 'نسبة الضريبة',

            # Status Values - French to Arabic
            'Actif': 'نشط',
            'Inactif': 'غير نشط',
            'Disponible': 'متوفر',
            'Non Disponible': 'غير متوفر',
            'Payée': 'مدفوعة',
            'En Attente': 'في الانتظار',
            'Annulée': 'ملغاة',
            'Complétée': 'مكتملة',

            # Messages - French to Arabic
            'Enregistré avec succès': 'تم الحفظ بنجاح',
            'Mis à jour avec succès': 'تم التحديث بنجاح',
            'Supprimé avec succès': 'تم الحذف بنجاح',
            'Une erreur s\'est produite': 'حدث خطأ',
            'Aucune donnée': 'لا توجد بيانات',
            'Confirmer la suppression': 'تأكيد الحذف',
            'Êtes-vous sûr ?': 'هل أنت متأكد؟',

            # Empty States - French to Arabic
            'Aucun client pour le moment': 'لا توجد عملاء حتى الآن',
            'Aucun produit pour le moment': 'لا توجد منتجات حتى الآن',
            'Aucune facture pour le moment': 'لا توجد فواتير حتى الآن',
            'Commencez par ajouter un nouveau client': 'ابدأ بإضافة عميل جديد',
            'Commencez par ajouter un nouveau produit': 'ابدأ بإضافة منتج جديد',
            'Commencez par créer une nouvelle facture': 'ابدأ بإنشاء فاتورة جديدة',

            # Pagination - French to Arabic
            'Page': 'الصفحة',
            'de': 'من',
            'à': 'إلى',
            'Suivant': 'التالي',
            'Précédent': 'السابق',
            'Première': 'الأولى',
            'Dernière': 'الأخيرة',

            # Currency and Numbers - French to Arabic
            'Ouguiya': 'أوقية',

            # Reports - French to Arabic
            'Rapports Avancés': 'التقارير المتقدمة',
            'Rapport Quotidien': 'تقرير يومي',
            'Rapport Hebdomadaire': 'تقرير أسبوعي',
            'Rapport Mensuel': 'تقرير شهري',
            'Période Personnalisée': 'فترة مخصصة',
            'Date de Début': 'من تاريخ',
            'Date de Fin': 'إلى تاريخ',
            'Afficher le Rapport': 'عرض التقرير',
            'Imprimer le Rapport': 'طباعة التقرير',
            'Exporter en PDF': 'تصدير PDF',

            # Time and Dates - French to Arabic
            'Aujourd\'hui': 'اليوم',
            'Hier': 'أمس',
            'Cette Semaine': 'هذا الأسبوع',
            'Ce Mois': 'هذا الشهر',
            'Cette Année': 'هذا العام',

            # Common Words - French to Arabic
            'Oui': 'نعم',
            'Non': 'لا',
            'OK': 'موافق',
            'Fermer': 'إغلاق',
            'Ouvrir': 'فتح',
            'Nouveau': 'جديد',
            'Ancien': 'قديم',
            'Tout': 'كل',
            'Quelques': 'بعض',
            'Rien': 'لا شيء',
        }
    }
    
    @classmethod
    def translate(cls, text, target_language='fr', source_language='ar'):
        """
        Bidirectional translation between Arabic and French
        """
        if not text:
            return text

        # Clean the text
        text = str(text).strip()

        # Determine translation direction
        if source_language == 'ar' and target_language == 'fr':
            translation_key = 'ar_to_fr'
        elif source_language == 'fr' and target_language == 'ar':
            translation_key = 'fr_to_ar'
        else:
            # Auto-detect language if not specified
            translation_key = cls._detect_language_and_get_key(text, target_language)

        if translation_key not in cls.TRANSLATIONS:
            return text

        translations = cls.TRANSLATIONS[translation_key]

        # Direct translation lookup
        if text in translations:
            return translations[text]

        # Try case-insensitive lookup
        text_lower = text.lower()
        for key, value in translations.items():
            if key.lower() == text_lower:
                return value

        # Try partial matches for compound phrases
        translated_text = text
        for source_text, target_text in translations.items():
            if source_text in translated_text:
                translated_text = translated_text.replace(source_text, target_text)

        # If any translation occurred, return it
        if translated_text != text:
            return translated_text

        # Return original if no translation found
        return text

    @classmethod
    def _detect_language_and_get_key(cls, text, target_language):
        """
        Auto-detect source language and return appropriate translation key
        """
        # Simple language detection based on character sets
        arabic_chars = set('ابتثجحخدذرزسشصضطظعغفقكلمنهوي')
        french_chars = set('abcdefghijklmnopqrstuvwxyzàâäéèêëïîôöùûüÿç')

        text_chars = set(text.lower())

        arabic_score = len(text_chars.intersection(arabic_chars))
        french_score = len(text_chars.intersection(french_chars))

        if arabic_score > french_score:
            # Source is Arabic
            return 'ar_to_fr' if target_language == 'fr' else 'ar_to_fr'
        else:
            # Source is French
            return 'fr_to_ar' if target_language == 'ar' else 'fr_to_ar'
    
    @classmethod
    def translate_multiple(cls, texts, target_language='fr', source_language='ar'):
        """
        Translate multiple texts with bidirectional support
        """
        return [cls.translate(text, target_language, source_language) for text in texts]

    @classmethod
    def get_all_translations(cls, target_language='fr', source_language='ar'):
        """
        Get all translations for a language direction
        """
        if source_language == 'ar' and target_language == 'fr':
            return cls.TRANSLATIONS.get('ar_to_fr', {})
        elif source_language == 'fr' and target_language == 'ar':
            return cls.TRANSLATIONS.get('fr_to_ar', {})
        else:
            # Return both directions
            return {
                'ar_to_fr': cls.TRANSLATIONS.get('ar_to_fr', {}),
                'fr_to_ar': cls.TRANSLATIONS.get('fr_to_ar', {})
            }

    @classmethod
    def translate_page_content(cls, content, target_language='fr', source_language='ar'):
        """
        Translate entire page content with bidirectional support
        """
        if not content:
            return content

        # Determine translation direction
        if source_language == 'ar' and target_language == 'fr':
            translation_key = 'ar_to_fr'
        elif source_language == 'fr' and target_language == 'ar':
            translation_key = 'fr_to_ar'
        else:
            return content

        if translation_key not in cls.TRANSLATIONS:
            return content

        translations = cls.TRANSLATIONS[translation_key]

        # Replace all text with translations
        translated_content = content
        for source_text, target_text in translations.items():
            translated_content = translated_content.replace(source_text, target_text)

        return translated_content

    @classmethod
    def auto_translate(cls, text, target_language=None):
        """
        Auto-detect source language and translate to target
        """
        if not text or not target_language:
            return text

        # Auto-detect source language
        arabic_chars = set('ابتثجحخدذرزسشصضطظعغفقكلمنهوي')
        text_chars = set(text.lower())

        if len(text_chars.intersection(arabic_chars)) > 0:
            # Source is Arabic
            source_language = 'ar'
        else:
            # Source is French
            source_language = 'fr'

        return cls.translate(text, target_language, source_language)
    
    @classmethod
    def get_language_direction(cls, language_code):
        """
        Get text direction for language
        """
        rtl_languages = ['ar', 'he', 'fa', 'ur']
        return 'rtl' if language_code in rtl_languages else 'ltr'
    
    @classmethod
    def get_language_name(cls, language_code):
        """
        Get display name for language
        """
        language_names = {
            'ar': 'العربية',
            'fr': 'Français',
            'en': 'English'
        }
        return language_names.get(language_code, language_code)

    @classmethod
    def get_opposite_language(cls, language_code):
        """
        Get the opposite language for bidirectional translation
        """
        opposite_map = {
            'ar': 'fr',
            'fr': 'ar'
        }
        return opposite_map.get(language_code, 'fr')

    @classmethod
    def translate_to_opposite(cls, text, current_language):
        """
        Translate text to the opposite language
        """
        target_language = cls.get_opposite_language(current_language)
        return cls.translate(text, target_language, current_language)

    @classmethod
    def get_translation_stats(cls):
        """
        Get statistics about available translations
        """
        ar_to_fr_count = len(cls.TRANSLATIONS.get('ar_to_fr', {}))
        fr_to_ar_count = len(cls.TRANSLATIONS.get('fr_to_ar', {}))

        return {
            'ar_to_fr': ar_to_fr_count,
            'fr_to_ar': fr_to_ar_count,
            'total': ar_to_fr_count + fr_to_ar_count,
            'bidirectional': ar_to_fr_count == fr_to_ar_count
        }

    @classmethod
    def search_translations(cls, query, direction='both'):
        """
        Search for translations containing the query
        """
        results = {}

        if direction in ['both', 'ar_to_fr']:
            ar_to_fr = cls.TRANSLATIONS.get('ar_to_fr', {})
            results['ar_to_fr'] = {
                k: v for k, v in ar_to_fr.items()
                if query.lower() in k.lower() or query.lower() in v.lower()
            }

        if direction in ['both', 'fr_to_ar']:
            fr_to_ar = cls.TRANSLATIONS.get('fr_to_ar', {})
            results['fr_to_ar'] = {
                k: v for k, v in fr_to_ar.items()
                if query.lower() in k.lower() or query.lower() in v.lower()
            }

        return results

    @classmethod
    def validate_translation_consistency(cls):
        """
        Validate that translations are consistent in both directions
        """
        ar_to_fr = cls.TRANSLATIONS.get('ar_to_fr', {})
        fr_to_ar = cls.TRANSLATIONS.get('fr_to_ar', {})

        inconsistencies = []

        for ar_text, fr_text in ar_to_fr.items():
            if fr_text in fr_to_ar:
                if fr_to_ar[fr_text] != ar_text:
                    inconsistencies.append({
                        'arabic': ar_text,
                        'french': fr_text,
                        'reverse_arabic': fr_to_ar[fr_text],
                        'issue': 'reverse_mismatch'
                    })
            else:
                inconsistencies.append({
                    'arabic': ar_text,
                    'french': fr_text,
                    'issue': 'missing_reverse'
                })

        return {
            'is_consistent': len(inconsistencies) == 0,
            'inconsistencies': inconsistencies,
            'total_checked': len(ar_to_fr)
        }

# Global translation functions
def translate_text(text, target_language=None, source_language=None):
    """
    Global function for bidirectional translation
    """
    if target_language is None:
        current_lang = translation.get_language()
        target_language = TranslationEngine.get_opposite_language(current_lang)

    if source_language is None:
        current_lang = translation.get_language()
        source_language = current_lang

    return TranslationEngine.translate(text, target_language, source_language)

def auto_translate_text(text):
    """
    Auto-detect language and translate to opposite
    """
    current_lang = translation.get_language()
    target_lang = TranslationEngine.get_opposite_language(current_lang)
    return TranslationEngine.auto_translate(text, target_lang)

def translate_to_current_language(text):
    """
    Translate text to current language
    """
    current_lang = translation.get_language()
    if current_lang == 'ar':
        return TranslationEngine.translate(text, 'ar', 'fr')
    else:
        return TranslationEngine.translate(text, 'fr', 'ar')

# Template filter functions
def trans_filter(text, language=None):
    """
    Template filter for translation
    """
    return translate_text(text, language)

def bidirectional_trans_filter(text):
    """
    Template filter for bidirectional translation
    """
    return auto_translate_text(text)

def smart_translate_filter(text, target_lang=None):
    """
    Smart translation filter with auto-detection
    """
    if target_lang:
        return TranslationEngine.auto_translate(text, target_lang)
    else:
        return auto_translate_text(text)
