// Main JavaScript file for Sales Management System

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Confirm delete actions
    $('.delete-confirm').on('click', function(e) {
        if (!confirm('هل أنت متأكد من أنك تريد حذف هذا العنصر؟')) {
            e.preventDefault();
        }
    });

    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#dataTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Invoice form functionality
    if ($('#invoice-form').length) {
        initializeInvoiceForm();
    }

    // Product price update
    $(document).on('change', '.product-select', function() {
        var productId = $(this).val();
        var row = $(this).closest('.invoice-item-row');
        var priceInput = row.find('.price-input');
        var stockSpan = row.find('.stock-info');

        if (productId) {
            $.ajax({
                url: '/ajax/get-product-price/',
                data: {
                    'product_id': productId
                },
                success: function(data) {
                    priceInput.val(data.price.toFixed(2));
                    if (stockSpan.length) {
                        stockSpan.text('المخزون: ' + data.stock);
                    }
                    calculateRowTotal(row);
                    calculateInvoiceTotal();
                }
            });
        } else {
            priceInput.val('');
            if (stockSpan.length) {
                stockSpan.text('');
            }
            calculateRowTotal(row);
            calculateInvoiceTotal();
        }
    });

    // Quantity change
    $(document).on('input', '.quantity-input', function() {
        var row = $(this).closest('.invoice-item-row');
        calculateRowTotal(row);
        calculateInvoiceTotal();
    });

    // Price change
    $(document).on('input', '.price-input', function() {
        var row = $(this).closest('.invoice-item-row');
        calculateRowTotal(row);
        calculateInvoiceTotal();
    });

    // Discount and tax change
    $(document).on('input', '#id_discount_percentage, #id_tax_percentage', function() {
        calculateInvoiceTotal();
    });
});

function initializeInvoiceForm() {
    // Add new invoice item row
    $('#add-item').on('click', function(e) {
        e.preventDefault();
        var formCount = parseInt($('#id_invoiceitem_set-TOTAL_FORMS').val());
        var newForm = $('.invoice-item-row:first').clone();
        
        // Update form indices
        newForm.find('input, select').each(function() {
            var name = $(this).attr('name');
            if (name) {
                $(this).attr('name', name.replace(/invoiceitem_set-\d+/, 'invoiceitem_set-' + formCount));
                $(this).attr('id', 'id_' + name.replace(/invoiceitem_set-\d+/, 'invoiceitem_set-' + formCount));
            }
        });
        
        // Clear values
        newForm.find('input, select').val('');
        newForm.find('.total-cell').text('0.00');
        
        // Add remove button
        if (!newForm.find('.remove-item').length) {
            newForm.append('<td><button type="button" class="btn btn-danger btn-sm remove-item">حذف</button></td>');
        }
        
        $('#invoice-items tbody').append(newForm);
        $('#id_invoiceitem_set-TOTAL_FORMS').val(formCount + 1);
    });

    // Remove invoice item row
    $(document).on('click', '.remove-item', function(e) {
        e.preventDefault();
        $(this).closest('.invoice-item-row').remove();
        calculateInvoiceTotal();
        
        // Update form indices
        $('.invoice-item-row').each(function(index) {
            $(this).find('input, select').each(function() {
                var name = $(this).attr('name');
                if (name) {
                    $(this).attr('name', name.replace(/invoiceitem_set-\d+/, 'invoiceitem_set-' + index));
                    $(this).attr('id', 'id_' + name.replace(/invoiceitem_set-\d+/, 'invoiceitem_set-' + index));
                }
            });
        });
        
        $('#id_invoiceitem_set-TOTAL_FORMS').val($('.invoice-item-row').length);
    });

    // Initial calculation
    calculateInvoiceTotal();
}

function calculateRowTotal(row) {
    var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
    var price = parseFloat(row.find('.price-input').val()) || 0;
    var total = quantity * price;
    
    row.find('.total-cell').text(total.toFixed(2));
}

function calculateInvoiceTotal() {
    var subtotal = 0;
    
    $('.invoice-item-row').each(function() {
        var quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
        var price = parseFloat($(this).find('.price-input').val()) || 0;
        subtotal += quantity * price;
    });
    
    var discountPercentage = parseFloat($('#id_discount_percentage').val()) || 0;
    var taxPercentage = parseFloat($('#id_tax_percentage').val()) || 0;
    
    var discountAmount = (subtotal * discountPercentage) / 100;
    var taxableAmount = subtotal - discountAmount;
    var taxAmount = (taxableAmount * taxPercentage) / 100;
    var total = taxableAmount + taxAmount;
    
    $('#subtotal').text(subtotal.toFixed(2));
    $('#discount-amount').text(discountAmount.toFixed(2));
    $('#tax-amount').text(taxAmount.toFixed(2));
    $('#total-amount').text(total.toFixed(2));
}

// Print function
function printInvoice() {
    window.print();
}

// Export to PDF (placeholder function)
function exportToPDF() {
    alert('ميزة التصدير إلى PDF ستكون متاحة قريباً');
}

// Data table initialization
function initializeDataTable(tableId) {
    if ($.fn.DataTable) {
        $(tableId).DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "responsive": true,
            "pageLength": 25,
            "order": [[0, "desc"]]
        });
    }
}

// Format currency
function formatCurrency(amount) {
    return parseFloat(amount).toFixed(2) + ' أوقية';
}

// Show loading spinner
function showLoading() {
    $('body').append('<div id="loading-overlay" class="position-fixed w-100 h-100 d-flex justify-content-center align-items-center" style="top:0;left:0;background:rgba(0,0,0,0.5);z-index:9999;"><div class="spinner-border text-light" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
}

// Hide loading spinner
function hideLoading() {
    $('#loading-overlay').remove();
}

// AJAX setup for CSRF token
$.ajaxSetup({
    beforeSend: function(xhr, settings) {
        if (!(/^http:.*/.test(settings.url) || /^https:.*/.test(settings.url))) {
            xhr.setRequestHeader("X-CSRFToken", $('[name=csrfmiddlewaretoken]').val());
        }
    }
});

// Form validation
function validateForm(formId) {
    var isValid = true;
    $(formId + ' [required]').each(function() {
        if (!$(this).val()) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    return isValid;
}

// Success message
function showSuccessMessage(message) {
    var alert = '<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                message +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>';
    $('.container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

// Error message
function showErrorMessage(message) {
    var alert = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                message +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>';
    $('.container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}
