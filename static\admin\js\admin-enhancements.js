/* Django Admin Dark Theme Enhancements */
/* تحسينات واجهة إدارة Django */

document.addEventListener('DOMContentLoaded', function() {
    enhanceAdminTables();
    enhanceAdminForms();
    addAdminAnimations();
    setupAdminInteractions();
});

function enhanceAdminTables() {
    // Find all admin tables
    const tables = document.querySelectorAll('#changelist table, #changelist-table, .results table, .changelist-results, .module table');
    
    tables.forEach(function(table) {
        // Apply dark theme classes
        table.classList.add('admin-dark-table');
        
        // Enhance table cells
        enhanceAdminTableCells(table);
        
        // Add hover effects
        addAdminTableHoverEffects(table);
    });
}

function enhanceAdminTableCells(table) {
    const cells = table.querySelectorAll('td');
    
    cells.forEach(function(cell, index) {
        const content = cell.textContent.trim();
        const row = cell.parentElement;
        const cellIndex = Array.from(row.cells).indexOf(cell);
        
        // Apply base dark styling
        cell.style.backgroundColor = '#2c3e50';
        cell.style.color = '#ffffff';
        cell.style.borderColor = '#4a6741';
        cell.style.padding = '12px 10px';
        cell.style.fontWeight = '500';
        
        // Apply alternating row colors
        const rowIndex = Array.from(row.parentElement.children).indexOf(row);
        if (rowIndex % 2 === 1) {
            cell.style.backgroundColor = '#34495e';
        }
        
        // Color coding based on content and position
        if (cellIndex === 0) {
            // First column (usually IDs) - Red
            cell.style.color = '#ff6b6b';
            cell.style.fontWeight = '700';
            cell.style.fontSize = '16px';
            cell.style.fontFamily = 'Courier New, monospace';
        } else if (isPrice(content)) {
            // Prices - Green
            cell.style.color = '#51cf66';
            cell.style.fontWeight = '700';
            cell.style.fontSize = '16px';
        } else if (isDate(content)) {
            // Dates - Blue
            cell.style.color = '#74c0fc';
            cell.style.fontWeight = '600';
        } else if (isStatus(content)) {
            // Status - Various colors
            applyStatusStyling(cell, content);
        } else if (cell.querySelector('a')) {
            // Links - Blue
            const link = cell.querySelector('a');
            link.style.color = '#74c0fc';
            link.style.textDecoration = 'none';
            link.style.fontWeight = '600';
        } else {
            // Regular text - Purple
            cell.style.color = '#da77f2';
            cell.style.fontWeight = '600';
        }
    });
}

function addAdminTableHoverEffects(table) {
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(function(row) {
        row.addEventListener('mouseenter', function() {
            const cells = row.querySelectorAll('td');
            cells.forEach(function(cell) {
                cell.style.backgroundColor = '#4a90e2';
                cell.style.color = '#ffffff';
                cell.style.transform = 'translateY(-1px)';
                cell.style.boxShadow = '0 4px 12px rgba(74, 144, 226, 0.3)';
                cell.style.transition = 'all 0.3s ease';
            });
        });
        
        row.addEventListener('mouseleave', function() {
            const cells = row.querySelectorAll('td');
            const rowIndex = Array.from(row.parentElement.children).indexOf(row);
            
            cells.forEach(function(cell, cellIndex) {
                // Restore original background
                if (rowIndex % 2 === 1) {
                    cell.style.backgroundColor = '#34495e';
                } else {
                    cell.style.backgroundColor = '#2c3e50';
                }
                
                // Restore original text colors
                const content = cell.textContent.trim();
                if (cellIndex === 0) {
                    cell.style.color = '#ff6b6b';
                } else if (isPrice(content)) {
                    cell.style.color = '#51cf66';
                } else if (isDate(content)) {
                    cell.style.color = '#74c0fc';
                } else if (cell.querySelector('a')) {
                    cell.querySelector('a').style.color = '#74c0fc';
                } else {
                    cell.style.color = '#da77f2';
                }
                
                cell.style.transform = 'translateY(0)';
                cell.style.boxShadow = 'none';
            });
        });
    });
}

function enhanceAdminForms() {
    // Style form inputs
    const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[type="number"], textarea, select');
    
    inputs.forEach(function(input) {
        input.style.backgroundColor = '#2c3e50';
        input.style.color = '#ffffff';
        input.style.border = '1px solid #4a6741';
        input.style.borderRadius = '4px';
        input.style.padding = '8px 12px';
        
        input.addEventListener('focus', function() {
            input.style.backgroundColor = '#34495e';
            input.style.borderColor = '#74c0fc';
            input.style.boxShadow = '0 0 0 2px rgba(116, 192, 252, 0.3)';
        });
        
        input.addEventListener('blur', function() {
            input.style.backgroundColor = '#2c3e50';
            input.style.borderColor = '#4a6741';
            input.style.boxShadow = 'none';
        });
    });
    
    // Style labels
    const labels = document.querySelectorAll('label');
    labels.forEach(function(label) {
        label.style.color = '#ffffff';
        label.style.fontWeight = '600';
    });
}

function addAdminAnimations() {
    // Add loading animation to buttons
    const buttons = document.querySelectorAll('.button, input[type="submit"], input[type="button"]');
    
    buttons.forEach(function(button) {
        button.addEventListener('click', function() {
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        });
    });
    
    // Add fade-in animation to tables
    const tables = document.querySelectorAll('#changelist table, .results table');
    tables.forEach(function(table) {
        table.style.opacity = '0';
        table.style.transform = 'translateY(20px)';
        table.style.transition = 'all 0.5s ease';
        
        setTimeout(() => {
            table.style.opacity = '1';
            table.style.transform = 'translateY(0)';
        }, 100);
    });
}

function setupAdminInteractions() {
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl + S to save
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveButton = document.querySelector('input[type="submit"][name="_save"]');
            if (saveButton) {
                saveButton.click();
            }
        }
        
        // Ctrl + A to add new
        if (e.ctrlKey && e.key === 'a' && e.shiftKey) {
            e.preventDefault();
            const addButton = document.querySelector('.addlink');
            if (addButton) {
                addButton.click();
            }
        }
    });
    
    // Enhance search functionality
    const searchInput = document.querySelector('#searchbar');
    if (searchInput) {
        searchInput.style.backgroundColor = '#2c3e50';
        searchInput.style.color = '#ffffff';
        searchInput.style.border = '1px solid #4a6741';
        searchInput.style.borderRadius = '20px';
        searchInput.style.padding = '8px 16px';
        
        searchInput.addEventListener('focus', function() {
            searchInput.style.borderColor = '#74c0fc';
            searchInput.style.boxShadow = '0 0 0 2px rgba(116, 192, 252, 0.3)';
        });
    }
}

// Helper functions
function isPrice(str) {
    return /\d+\.?\d*\s*(أوقية|MRU|UM)/i.test(str) || 
           (/^\d+\.?\d*$/.test(str) && parseFloat(str) > 10);
}

function isDate(str) {
    return /\d{4}-\d{2}-\d{2}/.test(str) || 
           /\d{2}\/\d{2}\/\d{4}/.test(str) ||
           /\d{1,2}\s+(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/.test(str);
}

function isStatus(str) {
    const statusWords = [
        'نشط', 'غير نشط', 'متوفر', 'غير متوفر', 'مدفوع', 'غير مدفوع',
        'مكتمل', 'معلق', 'ملغي', 'active', 'inactive', 'available', 
        'unavailable', 'paid', 'unpaid', 'completed', 'pending', 'cancelled'
    ];
    return statusWords.some(word => str.toLowerCase().includes(word.toLowerCase()));
}

function applyStatusStyling(cell, content) {
    const activeWords = ['نشط', 'متوفر', 'مدفوع', 'مكتمل', 'active', 'available', 'paid', 'completed'];
    const inactiveWords = ['غير نشط', 'غير متوفر', 'غير مدفوع', 'ملغي', 'inactive', 'unavailable', 'unpaid', 'cancelled'];
    const pendingWords = ['معلق', 'pending'];
    
    if (activeWords.some(word => content.toLowerCase().includes(word.toLowerCase()))) {
        cell.style.color = '#51cf66';
        cell.style.fontWeight = '700';
        cell.style.backgroundColor = 'rgba(81, 207, 102, 0.1)';
        cell.style.borderRadius = '4px';
    } else if (inactiveWords.some(word => content.toLowerCase().includes(word.toLowerCase()))) {
        cell.style.color = '#ff6b6b';
        cell.style.fontWeight = '700';
        cell.style.backgroundColor = 'rgba(255, 107, 107, 0.1)';
        cell.style.borderRadius = '4px';
    } else if (pendingWords.some(word => content.toLowerCase().includes(word.toLowerCase()))) {
        cell.style.color = '#ffd43b';
        cell.style.fontWeight = '700';
        cell.style.backgroundColor = 'rgba(255, 212, 59, 0.1)';
        cell.style.borderRadius = '4px';
    }
}

// Auto-enhance when new content is loaded
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const tables = node.querySelectorAll ? node.querySelectorAll('table') : [];
                    if (node.tagName === 'TABLE' || tables.length > 0) {
                        setTimeout(() => {
                            enhanceAdminTables();
                        }, 100);
                    }
                }
            });
        }
    });
});

// Start observing
observer.observe(document.body, {
    childList: true,
    subtree: true
});
