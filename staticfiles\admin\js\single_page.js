// Single Page Display for Django Admin
// إزالة التقسيم على صفحات متعددة وعرض جميع النتائج في صفحة واحدة

document.addEventListener('DOMContentLoaded', function() {
    // إخفاء عناصر التقسيم على صفحات
    hidePaginationElements();
    
    // تحسين عرض الجداول
    optimizeTableDisplay();
    
    // تحسين لوحة التحكم
    optimizeDashboard();
    
    // إضافة زر "عرض الكل" إذا لم يكن موجوداً
    addShowAllButton();
});

function hidePaginationElements() {
    // إخفاء عناصر التقسيم
    const paginators = document.querySelectorAll('.paginator');
    paginators.forEach(paginator => {
        paginator.style.display = 'none';
    });
    
    // إخفاء روابط الصفحات
    const pageLinks = document.querySelectorAll('.page-links');
    pageLinks.forEach(link => {
        link.style.display = 'none';
    });
    
    // إخفاء معلومات عدد الصفحات
    const pageInfo = document.querySelectorAll('.page-info');
    pageInfo.forEach(info => {
        info.style.display = 'none';
    });
}

function optimizeTableDisplay() {
    // تحسين عرض الجداول لتكون في صفحة واحدة
    const tables = document.querySelectorAll('.results table');
    tables.forEach(table => {
        table.style.maxHeight = 'none';
        table.style.overflow = 'visible';
        
        // تحسين tbody
        const tbody = table.querySelector('tbody');
        if (tbody) {
            tbody.style.maxHeight = 'none';
            tbody.style.overflow = 'visible';
        }
    });
    
    // تحسين حاوي النتائج
    const results = document.querySelectorAll('.results');
    results.forEach(result => {
        result.style.maxHeight = 'none';
        result.style.overflow = 'visible';
    });
}

function optimizeDashboard() {
    // تحسين لوحة التحكم لتكون في صفحة واحدة
    const dashboard = document.querySelector('.dashboard');
    if (dashboard) {
        dashboard.style.display = 'flex';
        dashboard.style.flexWrap = 'wrap';
        dashboard.style.justifyContent = 'space-between';
        dashboard.style.alignItems = 'flex-start';
        dashboard.style.minHeight = 'calc(100vh - 100px)';
        
        // تحسين وحدات لوحة التحكم
        const modules = dashboard.querySelectorAll('.module');
        modules.forEach((module, index) => {
            module.style.width = 'calc(50% - 20px)';
            module.style.display = 'inline-block';
            module.style.verticalAlign = 'top';
            module.style.minHeight = '300px';
            module.style.margin = '10px';
            
            // تحسين الجداول داخل الوحدات
            const moduleTable = module.querySelector('table');
            if (moduleTable) {
                moduleTable.style.maxHeight = 'none';
                moduleTable.style.overflow = 'visible';
                
                const moduleTbody = moduleTable.querySelector('tbody');
                if (moduleTbody) {
                    moduleTbody.style.maxHeight = 'none';
                    moduleTbody.style.overflow = 'visible';
                }
            }
        });
    }
}

function addShowAllButton() {
    // إضافة زر "عرض الكل" إذا لم يكن موجوداً
    const changeList = document.querySelector('.change-list');
    if (changeList && !document.querySelector('.show-all-btn')) {
        const showAllBtn = document.createElement('button');
        showAllBtn.className = 'show-all-btn';
        showAllBtn.textContent = 'عرض جميع النتائج';
        showAllBtn.style.cssText = `
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
        `;
        
        showAllBtn.addEventListener('click', function() {
            // إزالة جميع قيود العرض
            const allTables = document.querySelectorAll('table');
            allTables.forEach(table => {
                table.style.maxHeight = 'none';
                table.style.overflow = 'visible';
                
                const tbody = table.querySelector('tbody');
                if (tbody) {
                    tbody.style.maxHeight = 'none';
                    tbody.style.overflow = 'visible';
                }
            });
            
            // إخفاء الزر بعد الضغط عليه
            this.style.display = 'none';
            
            // إظهار رسالة تأكيد
            const message = document.createElement('div');
            message.textContent = 'تم عرض جميع النتائج في صفحة واحدة';
            message.style.cssText = `
                background: #2ecc71;
                color: white;
                padding: 10px 20px;
                margin: 10px;
                border-radius: 5px;
                font-size: 14px;
                text-align: center;
            `;
            
            this.parentNode.insertBefore(message, this);
            
            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                message.style.display = 'none';
            }, 3000);
        });
        
        // إضافة الزر في أعلى قائمة التغيير
        const actions = changeList.querySelector('.actions');
        if (actions) {
            actions.appendChild(showAllBtn);
        } else {
            changeList.insertBefore(showAllBtn, changeList.firstChild);
        }
    }
}

// تحسين الأداء عند التمرير
function optimizeScrolling() {
    // إزالة أي قيود على التمرير
    document.body.style.overflow = 'auto';
    document.documentElement.style.overflow = 'auto';
    
    // تحسين عرض المحتوى الطويل
    const content = document.querySelector('#content');
    if (content) {
        content.style.maxHeight = 'none';
        content.style.overflow = 'visible';
    }
    
    const main = document.querySelector('.main');
    if (main) {
        main.style.maxHeight = 'none';
        main.style.overflow = 'visible';
    }
}

// تشغيل تحسين التمرير
optimizeScrolling();

// مراقبة التغييرات في DOM لتطبيق التحسينات على المحتوى الجديد
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            // إعادة تطبيق التحسينات على العناصر الجديدة
            hidePaginationElements();
            optimizeTableDisplay();
            optimizeDashboard();
        }
    });
});

// بدء مراقبة التغييرات
observer.observe(document.body, {
    childList: true,
    subtree: true
});

// تحسين الطباعة لصفحة واحدة
window.addEventListener('beforeprint', function() {
    // إخفاء عناصر التقسيم عند الطباعة
    const style = document.createElement('style');
    style.textContent = `
        @media print {
            .paginator { display: none !important; }
            .page-links { display: none !important; }
            .page-info { display: none !important; }
            .show-all-btn { display: none !important; }
            
            .results table {
                page-break-inside: auto !important;
                max-height: none !important;
                overflow: visible !important;
            }
            
            .results table tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            .dashboard {
                page-break-inside: avoid !important;
            }
            
            .dashboard .module {
                page-break-inside: avoid !important;
                margin-bottom: 20px !important;
            }
        }
    `;
    document.head.appendChild(style);
});

console.log('Single Page Display Script Loaded - تم تحميل سكريبت العرض في صفحة واحدة');
