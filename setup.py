#!/usr/bin/env python
"""
Setup script for Sales Management System
نص إعداد نظام إدارة المبيعات
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error in {description}:")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up Sales Management System")
    print("🚀 إعداد نظام إدارة المبيعات")
    print("=" * 50)
    
    # Check if virtual environment exists
    venv_path = Path("venv")
    if not venv_path.exists():
        print("\n📦 Creating virtual environment...")
        if not run_command("python -m venv venv", "Creating virtual environment"):
            print("❌ Failed to create virtual environment. Please ensure Python is installed.")
            return False
    
    # Activate virtual environment and install requirements
    if sys.platform == "win32":
        activate_cmd = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
        python_cmd = "venv\\Scripts\\python"
    else:
        activate_cmd = "source venv/bin/activate"
        pip_cmd = "venv/bin/pip"
        python_cmd = "venv/bin/python"
    
    # Install requirements
    if not run_command(f"{pip_cmd} install -r requirements.txt", "Installing Python packages"):
        print("❌ Failed to install requirements. Please check your internet connection.")
        return False
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("\n⚠️  .env file not found. Please create it with your database settings.")
        print("Example .env content:")
        print("""
SECRET_KEY=your-secret-key-here
DEBUG=True
DB_NAME=sales_system
DB_USER=root
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
        """)
        return False
    
    # Run migrations
    if not run_command(f"{python_cmd} manage.py makemigrations", "Creating migrations"):
        print("⚠️  Warning: Could not create migrations. This might be normal for first run.")
    
    if not run_command(f"{python_cmd} manage.py migrate", "Applying migrations"):
        print("❌ Failed to apply migrations. Please check your database settings.")
        return False
    
    # Collect static files
    if not run_command(f"{python_cmd} manage.py collectstatic --noinput", "Collecting static files"):
        print("⚠️  Warning: Could not collect static files.")
    
    # Compile messages
    if not run_command(f"{python_cmd} manage.py compilemessages", "Compiling translation messages"):
        print("⚠️  Warning: Could not compile translation messages.")
    
    print("\n🎉 Setup completed successfully!")
    print("🎉 تم الإعداد بنجاح!")
    print("\n📋 Next steps:")
    print("📋 الخطوات التالية:")
    print("1. Create a superuser: python manage.py createsuperuser")
    print("1. إنشاء مستخدم إداري: python manage.py createsuperuser")
    print("2. Run the server: python manage.py runserver")
    print("2. تشغيل الخادم: python manage.py runserver")
    print("3. Open http://127.0.0.1:8000 in your browser")
    print("3. افتح http://127.0.0.1:8000 في المتصفح")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
