#!/usr/bin/env python3
"""
Script to create .mo file from .po file for French translations
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
django.setup()

from django.core.management import execute_from_command_line

def create_mo_files():
    """Create .mo files from .po files"""
    try:
        print("Creating .mo files from .po files...")
        
        # Try to compile messages
        execute_from_command_line(['manage.py', 'compilemessages'])
        print("✅ Successfully created .mo files")
        
    except Exception as e:
        print(f"❌ Error creating .mo files: {e}")
        
        # Manual creation as fallback
        print("🔄 Trying manual creation...")
        try:
            import polib
            
            # Load the .po file
            po_file = BASE_DIR / 'locale' / 'fr' / 'LC_MESSAGES' / 'django.po'
            mo_file = BASE_DIR / 'locale' / 'fr' / 'LC_MESSAGES' / 'django.mo'
            
            if po_file.exists():
                po = polib.pofile(str(po_file))
                po.save_as_mofile(str(mo_file))
                print(f"✅ Manually created {mo_file}")
            else:
                print(f"❌ .po file not found: {po_file}")
                
        except ImportError:
            print("❌ polib not available, installing...")
            os.system('pip install polib')
            
            # Try again
            try:
                import polib
                po_file = BASE_DIR / 'locale' / 'fr' / 'LC_MESSAGES' / 'django.po'
                mo_file = BASE_DIR / 'locale' / 'fr' / 'LC_MESSAGES' / 'django.mo'
                
                if po_file.exists():
                    po = polib.pofile(str(po_file))
                    po.save_as_mofile(str(mo_file))
                    print(f"✅ Created {mo_file} with polib")
                    
            except Exception as e2:
                print(f"❌ Manual creation failed: {e2}")
                
                # Create a simple .mo file manually
                print("🔄 Creating basic .mo file...")
                create_basic_mo_file()

def create_basic_mo_file():
    """Create a basic .mo file manually"""
    import struct
    
    mo_file = BASE_DIR / 'locale' / 'fr' / 'LC_MESSAGES' / 'django.mo'
    
    # Basic translations
    translations = {
        'نظام إدارة المبيعات': 'Système de Gestion des Ventes',
        'لوحة التحكم': 'Tableau de Bord',
        'العملاء': 'Clients',
        'المنتجات': 'Produits',
        'الفواتير': 'Factures',
        'التقارير': 'Rapports',
        'الموردين': 'Fournisseurs',
        'الفئات': 'Catégories',
        'إضافة': 'Ajouter',
        'تعديل': 'Modifier',
        'حذف': 'Supprimer',
        'عرض': 'Voir',
        'بحث': 'Rechercher',
        'حفظ': 'Enregistrer',
        'إلغاء': 'Annuler',
        'طباعة': 'Imprimer',
    }
    
    # Create .mo file structure
    keys = list(translations.keys())
    values = list(translations.values())
    
    # Encode strings
    kencoded = [k.encode('utf-8') for k in keys]
    vencoded = [v.encode('utf-8') for v in values]
    
    # Create the .mo file
    try:
        with open(mo_file, 'wb') as f:
            # Magic number
            f.write(struct.pack('<I', 0x950412de))
            # Version
            f.write(struct.pack('<I', 0))
            # Number of entries
            f.write(struct.pack('<I', len(keys)))
            # Offset of key table
            f.write(struct.pack('<I', 28))
            # Offset of value table
            f.write(struct.pack('<I', 28 + 8 * len(keys)))
            # Hash table size
            f.write(struct.pack('<I', 0))
            # Offset of hash table
            f.write(struct.pack('<I', 0))
            
            # Calculate offsets
            key_offsets = []
            value_offsets = []
            offset = 28 + 16 * len(keys)
            
            for k in kencoded:
                key_offsets.append(offset)
                offset += len(k) + 1
                
            for v in vencoded:
                value_offsets.append(offset)
                offset += len(v) + 1
            
            # Write key table
            for i, k in enumerate(kencoded):
                f.write(struct.pack('<I', len(k)))
                f.write(struct.pack('<I', key_offsets[i]))
                
            # Write value table
            for i, v in enumerate(vencoded):
                f.write(struct.pack('<I', len(v)))
                f.write(struct.pack('<I', value_offsets[i]))
                
            # Write keys
            for k in kencoded:
                f.write(k)
                f.write(b'\x00')
                
            # Write values
            for v in vencoded:
                f.write(v)
                f.write(b'\x00')
                
        print(f"✅ Created basic .mo file: {mo_file}")
        
    except Exception as e:
        print(f"❌ Failed to create basic .mo file: {e}")

if __name__ == '__main__':
    create_mo_files()
