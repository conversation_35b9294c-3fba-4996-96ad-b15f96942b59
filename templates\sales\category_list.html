{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "قائمة الفئات" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-folder"></i> {% trans "قائمة الفئات" %}
            </h1>
            <a href="{% url 'sales:category_add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {% trans "إضافة فئة جديدة" %}
            </a>
        </div>
    </div>
</div>

<!-- Categories Grid -->
<div class="row">
    {% if categories %}
        {% for category in categories %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 50px; height: 50px;">
                            <i class="fas fa-folder fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0">{{ category.name }}</h5>
                            <small class="text-muted">
                                {% trans "تاريخ الإنشاء" %}: {{ category.created_at|date:"Y-m-d" }}
                            </small>
                        </div>
                    </div>
                    
                    {% if category.description %}
                        <p class="card-text">{{ category.description|truncatewords:20 }}</p>
                    {% else %}
                        <p class="card-text text-muted">{% trans "لا يوجد وصف" %}</p>
                    {% endif %}
                    
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="badge bg-info">
                                {{ category.product_set.count }} {% trans "منتج" %}
                            </span>
                            <span class="text-muted small">
                                {% trans "آخر تحديث" %}: {{ category.created_at|timesince }}
                            </span>
                        </div>
                        
                        <div class="btn-group w-100" role="group">
                            <a href="{% url 'sales:product_list' %}?category={{ category.id }}" 
                               class="btn btn-outline-info btn-sm">
                                <i class="fas fa-eye"></i> {% trans "عرض المنتجات" %}
                            </a>
                            <a href="{% url 'sales:category_edit' category.pk %}" 
                               class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'sales:category_delete' category.pk %}" 
                               class="btn btn-outline-danger btn-sm delete-confirm">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-folder fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "لا توجد فئات حتى الآن" %}</h5>
                <p class="text-muted">{% trans "ابدأ بإضافة فئة جديدة لتنظيم منتجاتك" %}</p>
                <a href="{% url 'sales:category_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {% trans "إضافة فئة جديدة" %}
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Quick Stats -->
{% if categories %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar"></i> {% trans "إحصائيات الفئات" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary">{{ categories|length }}</h4>
                        <p class="text-muted mb-0">{% trans "إجمالي الفئات" %}</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success">
                            {% for category in categories %}
                                {{ category.product_set.count|add:0 }}
                                {% if not forloop.last %}+{% endif %}
                            {% endfor %}
                        </h4>
                        <p class="text-muted mb-0">{% trans "إجمالي المنتجات" %}</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info">
                            {% for category in categories %}
                                {% if category.product_set.count > 0 %}{{ forloop.counter }}{% endif %}
                            {% empty %}0{% endfor %}
                        </h4>
                        <p class="text-muted mb-0">{% trans "فئات بها منتجات" %}</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning">
                            {% for category in categories %}
                                {% if category.product_set.count == 0 %}{{ forloop.counter }}{% endif %}
                            {% empty %}0{% endfor %}
                        </h4>
                        <p class="text-muted mb-0">{% trans "فئات فارغة" %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Confirm delete with additional warning for categories with products
    $('.delete-confirm').on('click', function(e) {
        var categoryName = $(this).closest('.card').find('.card-title').text();
        var productCount = $(this).closest('.card').find('.badge').text().split(' ')[0];
        
        var message = 'هل أنت متأكد من أنك تريد حذف فئة "' + categoryName + '"؟';
        
        if (parseInt(productCount) > 0) {
            message += '\n\nتحذير: هذه الفئة تحتوي على ' + productCount + ' منتج. سيتم حذف جميع المنتجات المرتبطة بها أيضاً!';
        }
        
        if (!confirm(message)) {
            e.preventDefault();
        }
    });
    
    // Add hover effects
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg').css('transform', 'translateY(-2px)');
        },
        function() {
            $(this).removeClass('shadow-lg').css('transform', 'translateY(0)');
        }
    );
});
</script>
{% endblock %}
