from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'sales'

urlpatterns = [
    # Authentication URLs
    path('login/', auth_views.LoginView.as_view(template_name='sales/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    
    # Dashboard
    path('', views.DashboardView.as_view(), name='dashboard'),
    
    # Customer URLs
    path('customers/', views.CustomerListView.as_view(), name='customer_list'),
    path('customers/add/', views.CustomerCreateView.as_view(), name='customer_add'),
    path('customers/<int:pk>/', views.CustomerDetailView.as_view(), name='customer_detail'),
    path('customers/<int:pk>/edit/', views.CustomerUpdateView.as_view(), name='customer_edit'),
    path('customers/<int:pk>/delete/', views.CustomerDeleteView.as_view(), name='customer_delete'),
    
    # Product URLs
    path('products/', views.ProductListView.as_view(), name='product_list'),
    path('products/add/', views.ProductCreateView.as_view(), name='product_add'),
    path('products/<int:pk>/', views.ProductDetailView.as_view(), name='product_detail'),
    path('products/<int:pk>/edit/', views.ProductUpdateView.as_view(), name='product_edit'),
    path('products/<int:pk>/delete/', views.ProductDeleteView.as_view(), name='product_delete'),
    
    # Category URLs
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('categories/add/', views.CategoryCreateView.as_view(), name='category_add'),
    path('categories/<int:pk>/edit/', views.CategoryUpdateView.as_view(), name='category_edit'),
    path('categories/<int:pk>/delete/', views.CategoryDeleteView.as_view(), name='category_delete'),
    
    # Invoice URLs
    path('invoices/', views.InvoiceListView.as_view(), name='invoice_list'),
    path('invoices/add/', views.InvoiceCreateView.as_view(), name='invoice_add'),
    path('invoices/<int:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'),
    path('invoices/<int:pk>/edit/', views.InvoiceUpdateView.as_view(), name='invoice_edit'),
    path('invoices/<int:pk>/delete/', views.InvoiceDeleteView.as_view(), name='invoice_delete'),
    path('invoices/<int:pk>/print/', views.InvoicePrintView.as_view(), name='invoice_print'),
    
    # Reports
    path('reports/', views.ReportsView.as_view(), name='reports'),
    path('advanced-reports/', views.AdvancedReportsView.as_view(), name='advanced_reports'),
    
    # Supplier URLs
    path('suppliers/', views.SupplierListView.as_view(), name='supplier_list'),
    path('suppliers/add/', views.SupplierCreateView.as_view(), name='supplier_add'),
    path('suppliers/<int:pk>/', views.SupplierDetailView.as_view(), name='supplier_detail'),
    path('suppliers/<int:pk>/edit/', views.SupplierUpdateView.as_view(), name='supplier_edit'),
    path('suppliers/<int:pk>/delete/', views.SupplierDeleteView.as_view(), name='supplier_delete'),

    # Purchase Invoice URLs
    path('purchase-invoices/', views.PurchaseInvoiceListView.as_view(), name='purchase_invoice_list'),
    path('purchase-invoices/add/', views.PurchaseInvoiceCreateView.as_view(), name='purchase_invoice_add'),
    path('purchase-invoices/<int:pk>/', views.PurchaseInvoiceDetailView.as_view(), name='purchase_invoice_detail'),
    path('purchase-invoices/<int:pk>/edit/', views.PurchaseInvoiceUpdateView.as_view(), name='purchase_invoice_edit'),
    path('purchase-invoices/<int:pk>/delete/', views.PurchaseInvoiceDeleteView.as_view(), name='purchase_invoice_delete'),
    path('purchase-invoices/<int:pk>/print/', views.PurchaseInvoicePrintView.as_view(), name='purchase_invoice_print'),

    # AJAX URLs
    path('ajax/get-product-price/', views.get_product_price, name='get_product_price'),
    path('ajax/customers/', views.ajax_customers, name='ajax_customers'),
    path('ajax/products/', views.ajax_products, name='ajax_products'),
    path('ajax/suppliers/', views.ajax_suppliers, name='ajax_suppliers'),

    # Barcode URLs
    path('product/<int:pk>/barcode/', views.BarcodeView.as_view(), name='product_barcode'),
    path('product/<int:pk>/barcode/print/', views.BarcodePrintView.as_view(), name='product_barcode_print'),
    path('ajax/generate-barcode/<int:product_id>/', views.generate_barcode_for_product, name='generate_barcode'),
    path('ajax/search-barcode/', views.search_by_barcode, name='search_barcode'),
    path('ajax/check-barcode-unique/', views.check_barcode_unique, name='check_barcode_unique'),
    path('ajax/generate-random-barcode/', views.generate_random_barcode, name='generate_random_barcode'),

    # Language Preference URLs
    path('ajax/set-language-preference/', views.set_user_language_preference, name='set_language_preference'),
    path('ajax/get-language-preference/', views.get_user_language_preference, name='get_language_preference'),
    path('set-language/', views.custom_set_language, name='custom_set_language'),
    path('test-language/', views.test_language, name='test_language'),
    # path('debug-language/', views.debug_language, name='debug_language'),  # Disabled - function not found
    path('simple-test/', views.simple_language_test, name='simple_language_test'),
    path('translation-test/', views.translation_test, name='translation_test'),
    path('bidirectional-demo/', views.bidirectional_translation_demo, name='bidirectional_demo'),
    path('translation-showcase/', views.complete_translation_showcase, name='translation_showcase'),
]
