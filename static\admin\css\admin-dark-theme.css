/* DJANGO ADMIN DARK THEME */
/* تطبيق الخلفية السوداء على واجهة إدارة الموقع */

/* ========================================
   ADMIN INTERFACE DARK THEME
   ======================================== */

/* Main admin body */
body.admin {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

/* Admin header */
#header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    color: #ffffff !important;
    border-bottom: 3px solid #4a6741 !important;
}

#header a:link,
#header a:visited {
    color: #74c0fc !important;
}

#header a:hover {
    color: #339af0 !important;
}

/* Breadcrumbs */
.breadcrumbs {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
    color: #ffffff !important;
    border-bottom: 1px solid #4a6741 !important;
}

.breadcrumbs a {
    color: #74c0fc !important;
}

/* ========================================
   ADMIN TABLES DARK THEME
   ======================================== */

/* All admin tables */
#changelist table,
#changelist-table,
.results table,
.changelist-results,
.module table,
.inline-group table,
.tabular table,
.admin table,
table.admin {
    background-color: #2c3e50 !important;
    border: 2px solid #4a6741 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
}

/* Table headers */
#changelist table thead th,
#changelist-table thead th,
.results table thead th,
.changelist-results thead th,
.module table thead th,
.inline-group table thead th,
.tabular table thead th,
.admin table thead th,
table.admin thead th {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    letter-spacing: 1px !important;
    padding: 15px 12px !important;
    border: none !important;
    border-bottom: 2px solid #4a6741 !important;
}

/* Table cells */
#changelist table td,
#changelist-table td,
.results table td,
.changelist-results td,
.module table td,
.inline-group table td,
.tabular table td,
.admin table td,
table.admin td {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    padding: 12px 10px !important;
    border-bottom: 1px solid #4a6741 !important;
    border-left: none !important;
    border-right: none !important;
    vertical-align: middle !important;
}

/* Alternating rows */
#changelist table tbody tr:nth-child(even) td,
#changelist-table tbody tr:nth-child(even) td,
.results table tbody tr:nth-child(even) td,
.changelist-results tbody tr:nth-child(even) td,
.module table tbody tr:nth-child(even) td,
.inline-group table tbody tr:nth-child(even) td,
.tabular table tbody tr:nth-child(even) td,
.admin table tbody tr:nth-child(even) td,
table.admin tbody tr:nth-child(even) td {
    background-color: #34495e !important;
}

/* Hover effects */
#changelist table tbody tr:hover td,
#changelist-table tbody tr:hover td,
.results table tbody tr:hover td,
.changelist-results tbody tr:hover td,
.module table tbody tr:hover td,
.inline-group table tbody tr:hover td,
.tabular table tbody tr:hover td,
.admin table tbody tr:hover td,
table.admin tbody tr:hover td {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3) !important;
    transition: all 0.3s ease !important;
}

/* ========================================
   COLORED TEXT FOR ADMIN TABLES
   ======================================== */

/* First column (IDs) - Red */
#changelist table td:first-child,
#changelist-table td:first-child,
.results table td:first-child,
.changelist-results td:first-child {
    color: #ff6b6b !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    font-family: 'Courier New', monospace !important;
}

/* Links in admin tables - Blue */
#changelist table a,
#changelist-table a,
.results table a,
.changelist-results a,
.module table a,
.admin table a {
    color: #74c0fc !important;
    text-decoration: none !important;
    font-weight: 600 !important;
}

#changelist table a:hover,
#changelist-table a:hover,
.results table a:hover,
.changelist-results a:hover,
.module table a:hover,
.admin table a:hover {
    color: #339af0 !important;
    text-decoration: underline !important;
}

/* Strong text - Yellow */
#changelist table strong,
#changelist-table strong,
.results table strong,
.changelist-results strong,
.module table strong,
.admin table strong {
    color: #ffd43b !important;
    font-weight: 700 !important;
}

/* ========================================
   ADMIN SIDEBAR AND MODULES
   ======================================== */

/* Sidebar */
#nav-sidebar {
    background-color: #2c3e50 !important;
    border-right: 2px solid #4a6741 !important;
}

#nav-sidebar .module {
    background-color: #34495e !important;
    border: 1px solid #4a6741 !important;
    border-radius: 8px !important;
    margin-bottom: 15px !important;
}

#nav-sidebar .module h2 {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    padding: 12px 15px !important;
    border-radius: 8px 8px 0 0 !important;
}

#nav-sidebar .module ul {
    background-color: #34495e !important;
    padding: 10px 0 !important;
}

#nav-sidebar .module li {
    border-bottom: 1px solid #4a6741 !important;
}

#nav-sidebar .module li:last-child {
    border-bottom: none !important;
}

#nav-sidebar .module a {
    color: #74c0fc !important;
    padding: 10px 15px !important;
    display: block !important;
    text-decoration: none !important;
    font-weight: 500 !important;
}

#nav-sidebar .module a:hover {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
    transform: translateX(5px) !important;
    transition: all 0.3s ease !important;
}

/* ========================================
   ADMIN FORMS
   ======================================== */

/* Form containers */
.module,
.form-row,
fieldset {
    background-color: #34495e !important;
    border: 1px solid #4a6741 !important;
    border-radius: 8px !important;
    color: #ffffff !important;
}

.module h2,
fieldset h2 {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    padding: 12px 15px !important;
    border-radius: 8px 8px 0 0 !important;
    margin: 0 !important;
}

/* Form inputs */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
    border: 1px solid #4a6741 !important;
    border-radius: 4px !important;
    padding: 8px 12px !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    background-color: #34495e !important;
    border-color: #74c0fc !important;
    box-shadow: 0 0 0 2px rgba(116, 192, 252, 0.3) !important;
}

/* Labels */
label {
    color: #ffffff !important;
    font-weight: 600 !important;
}

/* ========================================
   ADMIN BUTTONS
   ======================================== */

/* Buttons */
.button,
input[type="submit"],
input[type="button"],
.submit-row input {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 10px 20px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.button:hover,
input[type="submit"]:hover,
input[type="button"]:hover,
.submit-row input:hover {
    background: linear-gradient(135deg, #357abd 0%, #2968a3 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3) !important;
}

/* Delete button */
.deletelink {
    background: linear-gradient(135deg, #ff6b6b 0%, #e55555 100%) !important;
}

.deletelink:hover {
    background: linear-gradient(135deg, #e55555 0%, #cc4444 100%) !important;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
    #changelist table td,
    .results table td {
        padding: 8px 6px !important;
        font-size: 12px !important;
    }
    
    #changelist table thead th,
    .results table thead th {
        padding: 10px 8px !important;
        font-size: 10px !important;
    }
}
