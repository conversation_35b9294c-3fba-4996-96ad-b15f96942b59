{% load static %}
{% load translation_extras %}

<!-- Compact Language Switcher - Very Small Size -->
<div class="compact-lang-switcher" id="compact-switcher">
    <div class="compact-container">
        <!-- Arabic Button -->
        <div class="compact-btn {% if request.LANGUAGE_CODE == 'ar' %}active{% endif %}" 
             data-lang="ar" onclick="switchToLanguage('ar', 'العربية')">
            <div class="compact-flag">🇲🇷</div>
            <span class="compact-code">AR</span>
            {% if request.LANGUAGE_CODE == 'ar' %}
                <div class="compact-active">✓</div>
            {% endif %}
        </div>
        
        <!-- French Button -->
        <div class="compact-btn {% if request.LANGUAGE_CODE == 'fr' %}active{% endif %}" 
             data-lang="fr" onclick="switchToLanguage('fr', 'Français')">
            <div class="compact-flag">🇫🇷</div>
            <span class="compact-code">FR</span>
            {% if request.LANGUAGE_CODE == 'fr' %}
                <div class="compact-active">✓</div>
            {% endif %}
        </div>
    </div>
    
    <!-- Mini Status -->
    <div class="compact-status" id="compact-status">
        <i class="fas fa-sync fa-spin"></i>
    </div>
</div>

<style>
.compact-lang-switcher {
    position: fixed;
    top: 70px;
    {% if request.direction == 'rtl' %}
        left: 15px;
    {% else %}
        right: 15px;
    {% endif %}
    z-index: 10000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.compact-container {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 4px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    gap: 2px;
}

.compact-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    padding: 6px 8px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    position: relative;
    min-width: 50px;
}

.compact-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-1px);
}

.compact-btn.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    cursor: default;
}

.compact-btn.active:hover {
    transform: none;
}

.compact-flag {
    font-size: 14px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(0,0,0,0.1);
}

.compact-btn.active .compact-flag {
    background: rgba(255,255,255,0.2);
}

.compact-code {
    font-size: 9px;
    font-weight: 600;
    line-height: 1;
    opacity: 0.9;
}

.compact-active {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    font-weight: bold;
    border: 1px solid white;
    box-shadow: 0 1px 4px rgba(0,0,0,0.2);
}

.compact-status {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 5px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 10px;
    font-size: 10px;
    display: none;
    backdrop-filter: blur(5px);
}

.compact-status.show {
    display: flex;
    align-items: center;
    gap: 4px;
}

.compact-status.success {
    background: rgba(40, 167, 69, 0.9);
}

.compact-status.error {
    background: rgba(220, 53, 69, 0.9);
}

/* Responsive Design */
@media (max-width: 768px) {
    .compact-lang-switcher {
        top: 50px;
        {% if request.direction == 'rtl' %}
            left: 10px;
        {% else %}
            right: 10px;
        {% endif %}
    }
    
    .compact-btn {
        min-width: 45px;
        padding: 5px 6px;
    }
    
    .compact-flag {
        font-size: 12px;
        width: 18px;
        height: 18px;
    }
    
    .compact-code {
        font-size: 8px;
    }
    
    .compact-container {
        padding: 3px;
        border-radius: 12px;
    }
}

/* Animation for page transition */
.page-switching {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}
</style>

<script>
// Compact language switching function
function switchToLanguage(language, languageName) {
    // Don't switch if it's already the current language
    if (document.querySelector(`[data-lang="${language}"]`).classList.contains('active')) {
        return;
    }
    
    // Show compact status
    showCompactStatus('loading');
    
    // Add page transition effect
    document.body.classList.add('page-switching');
    
    // Switch language
    fetch('/set-language-no-csrf/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'language=' + encodeURIComponent(language)
    })
    .then(response => {
        if (response.ok) {
            showCompactStatus('success');
            
            // Quick content translation preview
            translateKeyContent(language).then(() => {
                setTimeout(() => {
                    window.location.reload();
                }, 800);
            });
        } else {
            throw new Error('Language switch failed');
        }
    })
    .catch(error => {
        console.error('Language switch error:', error);
        showCompactStatus('error');
        setTimeout(() => {
            hideCompactStatus();
            document.body.classList.remove('page-switching');
        }, 1500);
    });
}

// Show compact status
function showCompactStatus(type = 'loading') {
    const statusElement = document.getElementById('compact-status');
    if (!statusElement) return;
    
    const iconElement = statusElement.querySelector('i');
    statusElement.className = `compact-status show ${type}`;
    
    if (iconElement) {
        if (type === 'loading') {
            iconElement.className = 'fas fa-sync fa-spin';
        } else if (type === 'success') {
            iconElement.className = 'fas fa-check';
        } else if (type === 'error') {
            iconElement.className = 'fas fa-times';
        }
    }
}

// Hide compact status
function hideCompactStatus() {
    const statusElement = document.getElementById('compact-status');
    if (statusElement) {
        statusElement.classList.remove('show');
    }
}

// Quick content translation for key elements
async function translateKeyContent(targetLanguage) {
    try {
        // Get only the most important elements
        const keyElements = document.querySelectorAll('h1, h2, .card-title, .btn-primary');
        const textsToTranslate = [];
        const elementMap = new Map();
        
        keyElements.forEach(element => {
            const text = element.textContent.trim();
            if (text && text.length > 1 && text.length < 30 && !text.match(/^\d+$/)) {
                textsToTranslate.push(text);
                elementMap.set(text, element);
            }
        });
        
        if (textsToTranslate.length > 0) {
            const response = await fetch('/api/batch-translate/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    texts: textsToTranslate.slice(0, 5), // Only first 5 items
                    target_language: targetLanguage,
                    source_language: targetLanguage === 'ar' ? 'fr' : 'ar'
                })
            });
            
            const data = await response.json();
            
            if (data.success && data.translations) {
                data.translations.forEach((translation, index) => {
                    const element = elementMap.get(translation.original);
                    if (element) {
                        setTimeout(() => {
                            element.style.transition = 'opacity 0.2s ease';
                            element.style.opacity = '0.7';
                            setTimeout(() => {
                                element.textContent = translation.translated;
                                element.style.opacity = '1';
                            }, 100);
                        }, index * 50);
                    }
                });
            }
        }
    } catch (error) {
        console.error('Quick translation error:', error);
    }
}

// Initialize compact switcher
document.addEventListener('DOMContentLoaded', function() {
    // Add subtle hover effects
    document.querySelectorAll('.compact-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-1px) scale(1.05)';
            }
        });
        
        btn.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
});
</script>
