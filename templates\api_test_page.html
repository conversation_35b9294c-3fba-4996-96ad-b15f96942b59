{% load static %}
{% load translation_extras %}

<!DOCTYPE html>
<html lang="{{ lang.code }}" dir="{{ lang.direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API الترجمة - Translation API Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .api-test-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .api-test-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .api-result {
            background: #1e1e1e;
            color: #00ff00;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-loading { background: #ffc107; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .endpoint-url {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="display-4">
                    <i class="fas fa-code"></i>
                    اختبار API الترجمة المتكامل
                </h1>
                <p class="lead">Translation API Testing Interface</p>
            </div>
            
            <!-- API Health Check -->
            <div class="api-test-card">
                <h3>
                    <span class="status-indicator" id="health-status"></span>
                    فحص صحة API / API Health Check
                </h3>
                <div class="endpoint-url">GET /api/health/</div>
                <button class="test-btn" onclick="testAPIHealth()">
                    <i class="fas fa-heartbeat"></i> فحص الصحة
                </button>
                <div class="api-result" id="health-result"></div>
            </div>
            
            <!-- Simple Translation Test -->
            <div class="api-test-card">
                <h3>
                    <span class="status-indicator" id="simple-status"></span>
                    اختبار الترجمة البسيط / Simple Translation Test
                </h3>
                <div class="endpoint-url">POST /api/simple-translate/</div>
                
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">النص للترجمة / Text to Translate:</label>
                        <textarea class="form-control" id="simple-text" rows="3">نظام إدارة المبيعات المتطور</textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">اللغة المستهدفة / Target Language:</label>
                        <select class="form-control" id="simple-target">
                            <option value="fr">French (الفرنسية)</option>
                            <option value="ar">Arabic (العربية)</option>
                        </select>
                    </div>
                </div>
                
                <button class="test-btn mt-3" onclick="testSimpleTranslation()">
                    <i class="fas fa-language"></i> ترجم الآن
                </button>
                <div class="api-result" id="simple-result"></div>
            </div>
            
            <!-- Auto Translation Test -->
            <div class="api-test-card">
                <h3>
                    <span class="status-indicator" id="auto-status"></span>
                    اختبار الترجمة التلقائية / Auto Translation Test
                </h3>
                <div class="endpoint-url">POST /api/auto-translate/</div>
                
                <div class="row">
                    <div class="col-md-8">
                        <label class="form-label">النص (سيتم كشف اللغة تلقائياً) / Text (Language Auto-Detected):</label>
                        <textarea class="form-control" id="auto-text" rows="3">Bienvenue dans notre système de gestion</textarea>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">اللغة المستهدفة / Target Language:</label>
                        <select class="form-control" id="auto-target">
                            <option value="ar">Arabic (العربية)</option>
                            <option value="fr">French (الفرنسية)</option>
                        </select>
                    </div>
                </div>
                
                <button class="test-btn mt-3" onclick="testAutoTranslation()">
                    <i class="fas fa-magic"></i> ترجمة تلقائية
                </button>
                <div class="api-result" id="auto-result"></div>
            </div>
            
            <!-- Batch Translation Test -->
            <div class="api-test-card">
                <h3>
                    <span class="status-indicator" id="batch-status"></span>
                    اختبار الترجمة المجمعة / Batch Translation Test
                </h3>
                <div class="endpoint-url">POST /api/batch-translate/</div>
                
                <div class="row">
                    <div class="col-md-12">
                        <label class="form-label">النصوص (واحد في كل سطر) / Texts (one per line):</label>
                        <textarea class="form-control" id="batch-texts" rows="5">العملاء
المنتجات
الفواتير
التقارير
الإعدادات</textarea>
                    </div>
                </div>
                
                <button class="test-btn mt-3" onclick="testBatchTranslation()">
                    <i class="fas fa-list"></i> ترجمة مجمعة
                </button>
                <div class="api-result" id="batch-result"></div>
            </div>
            
            <!-- Translation Statistics -->
            <div class="api-test-card">
                <h3>
                    <span class="status-indicator" id="stats-status"></span>
                    إحصائيات الترجمة / Translation Statistics
                </h3>
                <div class="endpoint-url">GET /api/stats-simple/</div>
                
                <button class="test-btn" onclick="testTranslationStats()">
                    <i class="fas fa-chart-bar"></i> عرض الإحصائيات
                </button>
                <div class="api-result" id="stats-result"></div>
            </div>
            
            <!-- Debug Request -->
            <div class="api-test-card">
                <h3>
                    <span class="status-indicator" id="debug-status"></span>
                    تشخيص الطلبات / Request Debug
                </h3>
                <div class="endpoint-url">POST /api/debug/</div>
                
                <button class="test-btn" onclick="testDebugRequest()">
                    <i class="fas fa-bug"></i> تشخيص الطلب
                </button>
                <div class="api-result" id="debug-result"></div>
            </div>
            
            <!-- Test All APIs -->
            <div class="text-center mt-4">
                <button class="test-btn btn-lg" onclick="testAllAPIs()">
                    <i class="fas fa-play-circle"></i> اختبار جميع APIs
                </button>
                <button class="test-btn btn-lg" onclick="clearAllResults()">
                    <i class="fas fa-eraser"></i> مسح النتائج
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Helper functions
        function setStatus(elementId, status) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator status-${status}`;
        }
        
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            if (isError) {
                element.style.color = '#ff6b6b';
                element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            } else {
                element.style.color = '#00ff00';
                element.textContent = JSON.stringify(data, null, 2);
            }
        }
        
        function getCsrfToken() {
            const token = document.querySelector('[name=csrfmiddlewaretoken]');
            return token ? token.value : '';
        }
        
        // API Test Functions
        async function testAPIHealth() {
            setStatus('health-status', 'loading');
            try {
                const response = await fetch('/api/health/');
                const data = await response.json();
                setStatus('health-status', data.success ? 'success' : 'error');
                showResult('health-result', data);
            } catch (error) {
                setStatus('health-status', 'error');
                showResult('health-result', `Error: ${error.message}`, true);
            }
        }
        
        async function testSimpleTranslation() {
            setStatus('simple-status', 'loading');
            const text = document.getElementById('simple-text').value;
            const target = document.getElementById('simple-target').value;
            
            try {
                const response = await fetch('/api/simple-translate/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify({
                        text: text,
                        target_language: target
                    })
                });
                
                const data = await response.json();
                setStatus('simple-status', data.success ? 'success' : 'error');
                showResult('simple-result', data);
            } catch (error) {
                setStatus('simple-status', 'error');
                showResult('simple-result', `Error: ${error.message}`, true);
            }
        }
        
        async function testAutoTranslation() {
            setStatus('auto-status', 'loading');
            const text = document.getElementById('auto-text').value;
            const target = document.getElementById('auto-target').value;
            
            try {
                const response = await fetch('/api/auto-translate/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify({
                        text: text,
                        target_language: target
                    })
                });
                
                const data = await response.json();
                setStatus('auto-status', data.success ? 'success' : 'error');
                showResult('auto-result', data);
            } catch (error) {
                setStatus('auto-status', 'error');
                showResult('auto-result', `Error: ${error.message}`, true);
            }
        }
        
        async function testBatchTranslation() {
            setStatus('batch-status', 'loading');
            const textsInput = document.getElementById('batch-texts').value;
            const texts = textsInput.split('\n').filter(t => t.trim());
            
            try {
                const response = await fetch('/api/batch-translate/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify({
                        texts: texts,
                        target_language: 'fr',
                        source_language: 'ar'
                    })
                });
                
                const data = await response.json();
                setStatus('batch-status', data.success ? 'success' : 'error');
                showResult('batch-result', data);
            } catch (error) {
                setStatus('batch-status', 'error');
                showResult('batch-result', `Error: ${error.message}`, true);
            }
        }
        
        async function testTranslationStats() {
            setStatus('stats-status', 'loading');
            try {
                const response = await fetch('/api/stats-simple/');
                const data = await response.json();
                setStatus('stats-status', data.success ? 'success' : 'error');
                showResult('stats-result', data);
            } catch (error) {
                setStatus('stats-status', 'error');
                showResult('stats-result', `Error: ${error.message}`, true);
            }
        }
        
        async function testDebugRequest() {
            setStatus('debug-status', 'loading');
            try {
                const response = await fetch('/api/debug/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify({
                        test: 'debug request',
                        timestamp: new Date().toISOString()
                    })
                });
                
                const data = await response.json();
                setStatus('debug-status', data.success ? 'success' : 'error');
                showResult('debug-result', data);
            } catch (error) {
                setStatus('debug-status', 'error');
                showResult('debug-result', `Error: ${error.message}`, true);
            }
        }
        
        async function testAllAPIs() {
            await testAPIHealth();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testSimpleTranslation();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAutoTranslation();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testBatchTranslation();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testTranslationStats();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testDebugRequest();
        }
        
        function clearAllResults() {
            const resultElements = document.querySelectorAll('.api-result');
            resultElements.forEach(element => {
                element.textContent = '';
            });
            
            const statusElements = document.querySelectorAll('.status-indicator');
            statusElements.forEach(element => {
                element.className = 'status-indicator';
            });
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-test health on page load
            testAPIHealth();
        });
    </script>
</body>
</html>
