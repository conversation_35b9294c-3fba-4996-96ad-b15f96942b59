from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from sales.models import Customer, Category, Product, Invoice, InvoiceItem
from decimal import Decimal
import random
from django.utils import timezone
from datetime import timedelta


class Command(BaseCommand):
    help = 'Load sample data for testing the sales system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Loading sample data...'))
        
        # Create sample categories
        categories_data = [
            {'name': 'إلكترونيات', 'description': 'أجهزة إلكترونية متنوعة'},
            {'name': 'ملابس', 'description': 'ملابس رجالية ونسائية'},
            {'name': 'مواد غذائية', 'description': 'مواد غذائية أساسية'},
            {'name': 'أدوات منزلية', 'description': 'أدوات وأجهزة منزلية'},
            {'name': 'كتب ومكتبة', 'description': 'كتب وأدوات مكتبية'},
        ]
        
        categories = []
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            categories.append(category)
            if created:
                self.stdout.write(f'Created category: {category.name}')
        
        # Create sample products
        products_data = [
            {'name': 'هاتف ذكي', 'category': 'إلكترونيات', 'price': 15000, 'stock': 50},
            {'name': 'لابتوب', 'category': 'إلكترونيات', 'price': 45000, 'stock': 20},
            {'name': 'قميص قطني', 'category': 'ملابس', 'price': 800, 'stock': 100},
            {'name': 'بنطلون جينز', 'category': 'ملابس', 'price': 1200, 'stock': 75},
            {'name': 'أرز أبيض 5 كيلو', 'category': 'مواد غذائية', 'price': 600, 'stock': 200},
            {'name': 'زيت طبخ 1 لتر', 'category': 'مواد غذائية', 'price': 300, 'stock': 150},
            {'name': 'مكنسة كهربائية', 'category': 'أدوات منزلية', 'price': 3500, 'stock': 30},
            {'name': 'طقم أواني طبخ', 'category': 'أدوات منزلية', 'price': 2500, 'stock': 40},
            {'name': 'كتاب تعليمي', 'category': 'كتب ومكتبة', 'price': 250, 'stock': 80},
            {'name': 'دفتر ملاحظات', 'category': 'كتب ومكتبة', 'price': 100, 'stock': 120},
        ]
        
        products = []
        for prod_data in products_data:
            category = Category.objects.get(name=prod_data['category'])
            product, created = Product.objects.get_or_create(
                name=prod_data['name'],
                defaults={
                    'category': category,
                    'price': Decimal(str(prod_data['price'])),
                    'stock_quantity': prod_data['stock'],
                    'description': f'وصف {prod_data["name"]}',
                    'is_active': True
                }
            )
            products.append(product)
            if created:
                self.stdout.write(f'Created product: {product.name}')
        
        # Create sample customers
        customers_data = [
            {'name': 'أحمد محمد', 'email': '<EMAIL>', 'phone': '+222 12345678'},
            {'name': 'فاطمة علي', 'email': '<EMAIL>', 'phone': '+222 87654321'},
            {'name': 'محمد عبدالله', 'email': '<EMAIL>', 'phone': '+222 11223344'},
            {'name': 'عائشة حسن', 'email': '<EMAIL>', 'phone': '+222 44332211'},
            {'name': 'عمر إبراهيم', 'email': '<EMAIL>', 'phone': '+222 55667788'},
            {'name': 'خديجة أحمد', 'email': '<EMAIL>', 'phone': '+222 99887766'},
            {'name': 'يوسف محمود', 'email': '<EMAIL>', 'phone': '+222 33445566'},
            {'name': 'مريم سالم', 'email': '<EMAIL>', 'phone': '+222 77889900'},
        ]
        
        customers = []
        for cust_data in customers_data:
            customer, created = Customer.objects.get_or_create(
                name=cust_data['name'],
                defaults={
                    'email': cust_data['email'],
                    'phone': cust_data['phone'],
                    'address': f'عنوان {cust_data["name"]}, نواكشوط، موريتانيا'
                }
            )
            customers.append(customer)
            if created:
                self.stdout.write(f'Created customer: {customer.name}')
        
        # Create sample invoices
        if User.objects.filter(is_superuser=True).exists():
            admin_user = User.objects.filter(is_superuser=True).first()
            
            for i in range(15):
                customer = random.choice(customers)
                
                # Create invoice
                invoice = Invoice.objects.create(
                    customer=customer,
                    user=admin_user,
                    payment_status=random.choice(['pending', 'paid', 'pending']),
                    discount_percentage=random.choice([0, 5, 10]),
                    tax_percentage=random.choice([0, 14]),  # 14% VAT in Mauritania
                    notes=f'فاتورة تجريبية رقم {i+1}'
                )
                
                # Add random products to invoice
                num_items = random.randint(1, 5)
                selected_products = random.sample(products, min(num_items, len(products)))
                
                for product in selected_products:
                    quantity = random.randint(1, 5)
                    InvoiceItem.objects.create(
                        invoice=invoice,
                        product=product,
                        quantity=quantity,
                        unit_price=product.price
                    )
                
                # Update invoice date to be within last 30 days
                days_ago = random.randint(0, 30)
                invoice.date_created = timezone.now() - timedelta(days=days_ago)
                invoice.save()
                
                self.stdout.write(f'Created invoice: {invoice.invoice_number}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully loaded sample data:\n'
                f'- {len(categories)} categories\n'
                f'- {len(products)} products\n'
                f'- {len(customers)} customers\n'
                f'- {Invoice.objects.count()} invoices'
            )
        )
