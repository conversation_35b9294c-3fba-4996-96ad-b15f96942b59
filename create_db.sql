-- Create SQLite database with all required tables

-- Django migrations table
CREATE TABLE IF NOT EXISTS django_migrations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    app VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    applied DATETIME NOT NULL
);

-- Auth user table (simplified)
CREATE TABLE IF NOT EXISTS auth_user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    password VARCHAR(128) NOT NULL,
    last_login DATETIME,
    is_superuser BOOLEAN NOT NULL,
    username VARCHAR(150) NOT NULL UNIQUE,
    first_name VA<PERSON><PERSON><PERSON>(150) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(150) NOT NULL,
    email VARCHAR(254) NOT NULL,
    is_staff BOOLEAN NOT NULL,
    is_active BOOLEAN NOT NULL,
    date_joined DATETIME NOT NULL
);

-- Sales app tables
CREATE TABLE IF NOT EXISTS sales_customer (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(200) NOT NULL,
    email VARCHAR(254),
    phone VARCHAR(20),
    address TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

CREATE TABLE IF NOT EXISTS sales_category (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at DATETIME NOT NULL
);

CREATE TABLE IF NOT EXISTS sales_product (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INTEGER NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    stock_quantity INTEGER NOT NULL,
    image VARCHAR(100),
    is_active BOOLEAN NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (category_id) REFERENCES sales_category (id)
);

CREATE TABLE IF NOT EXISTS sales_invoice (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number VARCHAR(20) NOT NULL UNIQUE,
    customer_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    date_created DATETIME NOT NULL,
    date_due DATE,
    payment_status VARCHAR(20) NOT NULL,
    notes TEXT,
    discount_percentage DECIMAL(5, 2) NOT NULL,
    tax_percentage DECIMAL(5, 2) NOT NULL,
    FOREIGN KEY (customer_id) REFERENCES sales_customer (id),
    FOREIGN KEY (user_id) REFERENCES auth_user (id)
);

CREATE TABLE IF NOT EXISTS sales_invoiceitem (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (invoice_id) REFERENCES sales_invoice (id),
    FOREIGN KEY (product_id) REFERENCES sales_product (id)
);

-- Supplier table
CREATE TABLE IF NOT EXISTS sales_supplier (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(200) NOT NULL,
    email VARCHAR(254),
    phone VARCHAR(20),
    address TEXT,
    contact_person VARCHAR(100),
    tax_number VARCHAR(50),
    payment_terms VARCHAR(100),
    is_active BOOLEAN NOT NULL,
    date_created DATETIME NOT NULL,
    date_updated DATETIME NOT NULL
);

-- Purchase invoice table
CREATE TABLE IF NOT EXISTS sales_purchaseinvoice (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    date_created DATETIME NOT NULL,
    date_due DATE NOT NULL,
    payment_status VARCHAR(20) NOT NULL,
    notes TEXT,
    discount_percentage DECIMAL(5, 2) NOT NULL,
    tax_percentage DECIMAL(5, 2) NOT NULL,
    FOREIGN KEY (supplier_id) REFERENCES sales_supplier (id),
    FOREIGN KEY (user_id) REFERENCES auth_user (id)
);

-- Purchase invoice item table
CREATE TABLE IF NOT EXISTS sales_purchaseinvoiceitem (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_invoice_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (purchase_invoice_id) REFERENCES sales_purchaseinvoice (id),
    FOREIGN KEY (product_id) REFERENCES sales_product (id)
);

-- Insert migration records
INSERT OR IGNORE INTO django_migrations (app, name, applied) VALUES 
('contenttypes', '0001_initial', datetime('now')),
('auth', '0001_initial', datetime('now')),
('sales', '0001_initial', datetime('now')),
('sales', '0002_purchaseinvoice_supplier_purchaseinvoiceitem_and_more', datetime('now'));
