#!/usr/bin/env python
"""Apply migration to fix missing tables"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
django.setup()

from django.core.management import execute_from_command_line
from django.db import connection

def apply_migration():
    """Apply migration to create missing tables"""
    try:
        print("🔧 تطبيق migration لإصلاح الجداول المفقودة...")
        
        # Apply the specific migration
        execute_from_command_line(['manage.py', 'migrate', 'sales', '0003'])
        
        print("✅ تم تطبيق migration بنجاح!")
        
        # Test the tables
        with connection.cursor() as cursor:
            cursor.execute("SHOW TABLES LIKE 'sales_%'")
            tables = cursor.fetchall()
            
            print("\n📋 الجداول الموجودة:")
            for table in tables:
                print(f"  ✅ {table[0]}")
                
            # Check supplier count
            cursor.execute("SELECT COUNT(*) FROM sales_supplier")
            supplier_count = cursor.fetchone()[0]
            print(f"\n👥 عدد الموردين: {supplier_count}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق migration: {e}")
        return False

if __name__ == '__main__':
    success = apply_migration()
    if success:
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("python manage.py runserver")
    sys.exit(0 if success else 1)
