{% load static %}
{% load i18n %}
<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}
<html lang="{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% trans "فاتورة" %} {{ invoice.invoice_number }}</title>
    
    <!-- Bootstrap CSS -->
    {% if LANGUAGE_CODE == 'ar' %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        }
        
        [lang="ar"] {
            font-family: 'Amiri', 'Times New Roman', serif;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .invoice-details {
            background-color: #f8f9fc;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .invoice-total {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
        
        .table {
            border: 1px solid #dee2e6;
        }
        
        .table th {
            background-color: #f8f9fc;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #667eea;
            border-radius: 10px;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        
        @media print {
            @page {
                size: A4;
                margin: 1cm;
            }

            /* توسيط الصفحة */
            html {
                height: 100% !important;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .no-print {
                display: none !important;
            }

            body {
                margin: 0 !important;
                padding: 20px 0 !important;
                font-size: 10px !important;
                line-height: 1.2 !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                min-height: 100vh !important;
            }

            .container-fluid {
                padding: 15px !important;
                max-width: 90% !important;
                margin: auto !important;
                transform: scale(0.85) !important;
                transform-origin: center center !important;
                background: white !important;
                box-shadow: 0 0 15px rgba(0,0,0,0.1) !important;
                border-radius: 8px !important;
                position: relative !important;
            }

            .company-info {
                margin-bottom: 10px !important;
                padding: 10px !important;
            }

            .company-info h2 {
                font-size: 14px !important;
                margin-bottom: 3px !important;
            }

            .company-info p {
                font-size: 8px !important;
                margin-bottom: 2px !important;
            }

            .invoice-header {
                background: #667eea !important;
                padding: 8px !important;
                margin-bottom: 8px !important;
            }

            .invoice-header h3 {
                font-size: 12px !important;
                margin-bottom: 2px !important;
            }

            .invoice-header p {
                font-size: 9px !important;
                margin-bottom: 1px !important;
            }

            .invoice-details {
                padding: 8px !important;
                margin-bottom: 8px !important;
            }

            .invoice-details h6 {
                font-size: 10px !important;
                margin-bottom: 5px !important;
            }

            .invoice-details p {
                font-size: 8px !important;
                margin-bottom: 2px !important;
            }

            .table {
                font-size: 8px !important;
                margin-bottom: 8px !important;
            }

            .table th,
            .table td {
                padding: 3px 2px !important;
                font-size: 7px !important;
                line-height: 1.1 !important;
            }

            .table th {
                font-size: 8px !important;
                font-weight: bold !important;
            }

            .invoice-total {
                padding: 8px !important;
            }

            .invoice-total .table {
                font-size: 8px !important;
            }

            .footer {
                margin-top: 10px !important;
                padding-top: 8px !important;
                font-size: 7px !important;
            }

            .logo {
                width: 40px !important;
                height: 40px !important;
            }

            .badge {
                font-size: 8px !important;
                padding: 2px 4px !important;
            }

            .row {
                margin: 0 !important;
            }

            .col-md-3, .col-md-6, .col-md-12 {
                padding: 2px !important;
            }

            /* منع تقسيم العناصر */
            .company-info,
            .invoice-header,
            .invoice-total {
                page-break-inside: avoid !important;
            }

            /* تصغير المسافات */
            h1, h2, h3, h4, h5, h6 {
                margin: 2px 0 !important;
                line-height: 1.1 !important;
            }

            .mb-1, .mb-2, .mb-3, .mb-4 {
                margin-bottom: 3px !important;
            }
        }
        
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 100px;
            color: rgba(0,0,0,0.05);
            z-index: -1;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <div class="no-print text-center mb-3">
        <button onclick="window.print()" class="btn btn-primary me-2">
            <i class="fas fa-print"></i> {% trans "طباعة عادية" %}
        </button>
        <button onclick="printCompact()" class="btn btn-success me-2">
            <i class="fas fa-compress-arrows-alt"></i> {% trans "طباعة متوسطة" %}
        </button>
        <button onclick="printCentered()" class="btn btn-info me-2">
            <i class="fas fa-align-center"></i> {% trans "طباعة متوسطة" %}
        </button>
        <a href="{% url 'sales:invoice_detail' invoice.pk %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> {% trans "العودة" %}
        </a>
    </div>

    <!-- Watermark -->
    {% if invoice.payment_status == 'paid' %}
        <div class="watermark">{% trans "مدفوعة" %}</div>
    {% elif invoice.payment_status == 'cancelled' %}
        <div class="watermark">{% trans "ملغاة" %}</div>
    {% endif %}

    <div class="container-fluid">
        <!-- Company Header -->
        <div class="company-info">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    <img src="{% static 'images/logo.jpg' %}" alt="Logo" class="logo">
                </div>
                <div class="col-md-6">
                    <h2 class="mb-1">{% trans "نظام إدارة المبيعات" %}</h2>
                    <p class="mb-1">{% trans "شركة متخصصة في إدارة المبيعات" %}</p>
                    <p class="mb-0">
                        <i class="fas fa-map-marker-alt"></i> نواكشوط، موريتانيا |
                        <i class="fas fa-phone"></i> +222 12345678 |
                        <i class="fas fa-envelope"></i> <EMAIL>
                    </p>
                </div>
                <div class="col-md-3 text-center">
                    <h3 class="text-primary mb-0">{% trans "فاتورة مبيعات" %}</h3>
                    <p class="mb-0">Sales Invoice</p>
                </div>
            </div>
        </div>

        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h3 class="mb-0">{% trans "فاتورة رقم" %}: {{ invoice.invoice_number }}</h3>
                    <p class="mb-0">{% trans "تاريخ الإصدار" %}: {{ invoice.date_created|date:"Y-m-d" }}</p>
                    {% if invoice.date_due %}
                        <p class="mb-0">{% trans "تاريخ الاستحقاق" %}: {{ invoice.date_due|date:"Y-m-d" }}</p>
                    {% endif %}
                </div>
                <div class="col-md-6 text-end">
                    <div class="badge fs-6 
                        {% if invoice.payment_status == 'paid' %}bg-success
                        {% elif invoice.payment_status == 'pending' %}bg-warning
                        {% else %}bg-danger{% endif %}">
                        {% if invoice.payment_status == 'paid' %}
                            <i class="fas fa-check"></i> {% trans "مدفوعة" %}
                        {% elif invoice.payment_status == 'pending' %}
                            <i class="fas fa-clock"></i> {% trans "في الانتظار" %}
                        {% else %}
                            <i class="fas fa-times"></i> {% trans "ملغاة" %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer and Invoice Details -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="invoice-details">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-user"></i> {% trans "فوتر إلى" %} / Bill To
                    </h6>
                    <p class="mb-1"><strong>{{ invoice.customer.name }}</strong></p>
                    {% if invoice.customer.email %}
                        <p class="mb-1"><i class="fas fa-envelope"></i> {{ invoice.customer.email }}</p>
                    {% endif %}
                    {% if invoice.customer.phone %}
                        <p class="mb-1"><i class="fas fa-phone"></i> {{ invoice.customer.phone }}</p>
                    {% endif %}
                    {% if invoice.customer.address %}
                        <p class="mb-0"><i class="fas fa-map-marker-alt"></i> {{ invoice.customer.address }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="invoice-details">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-info-circle"></i> {% trans "تفاصيل إضافية" %}
                    </h6>
                    <p class="mb-1"><strong>{% trans "المستخدم" %}:</strong> {{ invoice.user.get_full_name|default:invoice.user.username }}</p>
                    <p class="mb-1"><strong>{% trans "وقت الطباعة" %}:</strong> {{ "now"|date:"Y-m-d H:i" }}</p>
                    {% if invoice.notes %}
                        <p class="mb-0"><strong>{% trans "ملاحظات" %}:</strong> {{ invoice.notes }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Invoice Items -->
        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 5%;">#</th>
                        <th style="width: 35%;">{% trans "المنتج" %} / Product</th>
                        <th style="width: 25%;">{% trans "الوصف" %} / Description</th>
                        <th style="width: 10%;" class="text-center">{% trans "الكمية" %} / Qty</th>
                        <th style="width: 12.5%;" class="text-end">{% trans "سعر الوحدة" %} / Unit Price</th>
                        <th style="width: 12.5%;" class="text-end">{% trans "الإجمالي" %} / Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice_items %}
                    <tr>
                        <td class="text-center">{{ forloop.counter }}</td>
                        <td>
                            <strong>{{ item.product.name }}</strong>
                            <br>
                            <small class="text-muted">{{ item.product.category.name }}</small>
                        </td>
                        <td>{{ item.product.description|default:"-" }}</td>
                        <td class="text-center">{{ item.quantity }}</td>
                        <td class="text-end">{{ item.unit_price|floatformat:2 }}</td>
                        <td class="text-end"><strong>{{ item.total_price|floatformat:2 }}</strong></td>
                    </tr>
                    {% endfor %}
                    
                    <!-- Empty rows for better layout -->
                    {% if invoice_items|length < 10 %}
                        {% for i in "123456789"|make_list %}
                            {% if forloop.counter0 >= invoice_items|length and forloop.counter0 < 10 %}
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>

        <!-- Invoice Totals -->
        <div class="row">
            <div class="col-md-6">
                <!-- Payment Terms -->
                <div class="invoice-details">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-credit-card"></i> {% trans "شروط الدفع" %} / Payment Terms
                    </h6>
                    <p class="mb-1">{% trans "العملة" %}: {% trans "الأوقية الموريتانية" %} (MRU)</p>
                    <p class="mb-1">{% trans "طريقة الدفع" %}: {% trans "نقداً أو تحويل بنكي" %}</p>
                    {% if invoice.date_due %}
                        <p class="mb-0">{% trans "تاريخ الاستحقاق" %}: {{ invoice.date_due|date:"Y-m-d" }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="invoice-total">
                    <table class="table table-sm mb-0">
                        <tr>
                            <td><strong>{% trans "المجموع الفرعي" %} / Subtotal:</strong></td>
                            <td class="text-end">{{ invoice.subtotal|floatformat:2 }} {% trans "أوقية" %}</td>
                        </tr>
                        {% if invoice.discount_percentage > 0 %}
                        <tr>
                            <td><strong>{% trans "الخصم" %} / Discount ({{ invoice.discount_percentage }}%):</strong></td>
                            <td class="text-end text-danger">-{{ invoice.discount_amount|floatformat:2 }} {% trans "أوقية" %}</td>
                        </tr>
                        {% endif %}
                        {% if invoice.tax_percentage > 0 %}
                        <tr>
                            <td><strong>{% trans "الضريبة" %} / Tax ({{ invoice.tax_percentage }}%):</strong></td>
                            <td class="text-end">{{ invoice.tax_amount|floatformat:2 }} {% trans "أوقية" %}</td>
                        </tr>
                        {% endif %}
                        <tr style="border-top: 2px solid #2196f3;">
                            <td><strong style="font-size: 1.1em;">{% trans "الإجمالي النهائي" %} / TOTAL:</strong></td>
                            <td class="text-end"><strong style="font-size: 1.2em; color: #2196f3;">{{ invoice.total_amount|floatformat:2 }} {% trans "أوقية" %}</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="mb-1"><strong>{% trans "شكراً لتعاملكم معنا" %} / Thank you for your business!</strong></p>
            <p class="mb-0">
                {% trans "هذه فاتورة مُولدة إلكترونياً" %} | This is an electronically generated invoice
            </p>
            <p class="mb-0">
                {% trans "نظام إدارة المبيعات" %} | Sales Management System | {{ "now"|date:"Y" }}
            </p>
        </div>
    </div>

    <!-- Auto-print script -->
    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }

        // Print function
        function printInvoice() {
            window.print();
        }

        // Compact print function
        function printCompact() {
            // Add compact class to body
            document.body.classList.add('compact-print');

            // Add additional compact styles
            const style = document.createElement('style');
            style.innerHTML = `
                @media print {
                    @page {
                        margin: 0.5cm !important;
                    }

                    .compact-print {
                        padding: 10px 0 !important;
                        display: flex !important;
                        justify-content: center !important;
                        align-items: center !important;
                        min-height: 100vh !important;
                    }

                    .compact-print .container-fluid {
                        padding: 8px !important;
                        max-width: 85% !important;
                        transform: scale(0.8) !important;
                        transform-origin: center center !important;
                        margin: auto !important;
                        background: white !important;
                        border-radius: 5px !important;
                        box-shadow: 0 0 10px rgba(0,0,0,0.1) !important;
                    }

                    .compact-print .table th,
                    .compact-print .table td {
                        padding: 2px 1px !important;
                        font-size: 7px !important;
                    }

                    .compact-print .company-info {
                        padding: 6px !important;
                        margin-bottom: 6px !important;
                    }

                    .compact-print .invoice-header {
                        padding: 6px !important;
                        margin-bottom: 6px !important;
                    }

                    .compact-print .invoice-details {
                        padding: 6px !important;
                        margin-bottom: 6px !important;
                    }
                }
            `;
            document.head.appendChild(style);

            // Print
            window.print();

            // Remove compact class and styles after printing
            setTimeout(() => {
                document.body.classList.remove('compact-print');
                document.head.removeChild(style);
            }, 1000);
        }

        // Centered print function
        function printCentered() {
            // Add centered class to body
            document.body.classList.add('centered-print');

            // Add centered print styles
            const style = document.createElement('style');
            style.innerHTML = `
                @media print {
                    @page {
                        margin: 1.5cm !important;
                    }

                    .centered-print {
                        padding: 30px 0 !important;
                        display: flex !important;
                        justify-content: center !important;
                        align-items: center !important;
                        min-height: 100vh !important;
                    }

                    .centered-print .container-fluid {
                        padding: 20px !important;
                        max-width: 80% !important;
                        transform: scale(0.9) !important;
                        transform-origin: center center !important;
                        margin: auto !important;
                        background: white !important;
                        border: 2px solid #667eea !important;
                        border-radius: 10px !important;
                        box-shadow: 0 0 20px rgba(0,0,0,0.15) !important;
                    }
                }
            `;
            document.head.appendChild(style);

            // Print
            window.print();

            // Remove centered class and styles after printing
            setTimeout(() => {
                document.body.classList.remove('centered-print');
                document.head.removeChild(style);
            }, 1000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+P for normal print
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Ctrl+Shift+P for compact print
            if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                printCompact();
            }
            // Ctrl+Alt+P for centered print
            if (e.ctrlKey && e.altKey && e.key === 'p') {
                e.preventDefault();
                printCentered();
            }
        });
    </script>
</body>
</html>
