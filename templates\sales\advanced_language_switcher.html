{% load i18n %}
{% load translation_extras %}

<!-- Advanced Language Switcher with Full Translation -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i> 
        <span id="current-lang-display">
            {% if request.session.django_language == 'fr' %}
                Français
            {% else %}
                العربية
            {% endif %}
        </span>
    </a>
    <ul class="dropdown-menu" id="lang-dropdown">
        {% if request.session.django_language == 'fr' %}
            <li>
                <a href="#" class="dropdown-item advanced-lang-switch" data-lang="ar" data-name="العربية" data-dir="rtl">
                    <i class="fas fa-language"></i> العربية
                </a>
            </li>
        {% else %}
            <li>
                <a href="#" class="dropdown-item advanced-lang-switch" data-lang="fr" data-name="Français" data-dir="ltr">
                    <i class="fas fa-language"></i> Français
                </a>
            </li>
        {% endif %}
    </ul>
</li>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Translation dictionary
    const translations = {
        'ar': {
            // Navigation - French to Arabic
            'Tableau de Bord': 'لوحة التحكم',
            'Clients': 'العملاء',
            'Produits': 'المنتجات',
            'Fournisseurs': 'الموردين',
            'Factures': 'الفواتير',
            'Rapports': 'التقارير',
            'Système de Gestion des Ventes': 'نظام إدارة المبيعات',

            // Navigation - Arabic (keep same)
            'نظام إدارة المبيعات': 'نظام إدارة المبيعات',
            'لوحة التحكم': 'لوحة التحكم',
            'العملاء': 'العملاء',
            'المنتجات': 'المنتجات',
            'الفواتير': 'الفواتير',
            'التقارير': 'التقارير',
            'الموردين': 'الموردين',
            'الفئات': 'الفئات',
            
            // Actions - French to Arabic
            'Ajouter': 'إضافة',
            'Modifier': 'تعديل',
            'Supprimer': 'حذف',
            'Voir': 'عرض',
            'Rechercher': 'بحث',
            'Enregistrer': 'حفظ',
            'Annuler': 'إلغاء',
            'Imprimer': 'طباعة',

            // Actions - Arabic (keep same)
            'إضافة': 'إضافة',
            'تعديل': 'تعديل',
            'حذف': 'حذف',
            'عرض': 'عرض',
            'بحث': 'بحث',
            'حفظ': 'حفظ',
            'إلغاء': 'إلغاء',
            'طباعة': 'طباعة',
            
            // Customer related
            'قائمة العملاء': 'قائمة العملاء',
            'إضافة عميل': 'إضافة عميل',
            'اسم العميل': 'اسم العميل',
            
            // Product related
            'قائمة المنتجات': 'قائمة المنتجات',
            'إضافة منتج جديد': 'إضافة منتج جديد',
            'اسم المنتج': 'اسم المنتج',
            'السعر': 'السعر',
            'الكمية': 'الكمية',
            'الوصف': 'الوصف',
            'الباركود': 'الباركود',
            
            // Invoice related
            'قائمة الفواتير': 'قائمة الفواتير',
            'إضافة فاتورة جديدة': 'إضافة فاتورة جديدة',
            'فواتير البيع': 'فواتير البيع',
            'فواتير الشراء': 'فواتير الشراء',
            'قائمة فواتير الشراء': 'قائمة فواتير الشراء',
            'إضافة فاتورة شراء': 'إضافة فاتورة شراء',
            'رقم الفاتورة': 'رقم الفاتورة',
            'التاريخ': 'التاريخ',
            'المبلغ': 'المبلغ',
            'الحالة': 'الحالة',
            
            // Supplier related
            'قائمة الموردين': 'قائمة الموردين',
            'إضافة مورد جديد': 'إضافة مورد جديد',
            
            // Reports
            'التقارير العامة': 'التقارير العامة',
            'التقارير المتقدمة': 'التقارير المتقدمة',
            
            // User menu
            'لوحة الإدارة': 'لوحة الإدارة',
            'تسجيل الخروج': 'تسجيل الخروج',
            
            // Footer
            'جميع الحقوق محفوظة': 'جميع الحقوق محفوظة'
        },
        'fr': {
            // Navigation - Arabic to French
            'نظام إدارة المبيعات': 'Système de Gestion des Ventes',
            'لوحة التحكم': 'Tableau de Bord',
            'العملاء': 'Clients',
            'المنتجات': 'Produits',
            'الفواتير': 'Factures',
            'التقارير': 'Rapports',
            'الموردين': 'Fournisseurs',
            'الفئات': 'Catégories',

            // Navigation - French (keep same)
            'Tableau de Bord': 'Tableau de Bord',
            'Clients': 'Clients',
            'Produits': 'Produits',
            'Fournisseurs': 'Fournisseurs',
            'Factures': 'Factures',
            'Rapports': 'Rapports',
            'Système de Gestion des Ventes': 'Système de Gestion des Ventes',
            
            // Actions - Arabic to French
            'إضافة': 'Ajouter',
            'تعديل': 'Modifier',
            'حذف': 'Supprimer',
            'عرض': 'Voir',
            'بحث': 'Rechercher',
            'حفظ': 'Enregistrer',
            'إلغاء': 'Annuler',
            'طباعة': 'Imprimer',

            // Actions - French (keep same)
            'Ajouter': 'Ajouter',
            'Modifier': 'Modifier',
            'Supprimer': 'Supprimer',
            'Voir': 'Voir',
            'Rechercher': 'Rechercher',
            'Enregistrer': 'Enregistrer',
            'Annuler': 'Annuler',
            'Imprimer': 'Imprimer',
            
            // Customer related
            'قائمة العملاء': 'Liste des Clients',
            'إضافة عميل': 'Ajouter un Client',
            'اسم العميل': 'Nom du Client',
            
            // Product related
            'قائمة المنتجات': 'Liste des Produits',
            'إضافة منتج جديد': 'Ajouter un Nouveau Produit',
            'اسم المنتج': 'Nom du Produit',
            'السعر': 'Prix',
            'الكمية': 'Quantité',
            'الوصف': 'Description',
            'الباركود': 'Code-barres',
            
            // Invoice related
            'قائمة الفواتير': 'Liste des Factures',
            'إضافة فاتورة جديدة': 'Ajouter une Nouvelle Facture',
            'فواتير البيع': 'Factures de Vente',
            'فواتير الشراء': 'Factures d\'Achat',
            'قائمة فواتير الشراء': 'Liste des Factures d\'Achat',
            'إضافة فاتورة شراء': 'Ajouter une Facture d\'Achat',
            'رقم الفاتورة': 'Numéro de Facture',
            'التاريخ': 'Date',
            'المبلغ': 'Montant',
            'الحالة': 'Statut',
            
            // Supplier related
            'قائمة الموردين': 'Liste des Fournisseurs',
            'إضافة مورد جديد': 'Ajouter un Nouveau Fournisseur',
            
            // Reports
            'التقارير العامة': 'Rapports Généraux',
            'التقارير المتقدمة': 'Rapports Avancés',
            
            // User menu
            'لوحة الإدارة': 'Panneau d\'Administration',
            'تسجيل الخروج': 'Déconnexion',
            
            // Footer
            'جميع الحقوق محفوظة': 'Tous droits réservés'
        }
    };

    // Function to apply direction-specific styles with custom ordering
    function applyDirectionStyles(direction) {
        const navbar = document.querySelector('.navbar');
        const navbarNav = document.querySelector('.navbar-nav');
        const navbarBrand = document.querySelector('.navbar-brand');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        const containerFluid = document.querySelector('.container-fluid');

        if (direction === 'rtl') {
            // Apply RTL styles
            if (navbarNav) {
                navbarNav.style.flexDirection = 'row-reverse';
                navbarNav.style.display = 'flex';
            }
            if (navbarBrand) {
                navbarBrand.style.marginRight = '0';
                navbarBrand.style.marginLeft = 'auto';
            }
            if (navbarCollapse) {
                navbarCollapse.style.justifyContent = 'flex-start';
            }
            if (containerFluid) {
                containerFluid.style.flexDirection = 'row-reverse';
            }

            // Apply custom ordering for RTL (Arabic) - نظام إدارة المبيعات بجانب لوحة التحكم
            const navItems = document.querySelectorAll('.nav-item:not(.language-switcher)');
            navItems.forEach(function(item, index) {
                // Custom order: نظام إدارة المبيعات بجانب لوحة التحكم
                const rtlOrder = [7, 6, 5, 4, 3, 2, 1]; // [نظام إدارة المبيعات، لوحة التحكم، العملاء، المنتجات، الموردين، الفواتير، التقارير]
                if (index < rtlOrder.length) {
                    item.style.order = rtlOrder[index];
                }
                item.style.marginLeft = '0.5rem';
                item.style.marginRight = '0';
            });

            // Language switcher always at the end for RTL
            const languageSwitcher = document.querySelector('.language-switcher');
            if (languageSwitcher) {
                languageSwitcher.style.order = '-1';
            }

            // Apply RTL to nav links
            document.querySelectorAll('.nav-link').forEach(function(link) {
                link.style.textAlign = 'right';
            });
        } else {
            // Apply LTR styles
            if (navbarNav) {
                navbarNav.style.flexDirection = 'row';
                navbarNav.style.display = 'flex';
            }
            if (navbarBrand) {
                navbarBrand.style.marginLeft = '0';
                navbarBrand.style.marginRight = 'auto';
            }
            if (navbarCollapse) {
                navbarCollapse.style.justifyContent = 'flex-end';
            }
            if (containerFluid) {
                containerFluid.style.flexDirection = 'row';
            }

            // Apply normal ordering for LTR (French)
            const navItems = document.querySelectorAll('.nav-item:not(.language-switcher)');
            navItems.forEach(function(item, index) {
                item.style.order = index + 1;
                item.style.marginRight = '0.5rem';
                item.style.marginLeft = '0';
            });

            // Language switcher always at the end for LTR
            const languageSwitcher = document.querySelector('.language-switcher');
            if (languageSwitcher) {
                languageSwitcher.style.order = '99';
            }

            // Apply LTR to nav links
            document.querySelectorAll('.nav-link').forEach(function(link) {
                link.style.textAlign = 'left';
            });
        }
    }

    // Function to translate all text on page
    function translatePage(language) {
        const dict = translations[language];
        if (!dict) return;

        // Translate all text nodes recursively
        function translateElement(element) {
            if (element.nodeType === Node.TEXT_NODE) {
                const text = element.textContent.trim();
                if (text && dict[text]) {
                    element.textContent = dict[text];
                }
            } else {
                for (let child of element.childNodes) {
                    translateElement(child);
                }
            }
        }

        // Translate specific elements including navigation
        document.querySelectorAll('a, button, span:not(.fas):not(.fa), h1, h2, h3, h4, h5, h6, p, td, th, label, .nav-link, .dropdown-item').forEach(function(element) {
            // Skip elements with icons
            if (element.querySelector('.fas, .fa')) {
                // For elements with icons, only translate the text part
                const textNodes = [];
                function getTextNodes(node) {
                    if (node.nodeType === Node.TEXT_NODE) {
                        textNodes.push(node);
                    } else {
                        for (let child of node.childNodes) {
                            getTextNodes(child);
                        }
                    }
                }
                getTextNodes(element);

                textNodes.forEach(function(textNode) {
                    const text = textNode.textContent.trim();
                    if (text && dict[text]) {
                        textNode.textContent = dict[text];
                    }
                });
            } else {
                const text = element.textContent.trim();
                if (text && dict[text]) {
                    element.textContent = dict[text];
                }
            }
        });

        // Translate placeholders
        document.querySelectorAll('input[placeholder], textarea[placeholder]').forEach(function(element) {
            const placeholder = element.getAttribute('placeholder');
            if (placeholder && dict[placeholder]) {
                element.setAttribute('placeholder', dict[placeholder]);
            }
        });

        // Translate titles
        document.querySelectorAll('[title]').forEach(function(element) {
            const title = element.getAttribute('title');
            if (title && dict[title]) {
                element.setAttribute('title', dict[title]);
            }
        });
    }

    // Advanced language switch with full translation
    document.querySelectorAll('.advanced-lang-switch').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const language = this.getAttribute('data-lang');
            const langName = this.getAttribute('data-name');
            const direction = this.getAttribute('data-dir');
            const currentDisplay = document.getElementById('current-lang-display');
            const dropdown = document.getElementById('lang-dropdown');
            
            // Immediate visual feedback
            currentDisplay.textContent = langName;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
            
            // Change page direction immediately
            document.documentElement.setAttribute('dir', direction);
            document.documentElement.setAttribute('lang', language);

            // Apply direction-specific classes immediately
            applyDirectionStyles(direction);

            // Translate page content immediately
            translatePage(language);
            
            // Update dropdown content immediately
            if (language === 'ar') {
                dropdown.innerHTML = `
                    <li>
                        <a href="#" class="dropdown-item advanced-lang-switch" data-lang="fr" data-name="Français" data-dir="ltr">
                            <i class="fas fa-language"></i> Français
                        </a>
                    </li>
                `;
            } else {
                dropdown.innerHTML = `
                    <li>
                        <a href="#" class="dropdown-item advanced-lang-switch" data-lang="ar" data-name="العربية" data-dir="rtl">
                            <i class="fas fa-language"></i> العربية
                        </a>
                    </li>
                `;
            }
            
            // Re-attach event listeners to new dropdown items
            dropdown.querySelectorAll('.advanced-lang-switch').forEach(function(newLink) {
                newLink.addEventListener('click', arguments.callee);
            });
            
            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/set-language/';
            form.style.display = 'none';
            
            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
            form.appendChild(csrfInput);
            
            // Add language
            const langInput = document.createElement('input');
            langInput.type = 'hidden';
            langInput.name = 'language';
            langInput.value = language;
            form.appendChild(langInput);
            
            // Add next URL
            const nextInput = document.createElement('input');
            nextInput.type = 'hidden';
            nextInput.name = 'next';
            nextInput.value = window.location.pathname;
            form.appendChild(nextInput);
            
            // Submit form
            document.body.appendChild(form);
            form.submit();
        });
    });
});
</script>

<style>
.advanced-lang-switch {
    cursor: pointer;
    transition: background-color 0.2s;
}
.advanced-lang-switch:hover {
    background-color: #f8f9fa !important;
}
.language-switcher .dropdown-toggle {
    transition: all 0.2s ease;
}

/* Smooth direction transition */
html {
    transition: all 0.3s ease;
}

/* Direction-specific styles for navbar with custom ordering */
[dir="rtl"] .navbar-nav {
    flex-direction: row-reverse !important;
    display: flex !important;
}

[dir="ltr"] .navbar-nav {
    flex-direction: row !important;
    display: flex !important;
}

/* Custom ordering for RTL - Arabic (نظام إدارة المبيعات بجانب لوحة التحكم) */
[dir="rtl"] .nav-item:nth-child(1):not(.language-switcher) { order: 7 !important; } /* نظام إدارة المبيعات - بجانب لوحة التحكم */
[dir="rtl"] .nav-item:nth-child(2):not(.language-switcher) { order: 6 !important; } /* لوحة التحكم */
[dir="rtl"] .nav-item:nth-child(3):not(.language-switcher) { order: 5 !important; } /* العملاء */
[dir="rtl"] .nav-item:nth-child(4):not(.language-switcher) { order: 4 !important; } /* المنتجات */
[dir="rtl"] .nav-item:nth-child(5):not(.language-switcher) { order: 3 !important; } /* الموردين */
[dir="rtl"] .nav-item:nth-child(6):not(.language-switcher) { order: 2 !important; } /* الفواتير */
[dir="rtl"] .nav-item:nth-child(7):not(.language-switcher) { order: 1 !important; } /* التقارير */

/* Normal ordering for LTR - French */
[dir="ltr"] .nav-item:nth-child(1):not(.language-switcher) { order: 1 !important; }
[dir="ltr"] .nav-item:nth-child(2):not(.language-switcher) { order: 2 !important; }
[dir="ltr"] .nav-item:nth-child(3):not(.language-switcher) { order: 3 !important; }
[dir="ltr"] .nav-item:nth-child(4):not(.language-switcher) { order: 4 !important; }
[dir="ltr"] .nav-item:nth-child(5):not(.language-switcher) { order: 5 !important; }
[dir="ltr"] .nav-item:nth-child(6):not(.language-switcher) { order: 6 !important; }
[dir="ltr"] .nav-item:nth-child(7):not(.language-switcher) { order: 7 !important; }

/* Language switcher positioning */
[dir="rtl"] .language-switcher {
    order: -1 !important;
}

[dir="ltr"] .language-switcher {
    order: 99 !important;
}

/* Navbar brand positioning */
[dir="rtl"] .navbar-brand {
    margin-right: 0 !important;
    margin-left: auto !important;
}

[dir="ltr"] .navbar-brand {
    margin-left: 0 !important;
    margin-right: auto !important;
}

/* Navbar collapse positioning */
[dir="rtl"] .navbar-collapse {
    justify-content: flex-start !important;
}

[dir="ltr"] .navbar-collapse {
    justify-content: flex-end !important;
}

/* Navigation items spacing */
[dir="rtl"] .nav-item {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="ltr"] .nav-item {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

/* Language switching animation */
.switching {
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

/* Bootstrap RTL/LTR specific adjustments */
[dir="rtl"] .dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

[dir="ltr"] .dropdown-menu {
    left: 0 !important;
    right: auto !important;
}

/* Text direction transitions */
[dir="rtl"] .navbar-nav .nav-link {
    text-align: right !important;
}

[dir="ltr"] .navbar-nav .nav-link {
    text-align: left !important;
}

/* Container direction */
[dir="rtl"] .container-fluid {
    flex-direction: row-reverse !important;
}

[dir="ltr"] .container-fluid {
    flex-direction: row !important;
}

/* Navbar toggler positioning */
[dir="rtl"] .navbar-toggler {
    margin-left: 0 !important;
    margin-right: auto !important;
}

[dir="ltr"] .navbar-toggler {
    margin-right: 0 !important;
    margin-left: auto !important;
}
</style>
