{% load translation_extras %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تبديل اللغة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>{% trans_fallback "اختبار تبديل اللغة" %}</h3>
                        <p>{% get_current_language_code as current_lang %}
                        {% trans_fallback "اللغة الحالية" %}: {{ current_lang }}</p>
                    </div>
                    <div class="card-body">
                        <h4>{% trans_fallback "نصوص للاختبار" %}:</h4>
                        <ul>
                            <li>{% trans_fallback "نظام إدارة المبيعات" %}</li>
                            <li>{% trans_fallback "لوحة التحكم" %}</li>
                            <li>{% trans_fallback "العملاء" %}</li>
                            <li>{% trans_fallback "المنتجات" %}</li>
                            <li>{% trans_fallback "الفواتير" %}</li>
                            <li>{% trans_fallback "التقارير" %}</li>
                            <li>{% trans_fallback "الباركود" %}</li>
                        </ul>
                        
                        <hr>
                        
                        <h4>{% trans_fallback "تبديل اللغة" %}:</h4>
                        
                        <!-- Working Language Switcher -->
                        <div class="btn-group" role="group">
                            {% get_current_language_code as current_lang %}
                            {% if current_lang != 'ar' %}
                                <form method="post" action="/set-language/" class="d-inline">
                                    {% csrf_token %}
                                    <input type="hidden" name="language" value="ar">
                                    <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                    <button type="submit" class="btn btn-primary btn-lg working-test-btn">
                                        <i class="fas fa-language"></i> العربية
                                    </button>
                                </form>
                            {% endif %}

                            {% if current_lang != 'fr' %}
                                <form method="post" action="/set-language/" class="d-inline">
                                    {% csrf_token %}
                                    <input type="hidden" name="language" value="fr">
                                    <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                    <button type="submit" class="btn btn-success btn-lg working-test-btn">
                                        <i class="fas fa-language"></i> Français
                                    </button>
                                </form>
                            {% endif %}
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                {% trans_fallback "انقر على اللغة للتبديل الفوري" %}
                            </small>
                        </div>
                        
                        <hr>
                        
                        <a href="/" class="btn btn-secondary">
                            <i class="fas fa-home"></i> {% trans_fallback "العودة للرئيسية" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Working feedback for test page
        document.querySelectorAll('.working-test-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                // Show feedback when form is submitted
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
                this.disabled = true;
            });
        });
    });
    </script>
</body>
</html>
