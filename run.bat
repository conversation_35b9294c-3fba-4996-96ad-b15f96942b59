@echo off
chcp 65001 >nul
echo ========================================
echo Sales Management System - نظام إدارة المبيعات
echo ========================================
echo.

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Error: Failed to create virtual environment
        echo Please ensure Python is installed correctly
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created successfully
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate

REM Install requirements
echo 📥 Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Error: Failed to install requirements
    echo Please check your internet connection
    pause
    exit /b 1
)
echo ✅ Requirements installed successfully

REM Check if .env file exists
if not exist ".env" (
    echo.
    echo ========================================
    echo WARNING: .env file not found!
    echo تحذير: ملف .env غير موجود!
    echo ========================================
    echo Please create .env file with your database settings:
    echo يرجى إنشاء ملف .env مع إعدادات قاعدة البيانات:
    echo.
    echo SECRET_KEY=your-secret-key-here
    echo DEBUG=True
    echo DB_NAME=sales_system
    echo DB_USER=root
    echo DB_PASSWORD=your_password
    echo DB_HOST=localhost
    echo DB_PORT=3306
    echo.
    pause
    exit /b 1
)

REM Run migrations
echo Running migrations...
python manage.py makemigrations
python manage.py migrate
if errorlevel 1 (
    echo Error: Failed to run migrations
    echo Please check your database settings in .env file
    pause
    exit /b 1
)

REM Collect static files
echo Collecting static files...
python manage.py collectstatic --noinput

REM Compile messages
echo Compiling translation messages...
python manage.py compilemessages

REM Check if superuser exists
echo.
echo ========================================
echo Checking for superuser...
echo التحقق من وجود مستخدم إداري...
echo ========================================
python manage.py shell -c "from django.contrib.auth.models import User; print('Superuser exists' if User.objects.filter(is_superuser=True).exists() else 'No superuser found - please create one')"

echo.
echo Do you want to load sample data? (y/n)
echo هل تريد تحميل بيانات تجريبية؟ (y/n)
set /p load_data=
if /i "%load_data%"=="y" (
    echo Loading sample data...
    python manage.py load_sample_data
)

echo.
echo ========================================
echo Starting Django development server...
echo بدء تشغيل خادم Django...
echo ========================================
echo.
echo Open your browser and go to: http://127.0.0.1:8000
echo افتح المتصفح واذهب إلى: http://127.0.0.1:8000
echo.
echo Press Ctrl+C to stop the server
echo اضغط Ctrl+C لإيقاف الخادم
echo.

python manage.py runserver

pause
