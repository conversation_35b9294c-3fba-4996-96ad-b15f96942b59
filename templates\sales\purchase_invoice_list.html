{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}فواتير الشراء - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">فواتير الشراء</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice-dollar"></i> فواتير الشراء
                </h5>
                <a href="{% url 'sales:purchase_invoice_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إنشاء فاتورة شراء جديدة
                </a>
            </div>
            <div class="card-body">
                <!-- Search and Filter Form -->
                <form method="get" class="mb-4">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" 
                                       placeholder="البحث برقم الفاتورة أو اسم المورد..." value="{{ request.GET.search }}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select" onchange="this.form.submit()">
                                <option value="">جميع الحالات</option>
                                <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>في الانتظار</option>
                                <option value="partial" {% if request.GET.status == 'partial' %}selected{% endif %}>دفع جزئي</option>
                                <option value="paid" {% if request.GET.status == 'paid' %}selected{% endif %}>مدفوعة</option>
                                <option value="overdue" {% if request.GET.status == 'overdue' %}selected{% endif %}>متأخرة</option>
                            </select>
                        </div>
                    </div>
                </form>

                <!-- Purchase Invoices Table -->
                {% if purchase_invoices %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>المورد</th>
                                <th>تاريخ الإنشاء</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>المبلغ الإجمالي</th>
                                <th>حالة الدفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for purchase_invoice in purchase_invoices %}
                            <tr>
                                <td>
                                    <strong>{{ purchase_invoice.invoice_number }}</strong>
                                </td>
                                <td>{{ purchase_invoice.supplier.name }}</td>
                                <td>{{ purchase_invoice.date_created|date:"Y-m-d" }}</td>
                                <td>{{ purchase_invoice.date_due|date:"Y-m-d" }}</td>
                                <td>
                                    <strong>{{ purchase_invoice.total_amount|floatformat:2 }} أوقية</strong>
                                </td>
                                <td>
                                    {% if purchase_invoice.payment_status == 'pending' %}
                                        <span class="badge bg-warning">في الانتظار</span>
                                    {% elif purchase_invoice.payment_status == 'partial' %}
                                        <span class="badge bg-info">دفع جزئي</span>
                                    {% elif purchase_invoice.payment_status == 'paid' %}
                                        <span class="badge bg-success">مدفوعة</span>
                                    {% elif purchase_invoice.payment_status == 'overdue' %}
                                        <span class="badge bg-danger">متأخرة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'sales:purchase_invoice_detail' purchase_invoice.pk %}" 
                                           class="btn btn-sm btn-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'sales:purchase_invoice_edit' purchase_invoice.pk %}" 
                                           class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'sales:purchase_invoice_print' purchase_invoice.pk %}" 
                                           class="btn btn-sm btn-secondary" title="طباعة" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        <a href="{% url 'sales:purchase_invoice_delete' purchase_invoice.pk %}" 
                                           class="btn btn-sm btn-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الفاتورة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice-dollar fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد فواتير شراء</h5>
                    <p class="text-muted">ابدأ بإنشاء فاتورة شراء جديدة</p>
                    <a href="{% url 'sales:purchase_invoice_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إنشاء فاتورة شراء جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
