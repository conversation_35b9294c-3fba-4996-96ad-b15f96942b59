@echo off
color 0E
echo ========================================
echo 🔧 الحل النهائي لمشكلة قاعدة البيانات
echo ========================================
echo.
echo ❌ المشكلة: Table 'sales_purchaseinvoiceitem' doesn't exist
echo.
echo 🎯 الحلول المتاحة:
echo.
echo 1. الحل المباشر (Python)
echo 2. تطبيق Migration
echo 3. الحل اليدوي (phpMyAdmin)
echo.
set /p choice="اختر الحل (1/2/3): "

if "%choice%"=="1" goto direct_fix
if "%choice%"=="2" goto migration_fix
if "%choice%"=="3" goto manual_fix
goto invalid_choice

:direct_fix
echo.
echo 🔧 تطبيق الحل المباشر...
python direct_fix.py
goto end

:migration_fix
echo.
echo 🔧 تطبيق Migration...
python apply_migration.py
goto end

:manual_fix
echo.
echo 📋 الحل اليدوي:
echo.
echo 1. افتح phpMyAdmin: http://localhost/phpmyadmin/
echo 2. اختر قاعدة البيانات: sales_system
echo 3. انقر تبويب SQL
echo 4. انسخ محتوى ملف URGENT_FIX.sql
echo 5. الصق والنقر Go
echo.
pause
goto end

:invalid_choice
echo.
echo ❌ اختيار غير صحيح!
pause
goto end

:end
echo.
echo 🚀 تشغيل النظام...
python manage.py runserver
pause
