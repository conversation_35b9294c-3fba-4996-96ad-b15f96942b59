-- ========================================
-- إعداد قاعدة البيانات لنظام إدارة المبيعات
-- ========================================
-- انسخ هذا الكود والصقه في phpMyAdmin
-- تأكد من اختيار قاعدة البيانات sales_system أولاً

-- حذف الجداول إذا كانت موجودة (لإعادة الإنشاء)
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `sales_purchaseinvoiceitem`;
DROP TABLE IF EXISTS `sales_purchaseinvoice`;
DROP TABLE IF EXISTS `sales_supplier`;
SET FOREIGN_KEY_CHECKS = 1;

-- إن<PERSON>ا<PERSON> جدول الموردين
CREATE TABLE `sales_supplier` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `email` varchar(254) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `contact_person` varchar(100) DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `payment_terms` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `date_created` datetime NOT NULL DEFAULT current_timestamp(),
  `date_updated` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_supplier_name` (`name`),
  KEY `idx_supplier_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول فواتير الشراء
CREATE TABLE `sales_purchaseinvoice` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(50) NOT NULL,
  `supplier_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `date_created` datetime NOT NULL DEFAULT current_timestamp(),
  `date_due` date NOT NULL,
  `payment_status` varchar(20) NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `discount_percentage` decimal(5,2) NOT NULL DEFAULT 0.00,
  `tax_percentage` decimal(5,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invoice_number` (`invoice_number`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_date_created` (`date_created`),
  CONSTRAINT `fk_purchaseinvoice_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `sales_supplier` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_purchaseinvoice_user` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول عناصر فواتير الشراء
CREATE TABLE `sales_purchaseinvoiceitem` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_invoice_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL CHECK (`quantity` > 0),
  `unit_price` decimal(10,2) NOT NULL CHECK (`unit_price` >= 0),
  PRIMARY KEY (`id`),
  KEY `idx_purchase_invoice_id` (`purchase_invoice_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_purchaseinvoiceitem_invoice` FOREIGN KEY (`purchase_invoice_id`) REFERENCES `sales_purchaseinvoice` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_purchaseinvoiceitem_product` FOREIGN KEY (`product_id`) REFERENCES `sales_product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية للموردين
INSERT IGNORE INTO `sales_supplier` (`name`, `email`, `phone`, `contact_person`, `is_active`) VALUES
('شركة التوريدات الذهبية', '<EMAIL>', '+222-45-123456', 'أحمد ولد محمد', 1),
('مؤسسة النور للتجارة', '<EMAIL>', '+222-45-654321', 'فاطمة بنت أحمد', 1),
('شركة الصحراء للمواد الغذائية', '<EMAIL>', '+222-45-789012', 'محمد ولد عبدالله', 1),
('شركة الأطلس للإلكترونيات', '<EMAIL>', '+222-45-345678', 'عبدالرحمن ولد أحمد', 1),
('مؤسسة الرياض للمواد الإنشائية', '<EMAIL>', '+222-45-567890', 'خديجة بنت محمد', 1);

-- ========================================
-- التحقق من إنشاء الجداول
-- ========================================

-- عرض الجداول المنشأة
SELECT 'تم إنشاء الجداول التالية:' as message;
SHOW TABLES LIKE 'sales_%';

-- التحقق من بنية الجداول
SELECT 'بنية جدول الموردين:' as message;
DESCRIBE sales_supplier;

SELECT 'بنية جدول فواتير الشراء:' as message;
DESCRIBE sales_purchaseinvoice;

SELECT 'بنية جدول عناصر فواتير الشراء:' as message;
DESCRIBE sales_purchaseinvoiceitem;

-- ========================================
-- تحديث جدول migrations في Django
-- ========================================
INSERT IGNORE INTO `django_migrations` (`app`, `name`, `applied`) VALUES
('sales', '0003_add_suppliers_and_purchases', NOW());

-- ========================================
-- عرض النتائج النهائية
-- ========================================
SELECT 'عدد الموردين المضافين:' as message, COUNT(*) as count FROM sales_supplier;
SELECT 'الجداول الموجودة:' as message;
SELECT TABLE_NAME as 'اسم الجدول', TABLE_ROWS as 'عدد الصفوف'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME LIKE 'sales_%'
ORDER BY TABLE_NAME;

SELECT '✅ تم إعداد قاعدة البيانات بنجاح!' as 'النتيجة';
