{% load static %}
{% load translation_extras %}

<!-- Bidirectional Language Switcher with Real-time Translation -->
<div class="bidirectional-lang-switcher" id="bidirectional-switcher">
    <!-- Current Language Display -->
    <div class="current-lang-display" id="current-lang-btn">
        <div class="lang-icon">
            <i class="fas fa-globe-americas"></i>
        </div>
        <div class="lang-info">
            <span class="lang-name" id="current-lang-name">{% get_language_name %}</span>
            <span class="lang-code">{{ request.LANGUAGE_CODE|upper }}</span>
        </div>
        <div class="toggle-icon">
            <i class="fas fa-exchange-alt"></i>
        </div>
    </div>
    
    <!-- Language Options Panel -->
    <div class="lang-options-panel" id="lang-options-panel">
        <div class="panel-header">
            <h6><i class="fas fa-language"></i> اختر اللغة / Choisir la langue</h6>
        </div>
        
        <div class="lang-options-grid">
            <!-- زر العربية / Arabic Button -->
            <div class="lang-option {% if request.LANGUAGE_CODE == 'ar' %}current-lang{% endif %}"
                 data-lang="ar" data-name="العربية" data-direction="rtl">
                <div class="lang-flag">🇲🇷</div>
                <div class="lang-details">
                    <span class="lang-title">العربية</span>
                    <span class="lang-subtitle">Arabic</span>
                </div>
                <div class="lang-action">
                    {% if request.LANGUAGE_CODE == 'ar' %}
                        <i class="fas fa-check-circle text-success"></i>
                    {% else %}
                        <i class="fas fa-arrow-right"></i>
                    {% endif %}
                </div>
            </div>

            <!-- زر الفرنسية / French Button -->
            <div class="lang-option {% if request.LANGUAGE_CODE == 'fr' %}current-lang{% endif %}"
                 data-lang="fr" data-name="Français" data-direction="ltr">
                <div class="lang-flag">🇫🇷</div>
                <div class="lang-details">
                    <span class="lang-title">Français</span>
                    <span class="lang-subtitle">French</span>
                </div>
                <div class="lang-action">
                    {% if request.LANGUAGE_CODE == 'fr' %}
                        <i class="fas fa-check-circle text-success"></i>
                    {% else %}
                        <i class="fas fa-arrow-right"></i>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Translation Preview -->
        <div class="translation-preview" id="translation-preview">
            <div class="preview-header">
                <i class="fas fa-eye"></i> معاينة الترجمة / Aperçu de traduction
            </div>
            <div class="preview-content">
                <div class="preview-item">
                    <span class="preview-original">نظام إدارة المبيعات</span>
                    <i class="fas fa-arrow-right"></i>
                    <span class="preview-translated" id="preview-translated">Système de Gestion des Ventes</span>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <button class="action-btn" onclick="testTranslation()">
                <i class="fas fa-vial"></i> اختبار الترجمة
            </button>
            <button class="action-btn" onclick="showTranslationStats()">
                <i class="fas fa-chart-bar"></i> إحصائيات
            </button>
        </div>
    </div>
</div>

<!-- Translation Status Indicator -->
<div class="translation-status" id="translation-status">
    <div class="status-content">
        <i class="fas fa-sync fa-spin"></i>
        <span id="status-text">جاري تبديل اللغة...</span>
    </div>
</div>

<style>
.bidirectional-lang-switcher {
    position: fixed;
    top: 20px;
    {% if request.direction == 'rtl' %}
        left: 20px;
    {% else %}
        right: 20px;
    {% endif %}
    z-index: 10000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.current-lang-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 200px;
}

.current-lang-display:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.4);
}

.lang-icon {
    font-size: 20px;
    opacity: 0.9;
}

.lang-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.lang-name {
    font-weight: 600;
    font-size: 14px;
}

.lang-code {
    font-size: 11px;
    opacity: 0.8;
    font-weight: 500;
}

.toggle-icon {
    font-size: 16px;
    opacity: 0.8;
    transition: transform 0.3s ease;
}

.bidirectional-lang-switcher.open .toggle-icon {
    transform: rotate(180deg);
}

.lang-options-panel {
    position: absolute;
    top: 100%;
    {% if request.direction == 'rtl' %}
        left: 0;
    {% else %}
        right: 0;
    {% endif %}
    margin-top: 10px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    border: 1px solid rgba(255,255,255,0.3);
    min-width: 350px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(-10px);
}

.bidirectional-lang-switcher.open .lang-options-panel {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
}

.panel-header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.panel-header h6 {
    margin: 0;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.lang-options-grid {
    padding: 15px;
}

.lang-option {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
    border: 2px solid transparent;
}

.lang-option:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX({% if request.direction == 'rtl' %}-5px{% else %}5px{% endif %});
    border-color: rgba(255,255,255,0.3);
}

.lang-flag {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.1);
    border-radius: 50%;
}

.lang-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.lang-title {
    font-weight: 600;
    font-size: 16px;
}

.lang-subtitle {
    font-size: 12px;
    opacity: 0.7;
}

.lang-action {
    font-size: 16px;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.lang-option:hover .lang-action {
    opacity: 1;
    transform: translateX({% if request.direction == 'rtl' %}-3px{% else %}3px{% endif %});
}

.lang-option.current-lang {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-color: rgba(255,255,255,0.3);
    cursor: default;
}

.lang-option.current-lang:hover {
    transform: none;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.lang-option.current-lang .lang-action {
    opacity: 1;
}

.text-success {
    color: #ffffff !important;
}

.translation-preview {
    margin: 15px;
    padding: 15px;
    background: rgba(0,0,0,0.05);
    border-radius: 12px;
    border: 1px solid rgba(0,0,0,0.1);
}

.preview-header {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    margin-bottom: 10px;
}

.preview-content {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.preview-original {
    color: #dc3545;
    font-weight: 600;
}

.preview-translated {
    color: #28a745;
    font-weight: 600;
}

.quick-actions {
    display: flex;
    gap: 10px;
    padding: 15px;
    border-top: 1px solid rgba(0,0,0,0.1);
}

.action-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.translation-status {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 20px 30px;
    border-radius: 15px;
    z-index: 10001;
    display: none;
    backdrop-filter: blur(10px);
}

.translation-status.show {
    display: block;
}

.status-content {
    display: flex;
    align-items: center;
    gap: 15px;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bidirectional-lang-switcher {
        top: 10px;
        {% if request.direction == 'rtl' %}
            left: 10px;
        {% else %}
            right: 10px;
        {% endif %}
    }
    
    .current-lang-display {
        min-width: 160px;
        padding: 10px 15px;
    }
    
    .lang-options-panel {
        min-width: 300px;
    }
    
    .lang-option {
        padding: 12px;
    }
}

/* Animation for switching */
.page-transition {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const switcher = document.getElementById('bidirectional-switcher');
    const currentLangBtn = document.getElementById('current-lang-btn');
    const optionsPanel = document.getElementById('lang-options-panel');
    const currentLangName = document.getElementById('current-lang-name');
    const statusIndicator = document.getElementById('translation-status');
    const statusText = document.getElementById('status-text');
    const previewTranslated = document.getElementById('preview-translated');
    
    // Toggle panel
    currentLangBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        switcher.classList.toggle('open');
        updatePreview();
    });
    
    // Close panel when clicking outside
    document.addEventListener('click', function() {
        switcher.classList.remove('open');
    });
    
    // Prevent panel from closing when clicking inside
    optionsPanel.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // Handle language switching
    document.querySelectorAll('.lang-option').forEach(function(option) {
        option.addEventListener('click', function() {
            // Don't switch if it's the current language
            if (this.classList.contains('current-lang')) {
                return;
            }

            const language = this.getAttribute('data-lang');
            const languageName = this.getAttribute('data-name');
            const direction = this.getAttribute('data-direction');

            switchLanguage(language, languageName, direction);
        });
    });
    
    // Language switching function
    function switchLanguage(language, languageName, direction) {
        // Show status
        showStatus('جاري تبديل اللغة... / Changement de langue...');
        
        // Update current display immediately
        currentLangName.textContent = languageName;
        
        // Close panel
        switcher.classList.remove('open');
        
        // Add page transition effect
        document.body.classList.add('page-transition');
        
        // Switch language
        fetch('/set-language/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken()
            },
            body: 'language=' + encodeURIComponent(language)
        })
        .then(response => {
            if (response.ok) {
                showStatus('تم تبديل اللغة بنجاح / Langue changée avec succès', 'success');

                // Translate page content before reload
                translatePageContent(language).then(() => {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                });
            } else {
                throw new Error('Language switch failed');
            }
        })
        .catch(error => {
            console.error('Language switch error:', error);
            showStatus('خطأ في تبديل اللغة / Erreur de changement', 'error');
            setTimeout(() => {
                hideStatus();
                document.body.classList.remove('page-transition');
            }, 2000);
        });
    }
    
    // Update translation preview
    function updatePreview() {
        const currentLang = '{{ request.LANGUAGE_CODE }}';
        const sampleText = 'نظام إدارة المبيعات';
        
        if (currentLang === 'ar') {
            previewTranslated.textContent = 'Système de Gestion des Ventes';
        } else {
            previewTranslated.textContent = 'نظام إدارة المبيعات';
        }
    }
    
    // Status functions
    function showStatus(message, type = 'loading') {
        statusText.textContent = message;
        statusIndicator.classList.add('show');
        
        if (type === 'success') {
            statusIndicator.style.background = 'rgba(40, 167, 69, 0.9)';
        } else if (type === 'error') {
            statusIndicator.style.background = 'rgba(220, 53, 69, 0.9)';
        } else {
            statusIndicator.style.background = 'rgba(0, 0, 0, 0.9)';
        }
    }
    
    function hideStatus() {
        statusIndicator.classList.remove('show');
    }
    
    // Helper functions
    function getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    // Translate page content
    async function translatePageContent(targetLanguage) {
        showStatus('جاري ترجمة المحتوى... / Traduction du contenu...');

        try {
            // Get all text elements that need translation
            const elementsToTranslate = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, button, a, label, .card-title, .nav-link');
            const textsToTranslate = [];
            const elementMap = new Map();

            elementsToTranslate.forEach((element, index) => {
                const text = element.textContent.trim();
                if (text && text.length > 1 && !text.match(/^\d+$/) && !text.includes('http')) {
                    textsToTranslate.push(text);
                    elementMap.set(text, element);
                }
            });

            if (textsToTranslate.length > 0) {
                // Batch translate
                const response = await fetch('/api/batch-translate/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify({
                        texts: textsToTranslate.slice(0, 20), // Limit to first 20 items
                        target_language: targetLanguage,
                        source_language: targetLanguage === 'ar' ? 'fr' : 'ar'
                    })
                });

                const data = await response.json();

                if (data.success && data.translations) {
                    // Apply translations with animation
                    data.translations.forEach((translation, index) => {
                        const element = elementMap.get(translation.original);
                        if (element) {
                            // Add fade effect
                            element.style.transition = 'opacity 0.3s ease';
                            element.style.opacity = '0.5';

                            setTimeout(() => {
                                element.textContent = translation.translated;
                                element.style.opacity = '1';
                            }, 200 + (index * 50));
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Translation error:', error);
        }
    }

    // Initialize
    updatePreview();
});

// Global functions for quick actions
function testTranslation() {
    window.open('/translation-test/', '_blank');
}

function showTranslationStats() {
    fetch('/api/translation-stats/')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const stats = data.statistics;
            alert(`إحصائيات الترجمة / Translation Statistics:
            
العربية → الفرنسية: ${stats.ar_to_fr}
الفرنسية → العربية: ${stats.fr_to_ar}
المجموع: ${stats.total}
التغطية: ${stats.coverage_percentage}%`);
        }
    })
    .catch(error => {
        console.error('Error fetching stats:', error);
        alert('خطأ في جلب الإحصائيات / Error fetching statistics');
    });
}
</script>
