# Generated by Django 4.2.7 on 2025-06-05 03:01

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(blank=True, max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('date_due', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('payment_status', models.CharField(choices=[('pending', 'في الانتظار'), ('partial', 'دفع جزئي'), ('paid', 'مدفوعة'), ('overdue', 'متأخرة')], default='pending', max_length=20, verbose_name='حالة الدفع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة الخصم')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة الضريبة')),
            ],
            options={
                'verbose_name': 'فاتورة شراء',
                'verbose_name_plural': 'فواتير الشراء',
                'ordering': ['-date_created'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('payment_terms', models.CharField(blank=True, max_length=100, null=True, verbose_name='شروط الدفع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseInvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر الوحدة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.product', verbose_name='المنتج')),
                ('purchase_invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.purchaseinvoice', verbose_name='فاتورة الشراء')),
            ],
            options={
                'verbose_name': 'عنصر فاتورة الشراء',
                'verbose_name_plural': 'عناصر فواتير الشراء',
            },
        ),
        migrations.AddField(
            model_name='purchaseinvoice',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.supplier', verbose_name='المورد'),
        ),
        migrations.AddField(
            model_name='purchaseinvoice',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم'),
        ),
    ]
