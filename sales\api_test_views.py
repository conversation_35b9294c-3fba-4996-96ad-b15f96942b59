"""
Simple API test views for debugging
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from sales.translation_engine import TranslationEngine
import json

@csrf_exempt
@require_http_methods(["GET", "POST"])
def api_test(request):
    """
    Simple API test endpoint
    """
    return JsonResponse({
        'success': True,
        'message': 'API is working',
        'method': request.method,
        'path': request.path,
        'content_type': request.content_type
    })

@csrf_exempt
@require_http_methods(["POST"])
def simple_translate(request):
    """
    Simple translation endpoint for testing
    """
    try:
        # Get data from request
        if request.content_type == 'application/json':
            data = json.loads(request.body.decode('utf-8'))
        else:
            data = {
                'text': request.POST.get('text', ''),
                'target_language': request.POST.get('target_language', 'fr')
            }
        
        text = data.get('text', '')
        target_language = data.get('target_language', 'fr')
        
        if not text:
            return JsonResponse({
                'success': False,
                'error': 'No text provided'
            }, status=400)
        
        # Simple translation
        if target_language == 'fr':
            translated = TranslationEngine.translate(text, 'fr', 'ar')
        else:
            translated = TranslationEngine.translate(text, 'ar', 'fr')
        
        return JsonResponse({
            'success': True,
            'original': text,
            'translated': translated,
            'target_language': target_language,
            'engine': 'TranslationEngine'
        })
        
    except json.JSONDecodeError as e:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON',
            'details': str(e)
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Translation failed',
            'details': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def translation_stats_simple(request):
    """
    Simple translation statistics endpoint
    """
    try:
        stats = TranslationEngine.get_translation_stats()
        return JsonResponse({
            'success': True,
            'statistics': stats
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def api_health(request):
    """
    API health check endpoint
    """
    try:
        # Test translation engine
        test_translation = TranslationEngine.translate('اختبار', 'fr', 'ar')
        
        return JsonResponse({
            'success': True,
            'status': 'healthy',
            'translation_engine': 'working',
            'test_translation': test_translation,
            'available_endpoints': [
                '/api/test/',
                '/api/simple-translate/',
                '/api/stats-simple/',
                '/api/health/',
                '/api/auto-translate/',
                '/api/batch-translate/',
                '/api/translation-stats/'
            ]
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def debug_request(request):
    """
    Debug endpoint to see request details
    """
    try:
        request_data = {
            'method': request.method,
            'path': request.path,
            'content_type': request.content_type,
            'headers': dict(request.headers),
            'GET': dict(request.GET),
            'POST': dict(request.POST),
        }
        
        # Try to parse body
        try:
            if request.body:
                if request.content_type == 'application/json':
                    request_data['body_json'] = json.loads(request.body.decode('utf-8'))
                else:
                    request_data['body_raw'] = request.body.decode('utf-8')
        except:
            request_data['body_error'] = 'Could not parse body'
        
        return JsonResponse({
            'success': True,
            'request_data': request_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
