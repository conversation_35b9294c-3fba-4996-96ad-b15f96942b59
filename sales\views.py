from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Sum, Count, Q, F
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timedelta, datetime
import random
from .models import Customer, Product, Category, Invoice, InvoiceItem, Supplier, PurchaseInvoice, PurchaseInvoiceItem, UserProfile
from .forms import CustomerForm, ProductForm, CategoryForm, InvoiceForm, InvoiceItemFormSet, SupplierForm, PurchaseInvoiceForm, PurchaseInvoiceItemFormSet


class DashboardView(TemplateView):
    template_name = 'sales/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Public statistics (safe for everyone)
        try:
            context['total_customers'] = Customer.objects.count()
            context['total_products'] = Product.objects.count()
            context['total_invoices'] = Invoice.objects.count()
            context['pending_invoices'] = Invoice.objects.filter(payment_status='pending').count()

            # Recent invoices (only if user is authenticated)
            if self.request.user.is_authenticated:
                context['recent_invoices'] = Invoice.objects.select_related('customer').order_by('-date_created')[:5]
                # Low stock products
                context['low_stock_products'] = Product.objects.filter(stock_quantity__lte=10, is_active=True)[:5]
            else:
                context['recent_invoices'] = []
                context['low_stock_products'] = []

            # Monthly sales
            today = timezone.now()
            start_of_month = today.replace(day=1)
            monthly_sales = Invoice.objects.filter(
                date_created__gte=start_of_month,
                payment_status='paid'
            ).aggregate(total=Sum('invoiceitem__quantity') * Sum('invoiceitem__unit_price'))
            context['monthly_sales'] = monthly_sales['total'] or 0

        except Exception as e:
            # Fallback for any database errors
            context.update({
                'total_customers': 0,
                'total_products': 0,
                'total_invoices': 0,
                'pending_invoices': 0,
                'recent_invoices': [],
                'low_stock_products': [],
                'monthly_sales': 0
            })

        return context


# Customer Views
class CustomerListView(LoginRequiredMixin, ListView):
    model = Customer
    template_name = 'sales/customer_list.html'
    context_object_name = 'customers'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Customer.objects.all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | 
                Q(email__icontains=search) | 
                Q(phone__icontains=search)
            )
        return queryset


class CustomerDetailView(LoginRequiredMixin, DetailView):
    model = Customer
    template_name = 'sales/customer_detail.html'
    context_object_name = 'customer'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['customer_invoices'] = Invoice.objects.filter(customer=self.object).order_by('-date_created')[:10]
        return context


class CustomerCreateView(LoginRequiredMixin, CreateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'sales/customer_form.html'
    success_url = reverse_lazy('sales:customer_list')

    def form_valid(self, form):
        response = super().form_valid(form)

        # Handle AJAX request
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'customer': {
                    'id': self.object.id,
                    'name': self.object.name,
                    'email': self.object.email,
                    'phone': self.object.phone,
                }
            })

        messages.success(self.request, _('تم إضافة العميل بنجاح'))
        return response

    def form_invalid(self, form):
        # Handle AJAX request
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'errors': form.errors
            })

        return super().form_invalid(form)


class CustomerUpdateView(LoginRequiredMixin, UpdateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'sales/customer_form.html'
    success_url = reverse_lazy('sales:customer_list')
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات العميل بنجاح'))
        return super().form_valid(form)


class CustomerDeleteView(LoginRequiredMixin, DeleteView):
    model = Customer
    template_name = 'sales/customer_confirm_delete.html'
    success_url = reverse_lazy('sales:customer_list')
    
    def delete(self, request, *args, **kwargs):
        messages.success(self.request, _('تم حذف العميل بنجاح'))
        return super().delete(request, *args, **kwargs)


# Product Views
class ProductListView(LoginRequiredMixin, ListView):
    model = Product
    template_name = 'sales/product_list.html'
    context_object_name = 'products'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Product.objects.select_related('category')
        search = self.request.GET.get('search')
        category = self.request.GET.get('category')
        
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | 
                Q(description__icontains=search)
            )
        
        if category:
            queryset = queryset.filter(category_id=category)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        return context


class ProductDetailView(LoginRequiredMixin, DetailView):
    model = Product
    template_name = 'sales/product_detail.html'
    context_object_name = 'product'


class ProductCreateView(LoginRequiredMixin, CreateView):
    model = Product
    form_class = ProductForm
    template_name = 'sales/product_form.html'
    success_url = reverse_lazy('sales:product_list')
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة المنتج بنجاح'))
        return super().form_valid(form)


class ProductUpdateView(LoginRequiredMixin, UpdateView):
    model = Product
    form_class = ProductForm
    template_name = 'sales/product_form.html'
    success_url = reverse_lazy('sales:product_list')
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات المنتج بنجاح'))
        return super().form_valid(form)


class ProductDeleteView(LoginRequiredMixin, DeleteView):
    model = Product
    template_name = 'sales/product_confirm_delete.html'
    success_url = reverse_lazy('sales:product_list')
    
    def delete(self, request, *args, **kwargs):
        messages.success(self.request, _('تم حذف المنتج بنجاح'))
        return super().delete(request, *args, **kwargs)


# Category Views
class CategoryListView(LoginRequiredMixin, ListView):
    model = Category
    template_name = 'sales/category_list.html'
    context_object_name = 'categories'


class CategoryCreateView(LoginRequiredMixin, CreateView):
    model = Category
    form_class = CategoryForm
    template_name = 'sales/category_form.html'
    success_url = reverse_lazy('sales:category_list')
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة الفئة بنجاح'))
        return super().form_valid(form)


class CategoryUpdateView(LoginRequiredMixin, UpdateView):
    model = Category
    form_class = CategoryForm
    template_name = 'sales/category_form.html'
    success_url = reverse_lazy('sales:category_list')
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث الفئة بنجاح'))
        return super().form_valid(form)


class CategoryDeleteView(LoginRequiredMixin, DeleteView):
    model = Category
    template_name = 'sales/category_confirm_delete.html'
    success_url = reverse_lazy('sales:category_list')
    
    def delete(self, request, *args, **kwargs):
        messages.success(self.request, _('تم حذف الفئة بنجاح'))
        return super().delete(request, *args, **kwargs)


# Invoice Views
class InvoiceListView(LoginRequiredMixin, ListView):
    model = Invoice
    template_name = 'sales/invoice_list.html'
    context_object_name = 'invoices'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Invoice.objects.select_related('customer', 'user')
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')
        
        if search:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search) | 
                Q(customer__name__icontains=search)
            )
        
        if status:
            queryset = queryset.filter(payment_status=status)
            
        return queryset


class InvoiceDetailView(LoginRequiredMixin, DetailView):
    model = Invoice
    template_name = 'sales/invoice_detail.html'
    context_object_name = 'invoice'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['invoice_items'] = InvoiceItem.objects.filter(invoice=self.object).select_related('product')
        return context


class InvoiceCreateView(LoginRequiredMixin, CreateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'sales/invoice_form.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = InvoiceItemFormSet(self.request.POST)
        else:
            context['formset'] = InvoiceItemFormSet()
        return context
    
    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']
        
        if formset.is_valid():
            form.instance.user = self.request.user
            self.object = form.save()
            formset.instance = self.object
            formset.save()
            
            # Update product stock
            for item_form in formset:
                if item_form.cleaned_data and not item_form.cleaned_data.get('DELETE'):
                    product = item_form.cleaned_data['product']
                    quantity = item_form.cleaned_data['quantity']
                    product.stock_quantity -= quantity
                    product.save()
            
            messages.success(self.request, _('تم إنشاء الفاتورة بنجاح'))
            return redirect('sales:invoice_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)


class InvoiceUpdateView(LoginRequiredMixin, UpdateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'sales/invoice_form.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = InvoiceItemFormSet(self.request.POST, instance=self.object)
        else:
            context['formset'] = InvoiceItemFormSet(instance=self.object)
        return context
    
    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']
        
        if formset.is_valid():
            self.object = form.save()
            formset.save()
            messages.success(self.request, _('تم تحديث الفاتورة بنجاح'))
            return redirect('sales:invoice_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)


class InvoiceDeleteView(LoginRequiredMixin, DeleteView):
    model = Invoice
    template_name = 'sales/invoice_confirm_delete.html'
    success_url = reverse_lazy('sales:invoice_list')
    
    def delete(self, request, *args, **kwargs):
        # Restore product stock
        invoice = self.get_object()
        for item in invoice.invoiceitem_set.all():
            item.product.stock_quantity += item.quantity
            item.product.save()
        
        messages.success(self.request, _('تم حذف الفاتورة بنجاح'))
        return super().delete(request, *args, **kwargs)


class InvoicePrintView(LoginRequiredMixin, DetailView):
    model = Invoice
    template_name = 'sales/invoice_print.html'
    context_object_name = 'invoice'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['invoice_items'] = InvoiceItem.objects.filter(invoice=self.object).select_related('product')
        return context


class ReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'sales/reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات عامة
        total_invoices = Invoice.objects.count()
        paid_invoices = Invoice.objects.filter(payment_status='paid').count()
        pending_invoices = Invoice.objects.filter(payment_status='pending').count()
        total_revenue = Invoice.objects.filter(payment_status='paid').aggregate(
            total=Sum('invoiceitem__quantity') * Sum('invoiceitem__unit_price')
        )['total'] or 0

        # إحصائيات المنتجات
        low_stock_products = Product.objects.filter(stock_quantity__lt=10).count()
        total_products = Product.objects.count()

        # إحصائيات الموردين
        total_suppliers = Supplier.objects.filter(is_active=True).count()
        total_purchase_invoices = PurchaseInvoice.objects.count()

        context.update({
            'total_invoices': total_invoices,
            'paid_invoices': paid_invoices,
            'pending_invoices': pending_invoices,
            'total_revenue': total_revenue,
            'low_stock_products': low_stock_products,
            'total_products': total_products,
            'total_suppliers': total_suppliers,
            'total_purchase_invoices': total_purchase_invoices,
        })

        # مبيعات آخر 6 أشهر
        today = timezone.now()
        months_data = []
        for i in range(6):
            month_start = today.replace(day=1) - timedelta(days=i*30)
            month_start = month_start.replace(day=1)

            if month_start.month == 12:
                month_end = month_start.replace(year=month_start.year+1, month=1, day=1) - timedelta(days=1)
            else:
                month_end = month_start.replace(month=month_start.month+1, day=1) - timedelta(days=1)

            sales_count = Invoice.objects.filter(
                date_created__range=[month_start, month_end]
            ).count()

            sales_amount = Invoice.objects.filter(
                date_created__range=[month_start, month_end],
                payment_status='paid'
            ).aggregate(
                total=Sum(F('invoiceitem__quantity') * F('invoiceitem__unit_price'))
            )['total'] or 0

            months_data.append({
                'month': month_start.strftime('%B'),
                'month_year': month_start.strftime('%B %Y'),
                'sales_count': sales_count,
                'sales_amount': float(sales_amount) if sales_amount else 0
            })

        context['monthly_sales'] = list(reversed(months_data))

        # أفضل المنتجات مبيعاً
        top_products = Product.objects.annotate(
            total_sold=Sum('invoiceitem__quantity'),
            total_revenue=Sum(F('invoiceitem__quantity') * F('invoiceitem__unit_price'))
        ).filter(total_sold__gt=0).order_by('-total_sold')[:10]

        context['top_products'] = top_products

        # أفضل العملاء
        top_customers = Customer.objects.annotate(
            total_invoices=Count('invoice'),
            total_spent=Sum(F('invoice__invoiceitem__quantity') * F('invoice__invoiceitem__unit_price'))
        ).filter(total_invoices__gt=0).order_by('-total_spent')[:10]

        context['top_customers'] = top_customers

        # أفضل الموردين
        top_suppliers = Supplier.objects.annotate(
            total_purchases=Count('purchaseinvoice'),
            total_amount=Sum(F('purchaseinvoice__purchaseinvoiceitem__quantity') * F('purchaseinvoice__purchaseinvoiceitem__unit_price'))
        ).filter(total_purchases__gt=0).order_by('-total_amount')[:10]

        context['top_suppliers'] = top_suppliers

        # المنتجات قليلة المخزون
        low_stock = Product.objects.filter(stock_quantity__lt=10, is_active=True).order_by('stock_quantity')[:10]
        context['low_stock_products'] = low_stock

        # إحصائيات حالة الدفع
        payment_stats = Invoice.objects.values('payment_status').annotate(
            count=Count('id'),
            total_amount=Sum(F('invoiceitem__quantity') * F('invoiceitem__unit_price'))
        )
        context['payment_stats'] = payment_stats

        return context


class AdvancedReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'sales/advanced_reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على المعاملات من الطلب
        report_type = self.request.GET.get('type', 'daily')
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')

        today = timezone.now().date()

        # تحديد الفترة الزمنية حسب نوع التقرير
        if report_type == 'daily':
            if not start_date:
                start_date = today
            else:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = start_date

        elif report_type == 'weekly':
            if not start_date:
                # بداية الأسبوع الحالي (الاثنين)
                start_date = today - timedelta(days=today.weekday())
            else:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = start_date + timedelta(days=6)

        elif report_type == 'monthly':
            if not start_date:
                # بداية الشهر الحالي
                start_date = today.replace(day=1)
            else:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                start_date = start_date.replace(day=1)
            # نهاية الشهر
            if start_date.month == 12:
                end_date = start_date.replace(year=start_date.year+1, month=1) - timedelta(days=1)
            else:
                end_date = start_date.replace(month=start_date.month+1) - timedelta(days=1)

        elif report_type == 'custom':
            if start_date and end_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            else:
                # آخر 30 يوم افتراضياً
                end_date = today
                start_date = today - timedelta(days=30)

        # تحويل التواريخ إلى datetime للاستعلام
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # الفواتير في الفترة المحددة
        invoices = Invoice.objects.filter(
            date_created__range=[start_datetime, end_datetime]
        )

        # إحصائيات عامة للفترة
        total_invoices = invoices.count()
        paid_invoices = invoices.filter(payment_status='paid').count()
        pending_invoices = invoices.filter(payment_status='pending').count()

        # إجمالي المبيعات
        total_sales = invoices.filter(payment_status='paid').aggregate(
            total=Sum(F('invoiceitem__quantity') * F('invoiceitem__unit_price'))
        )['total'] or 0

        # متوسط قيمة الفاتورة
        avg_invoice = total_sales / paid_invoices if paid_invoices > 0 else 0

        # أفضل المنتجات في الفترة
        top_products_period = Product.objects.filter(
            invoiceitem__invoice__in=invoices
        ).annotate(
            total_sold=Sum('invoiceitem__quantity'),
            total_revenue=Sum(F('invoiceitem__quantity') * F('invoiceitem__unit_price'))
        ).filter(total_sold__gt=0).order_by('-total_sold')[:10]

        # أفضل العملاء في الفترة
        top_customers_period = Customer.objects.filter(
            invoice__in=invoices
        ).annotate(
            total_invoices=Count('invoice'),
            total_spent=Sum(F('invoice__invoiceitem__quantity') * F('invoice__invoiceitem__unit_price'))
        ).filter(total_invoices__gt=0).order_by('-total_spent')[:10]

        context.update({
            'report_type': report_type,
            'start_date': start_date,
            'end_date': end_date,
            'total_invoices': total_invoices,
            'paid_invoices': paid_invoices,
            'pending_invoices': pending_invoices,
            'total_sales': float(total_sales),
            'avg_invoice': float(avg_invoice),
            'top_products_period': top_products_period,
            'top_customers_period': top_customers_period,
        })

        # بيانات يومية للرسم البياني
        if report_type in ['weekly', 'monthly', 'custom']:
            daily_data = []
            current_date = start_date

            while current_date <= end_date:
                day_start = datetime.combine(current_date, datetime.min.time())
                day_end = datetime.combine(current_date, datetime.max.time())

                day_invoices = invoices.filter(date_created__range=[day_start, day_end])
                day_sales = day_invoices.filter(payment_status='paid').aggregate(
                    total=Sum(F('invoiceitem__quantity') * F('invoiceitem__unit_price'))
                )['total'] or 0

                daily_data.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'date_display': current_date.strftime('%d/%m'),
                    'invoices_count': day_invoices.count(),
                    'sales_amount': float(day_sales)
                })

                current_date += timedelta(days=1)

            context['daily_data'] = daily_data

        return context


@login_required
def get_product_price(request):
    """AJAX view to get product price"""
    product_id = request.GET.get('product_id')
    if product_id:
        try:
            product = Product.objects.get(id=product_id)
            return JsonResponse({
                'price': float(product.price),
                'stock': product.stock_quantity
            })
        except Product.DoesNotExist:
            pass

    return JsonResponse({'price': 0, 'stock': 0})


@login_required
def ajax_customers(request):
    """AJAX view to get customers list"""
    customers = Customer.objects.all().values('id', 'name', 'email', 'phone')
    return JsonResponse({'customers': list(customers)})


@login_required
def ajax_products(request):
    """AJAX view to search products"""
    search = request.GET.get('search', '')
    products = Product.objects.filter(is_active=True).select_related('category')

    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(category__name__icontains=search)
        )

    products_data = []
    for product in products[:20]:  # Limit to 20 results
        products_data.append({
            'id': product.id,
            'name': product.name,
            'category': product.category.name,
            'price': float(product.price),
            'stock': product.stock_quantity,
        })

    return JsonResponse({'products': products_data})


# Supplier Views
class SupplierListView(LoginRequiredMixin, ListView):
    model = Supplier
    template_name = 'sales/supplier_list.html'
    context_object_name = 'suppliers'
    paginate_by = 20

    def get_queryset(self):
        queryset = Supplier.objects.all()
        search = self.request.GET.get('search')

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(email__icontains=search) |
                Q(phone__icontains=search)
            )

        return queryset


class SupplierDetailView(LoginRequiredMixin, DetailView):
    model = Supplier
    template_name = 'sales/supplier_detail.html'
    context_object_name = 'supplier'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['purchase_invoices'] = PurchaseInvoice.objects.filter(supplier=self.object).order_by('-date_created')[:10]
        return context


class SupplierCreateView(LoginRequiredMixin, CreateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'sales/supplier_form.html'
    success_url = reverse_lazy('sales:supplier_list')

    def form_valid(self, form):
        response = super().form_valid(form)

        # Handle AJAX request
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'supplier': {
                    'id': self.object.id,
                    'name': self.object.name,
                    'email': self.object.email,
                    'phone': self.object.phone,
                }
            })

        messages.success(self.request, _('تم إضافة المورد بنجاح'))
        return response

    def form_invalid(self, form):
        # Handle AJAX request
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'errors': form.errors
            })

        return super().form_invalid(form)


class SupplierUpdateView(LoginRequiredMixin, UpdateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'sales/supplier_form.html'
    success_url = reverse_lazy('sales:supplier_list')

    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث المورد بنجاح'))
        return super().form_valid(form)


class SupplierDeleteView(LoginRequiredMixin, DeleteView):
    model = Supplier
    template_name = 'sales/supplier_confirm_delete.html'
    success_url = reverse_lazy('sales:supplier_list')

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, _('تم حذف المورد بنجاح'))
        return super().delete(request, *args, **kwargs)


# Purchase Invoice Views
class PurchaseInvoiceListView(LoginRequiredMixin, ListView):
    model = PurchaseInvoice
    template_name = 'sales/purchase_invoice_list.html'
    context_object_name = 'purchase_invoices'
    paginate_by = 20

    def get_queryset(self):
        queryset = PurchaseInvoice.objects.select_related('supplier', 'user')
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')

        if search:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search) |
                Q(supplier__name__icontains=search)
            )

        if status:
            queryset = queryset.filter(payment_status=status)

        return queryset


class PurchaseInvoiceDetailView(LoginRequiredMixin, DetailView):
    model = PurchaseInvoice
    template_name = 'sales/purchase_invoice_detail.html'
    context_object_name = 'purchase_invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['purchase_invoice_items'] = PurchaseInvoiceItem.objects.filter(purchase_invoice=self.object).select_related('product')
        return context


class PurchaseInvoiceCreateView(LoginRequiredMixin, CreateView):
    model = PurchaseInvoice
    form_class = PurchaseInvoiceForm
    template_name = 'sales/purchase_invoice_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = PurchaseInvoiceItemFormSet(self.request.POST)
        else:
            context['formset'] = PurchaseInvoiceItemFormSet()
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            form.instance.user = self.request.user
            self.object = form.save()
            formset.instance = self.object
            formset.save()

            # Update product stock (increase for purchases)
            for item_form in formset:
                if item_form.cleaned_data and not item_form.cleaned_data.get('DELETE'):
                    product = item_form.cleaned_data['product']
                    quantity = item_form.cleaned_data['quantity']
                    product.stock_quantity += quantity
                    product.save()

            messages.success(self.request, _('تم إنشاء فاتورة الشراء بنجاح'))
            return redirect('sales:purchase_invoice_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)


class PurchaseInvoiceUpdateView(LoginRequiredMixin, UpdateView):
    model = PurchaseInvoice
    form_class = PurchaseInvoiceForm
    template_name = 'sales/purchase_invoice_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = PurchaseInvoiceItemFormSet(self.request.POST, instance=self.object)
        else:
            context['formset'] = PurchaseInvoiceItemFormSet(instance=self.object)
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            self.object = form.save()
            formset.save()
            messages.success(self.request, _('تم تحديث فاتورة الشراء بنجاح'))
            return redirect('sales:purchase_invoice_detail', pk=self.object.pk)
        else:
            return self.form_invalid(form)


class PurchaseInvoiceDeleteView(LoginRequiredMixin, DeleteView):
    model = PurchaseInvoice
    template_name = 'sales/purchase_invoice_confirm_delete.html'
    success_url = reverse_lazy('sales:purchase_invoice_list')

    def delete(self, request, *args, **kwargs):
        # Restore product stock (decrease for deleted purchases)
        purchase_invoice = self.get_object()
        for item in purchase_invoice.purchaseinvoiceitem_set.all():
            item.product.stock_quantity -= item.quantity
            item.product.save()

        messages.success(self.request, _('تم حذف فاتورة الشراء بنجاح'))
        return super().delete(request, *args, **kwargs)


class PurchaseInvoicePrintView(LoginRequiredMixin, DetailView):
    model = PurchaseInvoice
    template_name = 'sales/purchase_invoice_print.html'
    context_object_name = 'purchase_invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['purchase_invoice_items'] = PurchaseInvoiceItem.objects.filter(purchase_invoice=self.object).select_related('product')
        return context


@login_required
def ajax_suppliers(request):
    """AJAX view to get suppliers list"""
    suppliers = Supplier.objects.filter(is_active=True).values('id', 'name', 'email', 'phone')
    return JsonResponse({'suppliers': list(suppliers)})


class BarcodeView(LoginRequiredMixin, DetailView):
    """عرض الباركود للمنتج"""
    model = Product
    template_name = 'sales/barcode_view.html'
    context_object_name = 'product'


class BarcodePrintView(LoginRequiredMixin, DetailView):
    """طباعة الباركود للمنتج"""
    model = Product
    template_name = 'sales/barcode_print.html'
    context_object_name = 'product'


@login_required
def generate_barcode_for_product(request, product_id):
    """إنشاء باركود جديد للمنتج"""
    try:
        product = Product.objects.get(id=product_id)
        if not product.barcode:
            product.barcode = product.generate_barcode()
            product.save()
            return JsonResponse({
                'success': True,
                'barcode': product.barcode,
                'barcode_display': product.barcode_display
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'المنتج لديه باركود بالفعل'
            })
    except Product.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'المنتج غير موجود'
        })


@login_required
def search_by_barcode(request):
    """البحث عن منتج بالباركود"""
    barcode = request.GET.get('barcode', '').strip()
    if barcode:
        try:
            product = Product.objects.get(barcode=barcode, is_active=True)
            return JsonResponse({
                'success': True,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'price': float(product.price),
                    'stock_quantity': product.stock_quantity,
                    'category': product.category.name,
                    'barcode': product.barcode
                }
            })
        except Product.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'لم يتم العثور على منتج بهذا الباركود'
            })
    return JsonResponse({
        'success': False,
        'message': 'يرجى إدخال الباركود'
    })


@login_required
def check_barcode_unique(request):
    """فحص تفرد الباركود"""
    barcode = request.GET.get('barcode', '').strip()
    product_id = request.GET.get('product_id')  # للتحديث

    if not barcode:
        return JsonResponse({
            'success': False,
            'message': 'يرجى إدخال الباركود'
        })

    # فحص إذا كان الباركود موجود
    query = Product.objects.filter(barcode=barcode)

    # استثناء المنتج الحالي في حالة التحديث
    if product_id:
        query = query.exclude(id=product_id)

    if query.exists():
        existing_product = query.first()
        return JsonResponse({
            'success': False,
            'unique': False,
            'message': f'الباركود موجود بالفعل للمنتج: {existing_product.name}',
            'existing_product': {
                'id': existing_product.id,
                'name': existing_product.name,
                'category': existing_product.category.name
            }
        })

    return JsonResponse({
        'success': True,
        'unique': True,
        'message': 'الباركود متاح للاستخدام'
    })


@login_required
def generate_random_barcode(request):
    """إنشاء باركود عشوائي"""
    category_id = request.GET.get('category_id', '1')

    # إنشاء باركود EAN-13
    country_code = "616"  # كود المغرب العربي
    company_code = f"{int(category_id):04d}"

    # البحث عن رقم منتج فريد
    max_attempts = 100
    for _ in range(max_attempts):
        product_code = f"{random.randint(10000, 99999)}"
        barcode_without_check = country_code + company_code + product_code

        # حساب رقم التحقق
        odd_sum = sum(int(barcode_without_check[i]) for i in range(0, 12, 2))
        even_sum = sum(int(barcode_without_check[i]) for i in range(1, 12, 2))
        total = odd_sum + (even_sum * 3)
        check_digit = (10 - (total % 10)) % 10

        full_barcode = barcode_without_check + str(check_digit)

        # فحص التفرد
        if not Product.objects.filter(barcode=full_barcode).exists():
            return JsonResponse({
                'success': True,
                'barcode': full_barcode,
                'formatted': f"{full_barcode[0]} {full_barcode[1:7]} {full_barcode[7:13]}"
            })

    return JsonResponse({
        'success': False,
        'message': 'فشل في إنشاء باركود فريد. حاول مرة أخرى.'
    })


@login_required
def set_user_language_preference(request):
    """تعيين تفضيل اللغة للمستخدم مع معالجة أخطاء Unicode"""
    if request.method == 'POST':
        try:
            language = request.POST.get('language', '').strip()

            # Validate language
            if language in ['ar', 'fr']:
                # إنشاء أو تحديث ملف المستخدم
                profile, created = UserProfile.objects.get_or_create(user=request.user)
                profile.preferred_language = language
                profile.save()

                # تعيين اللغة في الجلسة
                request.session['django_language'] = language

                return JsonResponse({
                    'success': True,
                    'message': 'تم تحديث تفضيل اللغة بنجاح',
                    'language': language
                }, json_dumps_params={'ensure_ascii': False})
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'لغة غير مدعومة'
                }, json_dumps_params={'ensure_ascii': False})

        except (UnicodeDecodeError, UnicodeEncodeError) as e:
            return JsonResponse({
                'success': False,
                'message': 'خطأ في ترميز النص'
            }, json_dumps_params={'ensure_ascii': False})
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': 'حدث خطأ أثناء حفظ التفضيل'
            }, json_dumps_params={'ensure_ascii': False})

    return JsonResponse({
        'success': False,
        'message': 'طلب غير صحيح'
    }, json_dumps_params={'ensure_ascii': False})


@login_required
def get_user_language_preference(request):
    """الحصول على تفضيل اللغة للمستخدم مع معالجة أخطاء Unicode"""
    try:
        profile = UserProfile.objects.get(user=request.user)
        return JsonResponse({
            'success': True,
            'language': profile.preferred_language
        }, json_dumps_params={'ensure_ascii': False})
    except UserProfile.DoesNotExist:
        return JsonResponse({
            'success': True,
            'language': 'ar'  # اللغة الافتراضية
        }, json_dumps_params={'ensure_ascii': False})
    except (UnicodeDecodeError, UnicodeEncodeError) as e:
        return JsonResponse({
            'success': False,
            'message': 'خطأ في ترميز النص',
            'language': 'ar'
        }, json_dumps_params={'ensure_ascii': False})
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'حدث خطأ أثناء استرجاع التفضيل',
            'language': 'ar'
        }, json_dumps_params={'ensure_ascii': False})


def custom_set_language(request):
    """Final working language switching"""
    from django.shortcuts import redirect

    # Get language from POST or GET
    language = request.POST.get('language') or request.GET.get('language', 'ar')
    next_url = request.POST.get('next') or request.GET.get('next') or request.META.get('HTTP_REFERER', '/')

    # Validate and set language
    if language in ['ar', 'fr']:
        # Set language in session
        request.session['django_language'] = language
        request.session.modified = True  # Force session save

        # Save user preference if logged in
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                profile, created = UserProfile.objects.get_or_create(user=request.user)
                profile.preferred_language = language
                profile.save()
            except:
                pass  # Continue even if saving fails

    # Redirect to next URL or referer
    return redirect(next_url)


def test_language(request):
    """صفحة اختبار تبديل اللغة"""
    from django.shortcuts import render
    return render(request, 'test_language.html')


def debug_language(request):
    """صفحة تشخيص تبديل اللغة"""
    from django.shortcuts import render
    return render(request, 'language_debug.html')


def simple_language_test(request):
    """صفحة اختبار بسيطة لتبديل اللغة"""
    from django.shortcuts import render
    return render(request, 'simple_language_test.html')


def translation_test(request):
    """صفحة اختبار نظام الترجمة المحسن"""
    from django.shortcuts import render
    return render(request, 'translation_test.html')


def bidirectional_translation_demo(request):
    """صفحة عرض الترجمة ثنائية الاتجاه"""
    from django.shortcuts import render
    return render(request, 'bidirectional_translation_demo.html')


def complete_translation_showcase(request):
    """صفحة العرض الشامل لنظام الترجمة المتكامل"""
    from django.shortcuts import render
    return render(request, 'complete_translation_showcase.html')


def api_test_page(request):
    """صفحة اختبار API الترجمة"""
    from django.shortcuts import render
    return render(request, 'api_test_page.html')


def language_switcher_test(request):
    """صفحة اختبار مبدل اللغة المحسن"""
    from django.shortcuts import render
    return render(request, 'language_switcher_test.html')


def language_switcher_comparison(request):
    """صفحة مقارنة مبدلات اللغة المختلفة"""
    from django.shortcuts import render
    return render(request, 'language_switcher_comparison.html')
