# Django Admin Customization
# تخصيص واجهة إدارة Django

from django.contrib import admin
from django.contrib.admin import AdminSite
from django.utils.html import format_html

class CustomAdminSite(AdminSite):
    """
    Custom admin site with dark theme support
    """
    site_header = 'نظام إدارة المبيعات'
    site_title = 'إدارة الموقع'
    index_title = 'لوحة التحكم'
    
    def each_context(self, request):
        """
        Add custom context to admin templates
        """
        context = super().each_context(request)
        context.update({
            'custom_css': True,
            'dark_theme': True,
        })
        return context

# Create custom admin site instance
custom_admin_site = CustomAdminSite(name='custom_admin')

class BaseModelAdmin(admin.ModelAdmin):
    """
    Base admin class with dark theme styling
    """
    
    class Media:
        css = {
            'all': ('admin/css/admin-dark-theme.css',)
        }
        js = ('admin/js/admin-enhancements.js',)
    
    def get_list_display(self, request):
        """
        Customize list display for better visibility
        """
        list_display = super().get_list_display(request)
        return list_display
    
    def changelist_view(self, request, extra_context=None):
        """
        Customize changelist view with dark theme
        """
        extra_context = extra_context or {}
        extra_context.update({
            'dark_theme': True,
            'custom_styling': True,
        })
        return super().changelist_view(request, extra_context)

def apply_dark_theme_to_admin():
    """
    Apply dark theme to all registered admin models
    """
    # Get all registered models
    for model, admin_class in admin.site._registry.items():
        # Add custom CSS to existing admin classes
        if hasattr(admin_class, 'Media'):
            if hasattr(admin_class.Media, 'css'):
                admin_class.Media.css['all'] = admin_class.Media.css.get('all', ()) + ('admin/css/admin-dark-theme.css',)
            else:
                admin_class.Media.css = {'all': ('admin/css/admin-dark-theme.css',)}
        else:
            class Media:
                css = {'all': ('admin/css/admin-dark-theme.css',)}
            admin_class.Media = Media

# Apply dark theme when module is imported
apply_dark_theme_to_admin()
