"""
URL configuration for sales_system project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from sales.translation_views import (
    set_language as enhanced_set_language,
    translate_text,
    get_translations,
    translate_page,
    language_info,
    admin_set_language,
    translation_debug,
    auto_translate_text,
    search_translations_api,
    validate_translations_api,
    translation_statistics,
    batch_translate
)
from sales.api_test_views import (
    api_test,
    simple_translate,
    translation_stats_simple,
    api_health,
    debug_request
)

# Configure admin site
admin.site.site_header = getattr(settings, 'ADMIN_SITE_HEADER', 'نظام إدارة المبيعات')
admin.site.site_title = getattr(settings, 'ADMIN_SITE_TITLE', 'إدارة الموقع')
admin.site.index_title = getattr(settings, 'ADMIN_INDEX_TITLE', 'لوحة التحكم')

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('sales.urls')),

    # Enhanced Translation URLs
    path('set-language/', enhanced_set_language, name='set_language'),
    path('admin/set-language/', admin_set_language, name='admin_set_language'),

    # Translation API URLs
    path('api/translate/', translate_text, name='translate_text'),
    path('api/translations/', get_translations, name='get_translations'),
    path('api/translate-page/', translate_page, name='translate_page'),
    path('api/language-info/', language_info, name='language_info'),
    path('api/translation-debug/', translation_debug, name='translation_debug'),

    # Advanced Translation API URLs
    path('api/auto-translate/', auto_translate_text, name='auto_translate_text'),
    path('api/search-translations/', search_translations_api, name='search_translations'),
    path('api/validate-translations/', validate_translations_api, name='validate_translations'),
    path('api/translation-stats/', translation_statistics, name='translation_statistics'),
    path('api/batch-translate/', batch_translate, name='batch_translate'),

    # Simple API Test URLs
    path('api/test/', api_test, name='api_test'),
    path('api/simple-translate/', simple_translate, name='simple_translate'),
    path('api/stats-simple/', translation_stats_simple, name='stats_simple'),
    path('api/health/', api_health, name='api_health'),
    path('api/debug/', debug_request, name='debug_request'),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
