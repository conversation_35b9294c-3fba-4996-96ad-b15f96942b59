-- فحص سريع للتأكد من إصلاح المشكلة
USE sales_system;

-- التحقق من وجود الجداول
SELECT 'فحص الجداول:' as message;

SELECT 
    CASE WHEN COUNT(*) > 0 THEN '✅ موجود' ELSE '❌ غير موجود' END as 'sales_supplier'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME = 'sales_supplier';

SELECT 
    CASE WHEN COUNT(*) > 0 THEN '✅ موجود' ELSE '❌ غير موجود' END as 'sales_purchaseinvoice'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME = 'sales_purchaseinvoice';

SELECT 
    CASE WHEN COUNT(*) > 0 THEN '✅ موجود' ELSE '❌ غير موجود' END as 'sales_purchaseinvoiceitem'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME = 'sales_purchaseinvoiceitem';

-- ع<PERSON><PERSON> عدد الصفوف
SELECT 'عدد الموردين:' as message, COUNT(*) as count FROM sales_supplier;

-- عرض الموردين
SELECT 'الموردين المتاحين:' as message;
SELECT id, name, email, phone FROM sales_supplier;

SELECT '✅ تم إصلاح المشكلة بنجاح!' as 'النتيجة';
