<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الباركود - {{ product.name }}</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .barcode-label {
            width: 8cm;
            height: 4cm;
            border: 1px solid #000;
            margin: 0.5cm;
            padding: 0.3cm;
            display: inline-block;
            vertical-align: top;
            page-break-inside: avoid;
            background: white;
        }
        
        .barcode-container {
            text-align: center;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .product-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .barcode-svg {
            margin: 5px 0;
        }
        
        .barcode-number {
            font-size: 10px;
            font-family: 'Courier New', monospace;
            margin-top: 2px;
        }
        
        .price {
            font-size: 11px;
            font-weight: bold;
            color: #d63384;
        }
        
        .no-print {
            display: block;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                padding: 0;
                margin: 0;
            }
            
            .barcode-label {
                margin: 0.2cm;
            }
        }
        
        /* Multiple labels layout */
        .labels-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
        }
        
        .print-options {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .form-control {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <!-- Print Options -->
    <div class="print-options no-print">
        <h3>خيارات الطباعة</h3>
        <div>
            <label>عدد النسخ:</label>
            <input type="number" id="copyCount" value="1" min="1" max="50" class="form-control" style="width: 100px; display: inline-block;">
            
            <button class="btn btn-primary" onclick="generateLabels()">
                <i class="fas fa-sync"></i> تحديث العدد
            </button>
            
            <button class="btn btn-success" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
            
            <button class="btn btn-secondary" onclick="window.close()">
                <i class="fas fa-times"></i> إغلاق
            </button>
        </div>
        
        <div style="margin-top: 10px;">
            <small class="text-muted">
                * كل ملصق بحجم 8×4 سم مناسب للطباعة على ملصقات المنتجات
            </small>
        </div>
    </div>

    <!-- Barcode Labels -->
    <div class="labels-grid" id="labelsContainer">
        <!-- Labels will be generated here -->
    </div>

    <!-- JsBarcode Library -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <script>
        const productData = {
            name: "{{ product.name }}",
            barcode: "{{ product.barcode }}",
            price: "{{ product.price }}",
            category: "{{ product.category.name }}"
        };

        function createBarcodeLabel(index) {
            return `
                <div class="barcode-label">
                    <div class="barcode-container">
                        <div class="product-name">${productData.name}</div>
                        <div class="barcode-svg">
                            <svg id="barcode-${index}"></svg>
                        </div>
                        <div class="barcode-number">${productData.barcode}</div>
                        <div class="price">${productData.price} أوقية</div>
                    </div>
                </div>
            `;
        }

        function generateLabels() {
            const count = parseInt(document.getElementById('copyCount').value) || 1;
            const container = document.getElementById('labelsContainer');
            
            // Clear existing labels
            container.innerHTML = '';
            
            // Generate new labels
            for (let i = 0; i < count; i++) {
                container.innerHTML += createBarcodeLabel(i);
            }
            
            // Generate barcodes for each label
            setTimeout(() => {
                for (let i = 0; i < count; i++) {
                    try {
                        JsBarcode(`#barcode-${i}`, productData.barcode, {
                            format: "EAN13",
                            width: 1.5,
                            height: 40,
                            displayValue: false,
                            background: "#ffffff",
                            lineColor: "#000000",
                            margin: 0
                        });
                    } catch (error) {
                        console.error('Error generating barcode:', error);
                        document.getElementById(`barcode-${i}`).innerHTML = 
                            '<text x="50%" y="50%" text-anchor="middle" font-size="12">خطأ في الباركود</text>';
                    }
                }
            }, 100);
        }

        // Generate initial label
        document.addEventListener('DOMContentLoaded', function() {
            generateLabels();
        });

        // Auto-print when page loads (optional)
        // window.addEventListener('load', function() {
        //     setTimeout(() => window.print(), 1000);
        // });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
