#!/bin/bash

echo "========================================"
echo "Sales Management System - نظام إدارة المبيعات"
echo "========================================"
echo

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create virtual environment"
        echo "Please ensure Python 3 is installed"
        exit 1
    fi
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Error: Failed to install requirements"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo
    echo "========================================"
    echo "WARNING: .env file not found!"
    echo "تحذير: ملف .env غير موجود!"
    echo "========================================"
    echo "Please create .env file with your database settings:"
    echo "يرجى إنشاء ملف .env مع إعدادات قاعدة البيانات:"
    echo
    echo "SECRET_KEY=your-secret-key-here"
    echo "DEBUG=True"
    echo "DB_NAME=sales_system"
    echo "DB_USER=root"
    echo "DB_PASSWORD=your_password"
    echo "DB_HOST=localhost"
    echo "DB_PORT=3306"
    echo
    exit 1
fi

# Run migrations
echo "Running migrations..."
python manage.py makemigrations
python manage.py migrate
if [ $? -ne 0 ]; then
    echo "Error: Failed to run migrations"
    echo "Please check your database settings in .env file"
    exit 1
fi

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Compile messages
echo "Compiling translation messages..."
python manage.py compilemessages

# Check if superuser exists
echo
echo "========================================"
echo "Checking for superuser..."
echo "التحقق من وجود مستخدم إداري..."
echo "========================================"
python manage.py shell -c "from django.contrib.auth.models import User; print('Superuser exists' if User.objects.filter(is_superuser=True).exists() else 'No superuser found - please create one')"

echo
echo "Do you want to load sample data? (y/n)"
echo "هل تريد تحميل بيانات تجريبية؟ (y/n)"
read -p "Enter your choice: " load_data
if [[ $load_data == "y" || $load_data == "Y" ]]; then
    echo "Loading sample data..."
    python manage.py load_sample_data
fi

echo
echo "========================================"
echo "Starting Django development server..."
echo "بدء تشغيل خادم Django..."
echo "========================================"
echo
echo "Open your browser and go to: http://127.0.0.1:8000"
echo "افتح المتصفح واذهب إلى: http://127.0.0.1:8000"
echo
echo "Press Ctrl+C to stop the server"
echo "اضغط Ctrl+C لإيقاف الخادم"
echo

python manage.py runserver
