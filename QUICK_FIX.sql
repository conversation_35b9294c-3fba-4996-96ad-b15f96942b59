-- ========================================
-- إصلاح سريع لمشكلة الجداول المفقودة
-- ========================================
-- انسخ هذا الكود في phpMyAdmin إذا كانت الجداول غير موجودة

USE sales_system;

-- إنشاء جدول عناصر فواتير الشراء فقط (إذا كان مفقود)
CREATE TABLE IF NOT EXISTS `sales_purchaseinvoiceitem` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_invoice_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_purchase_invoice_id` (`purchase_invoice_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة Foreign Keys إذا لم تكن موجودة
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.TABLE_CONSTRAINTS 
     WHERE CONSTRAINT_SCHEMA = 'sales_system' 
     AND TABLE_NAME = 'sales_purchaseinvoiceitem' 
     AND CONSTRAINT_NAME = 'fk_purchaseinvoiceitem_invoice') = 0,
    'ALTER TABLE sales_purchaseinvoiceitem ADD CONSTRAINT fk_purchaseinvoiceitem_invoice FOREIGN KEY (purchase_invoice_id) REFERENCES sales_purchaseinvoice(id) ON DELETE CASCADE',
    'SELECT "Foreign key already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.TABLE_CONSTRAINTS 
     WHERE CONSTRAINT_SCHEMA = 'sales_system' 
     AND TABLE_NAME = 'sales_purchaseinvoiceitem' 
     AND CONSTRAINT_NAME = 'fk_purchaseinvoiceitem_product') = 0,
    'ALTER TABLE sales_purchaseinvoiceitem ADD CONSTRAINT fk_purchaseinvoiceitem_product FOREIGN KEY (product_id) REFERENCES sales_product(id) ON DELETE CASCADE',
    'SELECT "Foreign key already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- التحقق من النتيجة
SELECT 'تم إنشاء الجدول بنجاح!' as message;
DESCRIBE sales_purchaseinvoiceitem;
