{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "تعديل المنتج" %} - {{ object.name }}
    {% else %}
        {% trans "إضافة منتج جديد" %}
    {% endif %}
    - {{ block.super }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">{% trans "لوحة التحكم" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'sales:product_list' %}">{% trans "المنتجات" %}</a></li>
                <li class="breadcrumb-item active">
                    {% if object %}
                        {% trans "تعديل المنتج" %}
                    {% else %}
                        {% trans "إضافة منتج جديد" %}
                    {% endif %}
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-box"></i>
                    {% if object %}
                        {% trans "تعديل المنتج" %}: {{ object.name }}
                    {% else %}
                        {% trans "إضافة منتج جديد" %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <i class="fas fa-tag"></i> {{ form.name.label }}
                                {% if form.name.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">
                                <i class="fas fa-folder"></i> {{ form.category.label }}
                                {% if form.category.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.category.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                <a href="{% url 'sales:category_add' %}" target="_blank" class="text-decoration-none">
                                    <i class="fas fa-plus"></i> {% trans "إضافة فئة جديدة" %}
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            <i class="fas fa-align-left"></i> {{ form.description.label }}
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.description.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.price.id_for_label }}" class="form-label">
                                <i class="fas fa-money-bill"></i> {{ form.price.label }}
                                {% if form.price.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            <div class="input-group">
                                {{ form.price }}
                                <span class="input-group-text">{% trans "أوقية" %}</span>
                            </div>
                            {% if form.price.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.price.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.stock_quantity.id_for_label }}" class="form-label">
                                <i class="fas fa-boxes"></i> {{ form.stock_quantity.label }}
                                {% if form.stock_quantity.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            {{ form.stock_quantity }}
                            {% if form.stock_quantity.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.stock_quantity.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-toggle-on"></i> {% trans "حالة المنتج" %}
                            </label>
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.is_active.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Barcode Section -->
                    <div class="mb-3 barcode-section">
                        <label for="{{ form.barcode.id_for_label }}" class="form-label">
                            <i class="fas fa-barcode"></i> {{ form.barcode.label }}
                        </label>
                        <div class="input-group">
                            {{ form.barcode }}
                            <button type="button" class="btn btn-outline-success" id="generateBarcodeBtn">
                                <i class="fas fa-magic"></i> {% trans "إنشاء تلقائي" %}
                            </button>
                            <button type="button" class="btn btn-outline-info" id="previewBarcodeBtn" style="display: none;">
                                <i class="fas fa-eye"></i> {% trans "معاينة" %}
                            </button>
                        </div>
                        {% if form.barcode.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.barcode.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            {% trans "سيتم إنشاء باركود تلقائياً إذا تُرك فارغاً. يمكنك إدخال باركود مخصص أو استخدام الإنشاء التلقائي." %}
                        </div>

                        <!-- Barcode Preview -->
                        <div id="barcodePreview" class="mt-3" style="display: none;">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">{% trans "معاينة الباركود" %}</h6>
                                    <div id="barcodeDisplay"></div>
                                    <div id="barcodeNumber" class="mt-2 font-monospace"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.image.id_for_label }}" class="form-label">
                            <i class="fas fa-image"></i> {{ form.image.label }}
                        </label>

                        {% if object and object.image %}
                            <div class="mb-2">
                                <img src="{{ object.image.url }}" alt="{{ object.name }}"
                                     class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                <p class="text-muted small mt-1">{% trans "الصورة الحالية" %}</p>
                            </div>
                        {% endif %}

                        {{ form.image }}
                        {% if form.image.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.image.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            {% trans "اختر صورة للمنتج (اختياري). الحد الأقصى: 5 ميجابايت" %}
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'sales:product_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "إلغاء" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if object %}
                                {% trans "تحديث المنتج" %}
                            {% else %}
                                {% trans "إضافة المنتج" %}
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- JsBarcode Library -->
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>

<script>
$(document).ready(function() {
    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        
        // Check required fields
        $('[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        // Validate price
        var price = $('#{{ form.price.id_for_label }}').val();
        if (price && (isNaN(price) || parseFloat(price) <= 0)) {
            $('#{{ form.price.id_for_label }}').addClass('is-invalid');
            isValid = false;
        }
        
        // Validate stock quantity
        var stock = $('#{{ form.stock_quantity.id_for_label }}').val();
        if (stock && (isNaN(stock) || parseInt(stock) < 0)) {
            $('#{{ form.stock_quantity.id_for_label }}').addClass('is-invalid');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            showErrorMessage('{% trans "يرجى تصحيح الأخطاء في النموذج" %}');
        }
    });
    
    // Real-time validation
    $('input').on('blur', function() {
        if ($(this).attr('required') && !$(this).val().trim()) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Price validation
    $('#{{ form.price.id_for_label }}').on('blur', function() {
        var price = $(this).val();
        if (price && (isNaN(price) || parseFloat(price) <= 0)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Stock validation
    $('#{{ form.stock_quantity.id_for_label }}').on('blur', function() {
        var stock = $(this).val();
        if (stock && (isNaN(stock) || parseInt(stock) < 0)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Image preview
    $('#{{ form.image.id_for_label }}').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                // Remove existing preview
                $('.image-preview').remove();
                
                // Add new preview
                var preview = $('<div class="image-preview mt-2"><img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px; max-height: 200px;"><p class="text-muted small mt-1">{% trans "معاينة الصورة الجديدة" %}</p></div>');
                $('#{{ form.image.id_for_label }}').after(preview);
            };
            reader.readAsDataURL(file);
        }
    });

    // Barcode functionality
    $('#generateBarcodeBtn').on('click', function() {
        generateRandomBarcode();
    });

    $('#previewBarcodeBtn').on('click', function() {
        previewBarcode();
    });

    // Show preview button and check uniqueness when barcode is entered
    var barcodeTimeout;
    $('#{{ form.barcode.id_for_label }}').on('input', function() {
        var barcode = $(this).val().trim();
        var $input = $(this);

        // Clear previous timeout
        clearTimeout(barcodeTimeout);

        if (barcode.length >= 8) {
            $('#previewBarcodeBtn').show();

            // Check uniqueness after user stops typing
            barcodeTimeout = setTimeout(function() {
                checkBarcodeUniqueness(barcode, $input);
            }, 1000);
        } else {
            $('#previewBarcodeBtn').hide();
            $('#barcodePreview').hide();
            $input.removeClass('is-valid is-invalid');
        }
    });

    // Initial check for existing barcode
    var existingBarcode = $('#{{ form.barcode.id_for_label }}').val();
    if (existingBarcode && existingBarcode.length >= 8) {
        $('#previewBarcodeBtn').show();
    }
});

// Generate random barcode using AJAX
function generateRandomBarcode() {
    var categoryId = $('#{{ form.category.id_for_label }}').val() || '1';

    // Show loading
    $('#generateBarcodeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> {% trans "جاري الإنشاء..." %}');

    $.ajax({
        url: '{% url "sales:generate_random_barcode" %}',
        method: 'GET',
        data: {
            'category_id': categoryId
        },
        success: function(response) {
            if (response.success) {
                $('#{{ form.barcode.id_for_label }}').val(response.barcode);
                $('#previewBarcodeBtn').show();
                showSuccessMessage('{% trans "تم إنشاء الباركود بنجاح" %}: ' + response.formatted);

                // Auto preview
                setTimeout(previewBarcode, 500);
            } else {
                showErrorMessage(response.message);
            }
        },
        error: function() {
            showErrorMessage('{% trans "خطأ في الاتصال بالخادم" %}');
        },
        complete: function() {
            // Reset button
            $('#generateBarcodeBtn').prop('disabled', false).html('<i class="fas fa-magic"></i> {% trans "إنشاء تلقائي" %}');
        }
    });
}

// Check barcode uniqueness
function checkBarcodeUniqueness(barcode, $input) {
    if (barcode.length < 8) return;

    var productId = '{% if object %}{{ object.id }}{% endif %}';

    $.ajax({
        url: '{% url "sales:check_barcode_unique" %}',
        method: 'GET',
        data: {
            'barcode': barcode,
            'product_id': productId
        },
        success: function(response) {
            if (response.success && response.unique) {
                $input.removeClass('is-invalid').addClass('is-valid');
                // Remove any existing error messages
                $input.siblings('.invalid-feedback').remove();
            } else {
                $input.removeClass('is-valid').addClass('is-invalid');

                // Add error message
                $input.siblings('.invalid-feedback').remove();
                $input.after('<div class="invalid-feedback d-block">' + response.message + '</div>');
            }
        },
        error: function() {
            console.log('Error checking barcode uniqueness');
        }
    });
}

// Calculate EAN-13 check digit
function calculateEAN13CheckDigit(barcode) {
    var oddSum = 0, evenSum = 0;
    for (var i = 0; i < 12; i++) {
        if (i % 2 === 0) {
            oddSum += parseInt(barcode[i]);
        } else {
            evenSum += parseInt(barcode[i]);
        }
    }
    var total = oddSum + (evenSum * 3);
    return (10 - (total % 10)) % 10;
}

// Preview barcode
function previewBarcode() {
    var barcode = $('#{{ form.barcode.id_for_label }}').val().trim();

    if (!barcode) {
        showErrorMessage('{% trans "يرجى إدخال الباركود أولاً" %}');
        return;
    }

    if (barcode.length !== 13) {
        showErrorMessage('{% trans "الباركود يجب أن يكون 13 رقم" %}');
        return;
    }

    try {
        // Clear previous barcode
        $('#barcodeDisplay').empty();

        // Generate barcode SVG
        JsBarcode("#barcodeDisplay", barcode, {
            format: "EAN13",
            width: 2,
            height: 60,
            displayValue: false,
            background: "#ffffff",
            lineColor: "#000000"
        });

        // Display barcode number
        var formattedBarcode = barcode.substring(0, 1) + ' ' +
                              barcode.substring(1, 7) + ' ' +
                              barcode.substring(7, 13);
        $('#barcodeNumber').text(formattedBarcode);

        // Show preview
        $('#barcodePreview').show();

    } catch (error) {
        showErrorMessage('{% trans "خطأ في إنشاء الباركود" %}');
        console.error('Barcode error:', error);
    }
}

// Helper functions for messages
function showSuccessMessage(message) {
    // Remove existing alerts
    $('.alert-success').remove();

    // Add success alert
    var alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                  '<i class="fas fa-check-circle"></i> ' + message +
                  '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                  '</div>');

    $('.card-body').prepend(alert);

    // Auto remove after 3 seconds
    setTimeout(function() {
        alert.fadeOut();
    }, 3000);
}

function showErrorMessage(message) {
    // Remove existing alerts
    $('.alert-danger').remove();

    // Add error alert
    var alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                  '<i class="fas fa-exclamation-circle"></i> ' + message +
                  '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                  '</div>');

    $('.card-body').prepend(alert);

    // Auto remove after 5 seconds
    setTimeout(function() {
        alert.fadeOut();
    }, 5000);
}
</script>

<style>
.font-monospace {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: bold;
    letter-spacing: 1px;
}

#barcodeDisplay svg {
    max-width: 100%;
    height: auto;
}

.input-group .btn {
    white-space: nowrap;
}

#barcodePreview .card {
    border: 2px dashed #28a745;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    animation: fadeIn 0.5s ease-in;
}

.alert {
    margin-bottom: 1rem;
    animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.input-group .btn:disabled {
    opacity: 0.6;
}

.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.8-.77-.8-.77-.8.77.8.77zm1.48-4.97L6.06 4.04l-.8.77-2.28-2.28-.8-.77zm-1.48-.77L.5 2.77l.8.77 1.48-1.48-.8-.77z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.barcode-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.barcode-section .form-label {
    color: #495057;
    font-weight: 600;
}
</style>
{% endblock %}
