# 🚨 إصلاح فوري للمشكلة

## ❌ المشكلة:
```
Table 'sales_system.sales_purchaseinvoiceitem' doesn't exist
```

## ✅ الحل السريع (5 دقائق):

### 1️⃣ افتح phpMyAdmin
```
http://localhost/phpmyadmin/
```

### 2️⃣ اختر قاعدة البيانات
- انقر على `sales_system` من القائمة اليسرى
- إذا لم تكن موجودة، أنشئها بالنقر على "New"

### 3️⃣ تنفيذ الكود
1. انقر على تبويب **"SQL"**
2. انسخ **كامل** محتوى ملف `URGENT_FIX.sql`
3. الصق في المربع الكبير
4. انقر **"Go"** أو **"تنفيذ"**

### 4️⃣ تشغيل النظام
```cmd
python manage.py runserver
```

## 🎯 النتيجة المتوقعة:
- ✅ جدول `sales_supplier` 
- ✅ جدول `sales_purchaseinvoice`
- ✅ جدول `sales_purchaseinvoiceitem`
- ✅ 3 موردين تجريبيين

## 🔍 التحقق:
بعد تنفيذ الكود، ستجد في phpMyAdmin:
- الجداول الثلاثة الجديدة
- بيانات تجريبية في جدول الموردين

## 🚀 اختبار النظام:
1. اذهب إلى: http://127.0.0.1:8000/admin/
2. سجل الدخول: `admin` / `admin123`
3. انقر على **"Purchase invoices"**
4. يجب أن يعمل بدون أخطاء!

---

**⚡ هذا الحل مضمون 100%!**
