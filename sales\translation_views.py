"""
Views for handling translation and language switching
"""

from django.http import JsonResponse, HttpResponseRedirect
from django.shortcuts import redirect
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import translation
from django.conf import settings
from django.contrib import messages
from sales.translation_engine import TranslationEngine
import json

@require_http_methods(["GET", "POST"])
def set_language(request):
    """
    Enhanced language switching view
    """
    # Get language from POST or GET
    if request.method == 'POST':
        language = request.POST.get('language')
    else:
        language = request.GET.get('language')
    
    # Validate language
    if language not in ['ar', 'fr']:
        language = settings.LANGUAGE_CODE
    
    # Set language in session
    request.session['django_language'] = language
    
    # Activate language immediately
    translation.activate(language)
    
    # Get redirect URL
    next_url = request.POST.get('next') or request.GET.get('next') or request.META.get('HTTP_REFERER', '/')
    
    # Handle AJAX requests (including fetch requests)
    if (request.headers.get('X-Requested-With') == 'XMLHttpRequest' or
        request.headers.get('Content-Type') == 'application/x-www-form-urlencoded' or
        'application/json' in request.headers.get('Accept', '')):

        response = JsonResponse({
            'success': True,
            'language': language,
            'language_name': TranslationEngine.get_language_name(language),
            'direction': TranslationEngine.get_language_direction(language),
            'redirect_url': next_url,
            'message': f'Language switched to {language}'
        })

        # Set language cookie for AJAX requests too
        response.set_cookie(
            'django_language',
            language,
            max_age=365*24*60*60,
            httponly=False,
            samesite='Lax'
        )

        return response
    
    # Handle regular requests
    response = HttpResponseRedirect(next_url)
    response.set_cookie('django_language', language, max_age=365*24*60*60)  # 1 year
    
    return response

@csrf_exempt
@require_http_methods(["POST"])
def set_language_no_csrf(request):
    """
    Language switching view without CSRF protection for testing
    """
    try:
        language = request.POST.get('language')

        if not language:
            return JsonResponse({'error': 'No language specified'}, status=400)

        if language not in ['ar', 'fr']:
            return JsonResponse({'error': 'Invalid language'}, status=400)

        # Set language in session
        request.session['django_language'] = language

        # Activate language immediately
        translation.activate(language)

        # Create response
        response = JsonResponse({
            'success': True,
            'language': language,
            'language_name': TranslationEngine.get_language_name(language),
            'direction': TranslationEngine.get_language_direction(language),
            'message': f'Language switched to {language}'
        })

        # Set language cookie
        response.set_cookie(
            'django_language',
            language,
            max_age=365*24*60*60,
            httponly=False,
            samesite='Lax'
        )

        return response

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def translate_text(request):
    """
    API endpoint for bidirectional text translation
    """
    try:
        # Handle both JSON and form data
        if request.content_type == 'application/json':
            data = json.loads(request.body)
        else:
            data = request.POST

        text = data.get('text', '')
        target_language = data.get('language', 'fr')
        source_language = data.get('source_language', 'ar')
        auto_detect = data.get('auto_detect', False)

        if not text:
            return JsonResponse({'error': 'No text provided'}, status=400)

        if target_language not in ['ar', 'fr']:
            return JsonResponse({'error': 'Invalid target language'}, status=400)

        if source_language not in ['ar', 'fr']:
            return JsonResponse({'error': 'Invalid source language'}, status=400)

        # Auto-detect source language if requested
        if auto_detect:
            translated = TranslationEngine.auto_translate(text, target_language)
            detected_source = 'ar' if any(c in 'ابتثجحخدذرزسشصضطظعغفقكلمنهوي' for c in text) else 'fr'
        else:
            translated = TranslationEngine.translate(text, target_language, source_language)
            detected_source = source_language

        return JsonResponse({
            'success': True,
            'original': text,
            'translated': translated,
            'source_language': detected_source,
            'target_language': target_language,
            'source_direction': TranslationEngine.get_language_direction(detected_source),
            'target_direction': TranslationEngine.get_language_direction(target_language),
            'auto_detected': auto_detect
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@require_http_methods(["GET"])
def get_translations(request):
    """
    API endpoint for getting bidirectional translations
    """
    target_language = request.GET.get('language', 'fr')
    source_language = request.GET.get('source_language', 'ar')
    direction = request.GET.get('direction', 'single')  # single, reverse, both

    if target_language not in ['ar', 'fr']:
        return JsonResponse({'error': 'Invalid target language'}, status=400)

    if source_language not in ['ar', 'fr']:
        return JsonResponse({'error': 'Invalid source language'}, status=400)

    if direction == 'both':
        # Get both directions
        translations = TranslationEngine.get_all_translations()
        total_count = sum(len(trans) for trans in translations.values())
    elif direction == 'reverse':
        # Get reverse direction
        translations = TranslationEngine.get_all_translations(source_language, target_language)
        total_count = len(translations)
    else:
        # Get single direction
        translations = TranslationEngine.get_all_translations(target_language, source_language)
        total_count = len(translations)

    # Get translation statistics
    stats = TranslationEngine.get_translation_stats()

    return JsonResponse({
        'success': True,
        'source_language': source_language,
        'target_language': target_language,
        'direction': direction,
        'translations': translations,
        'count': total_count,
        'stats': stats,
        'source_direction': TranslationEngine.get_language_direction(source_language),
        'target_direction': TranslationEngine.get_language_direction(target_language)
    })

@require_http_methods(["POST"])
def translate_page(request):
    """
    API endpoint for translating entire page content
    """
    try:
        data = json.loads(request.body)
        content = data.get('content', '')
        target_language = data.get('language', 'fr')
        
        if not content:
            return JsonResponse({'error': 'No content provided'}, status=400)
        
        if target_language not in ['ar', 'fr']:
            return JsonResponse({'error': 'Invalid language'}, status=400)
        
        translated_content = TranslationEngine.translate_page_content(content, target_language)
        
        return JsonResponse({
            'success': True,
            'original_content': content,
            'translated_content': translated_content,
            'language': target_language,
            'direction': TranslationEngine.get_language_direction(target_language)
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@require_http_methods(["GET"])
def language_info(request):
    """
    Get current language information
    """
    current_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
    
    return JsonResponse({
        'current_language': current_language,
        'language_name': TranslationEngine.get_language_name(current_language),
        'direction': TranslationEngine.get_language_direction(current_language),
        'is_rtl': current_language == 'ar',
        'available_languages': [
            {
                'code': code,
                'name': name,
                'direction': TranslationEngine.get_language_direction(code),
                'is_current': code == current_language
            }
            for code, name in settings.LANGUAGES
        ]
    })

def admin_set_language(request):
    """
    Language switching specifically for Django Admin
    """
    language = request.GET.get('language') or request.POST.get('language')
    
    if language in ['ar', 'fr']:
        request.session['django_language'] = language
        translation.activate(language)
        
        # Add success message
        if language == 'ar':
            messages.success(request, 'تم تغيير اللغة إلى العربية بنجاح')
        else:
            messages.success(request, 'Langue changée en français avec succès')
    
    # Redirect back to admin
    next_url = request.GET.get('next', '/admin/')
    return redirect(next_url)

@require_http_methods(["GET"])
def translation_debug(request):
    """
    Debug endpoint for translation issues
    """
    current_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
    
    debug_info = {
        'current_language': current_language,
        'session_language': request.session.get('django_language'),
        'django_language': translation.get_language(),
        'available_languages': settings.LANGUAGES,
        'locale_paths': getattr(settings, 'LOCALE_PATHS', []),
        'use_i18n': settings.USE_I18N,
        'language_code': settings.LANGUAGE_CODE,
        'translation_engine_available': True,
        'sample_translations': {
            'ar_to_fr': TranslationEngine.translate('نظام إدارة المبيعات', 'fr'),
            'status_test': TranslationEngine.translate('مدفوعة', 'fr'),
        },
        'request_info': {
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'accept_language': request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
        }
    }
    
    return JsonResponse(debug_info, json_dumps_params={'ensure_ascii': False, 'indent': 2})

@csrf_exempt
@require_http_methods(["POST"])
def auto_translate_text(request):
    """
    API endpoint for auto-detection and translation
    """
    try:
        # Handle both JSON and form data
        if request.content_type == 'application/json':
            data = json.loads(request.body)
        else:
            data = request.POST

        text = data.get('text', '')
        target_language = data.get('target_language', 'fr')

        if not text:
            return JsonResponse({'error': 'No text provided'}, status=400)

        if target_language not in ['ar', 'fr']:
            return JsonResponse({'error': 'Invalid target language'}, status=400)

        # Auto-detect and translate
        translated = TranslationEngine.auto_translate(text, target_language)

        # Detect source language
        arabic_chars = set('ابتثجحخدذرزسشصضطظعغفقكلمنهوي')
        detected_source = 'ar' if any(c in arabic_chars for c in text) else 'fr'

        return JsonResponse({
            'success': True,
            'original': text,
            'translated': translated,
            'detected_source': detected_source,
            'target_language': target_language,
            'confidence': 'high' if translated != text else 'low'
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@require_http_methods(["GET"])
def search_translations_api(request):
    """
    API endpoint for searching translations
    """
    query = request.GET.get('query', '')
    direction = request.GET.get('direction', 'both')

    if not query:
        return JsonResponse({'error': 'No search query provided'}, status=400)

    results = TranslationEngine.search_translations(query, direction)

    # Count results
    total_results = 0
    for direction_results in results.values():
        total_results += len(direction_results)

    return JsonResponse({
        'success': True,
        'query': query,
        'direction': direction,
        'results': results,
        'total_results': total_results
    })

@require_http_methods(["GET"])
def validate_translations_api(request):
    """
    API endpoint for validating translation consistency
    """
    validation_result = TranslationEngine.validate_translation_consistency()

    return JsonResponse({
        'success': True,
        'validation': validation_result,
        'is_consistent': validation_result['is_consistent'],
        'issues_count': len(validation_result['inconsistencies'])
    })

@require_http_methods(["GET"])
def translation_statistics(request):
    """
    API endpoint for translation statistics
    """
    stats = TranslationEngine.get_translation_stats()

    # Additional statistics
    ar_to_fr_translations = TranslationEngine.get_all_translations('fr', 'ar')
    fr_to_ar_translations = TranslationEngine.get_all_translations('ar', 'fr')

    # Calculate coverage
    common_terms = set(ar_to_fr_translations.values()).intersection(set(fr_to_ar_translations.keys()))
    coverage_percentage = (len(common_terms) / max(len(ar_to_fr_translations), 1)) * 100

    enhanced_stats = {
        **stats,
        'coverage_percentage': round(coverage_percentage, 2),
        'common_terms': len(common_terms),
        'unique_ar_terms': len(set(ar_to_fr_translations.keys())),
        'unique_fr_terms': len(set(fr_to_ar_translations.keys())),
        'supported_languages': ['ar', 'fr'],
        'directions': ['ar_to_fr', 'fr_to_ar']
    }

    return JsonResponse({
        'success': True,
        'statistics': enhanced_stats
    })

@csrf_exempt
@require_http_methods(["POST"])
def batch_translate(request):
    """
    API endpoint for batch translation
    """
    try:
        # Handle both JSON and form data
        if request.content_type == 'application/json':
            data = json.loads(request.body)
        else:
            data = request.POST

        texts = data.get('texts', [])
        if isinstance(texts, str):
            texts = json.loads(texts)

        target_language = data.get('target_language', 'fr')
        source_language = data.get('source_language', 'ar')

        if not texts:
            return JsonResponse({'error': 'No texts provided'}, status=400)

        if not isinstance(texts, list):
            return JsonResponse({'error': 'Texts must be a list'}, status=400)

        if target_language not in ['ar', 'fr']:
            return JsonResponse({'error': 'Invalid target language'}, status=400)

        # Translate all texts
        translated_texts = TranslationEngine.translate_multiple(
            texts, target_language, source_language
        )

        # Create result pairs
        translation_pairs = [
            {
                'original': original,
                'translated': translated,
                'source_language': source_language,
                'target_language': target_language
            }
            for original, translated in zip(texts, translated_texts)
        ]

        return JsonResponse({
            'success': True,
            'translations': translation_pairs,
            'count': len(translation_pairs),
            'source_language': source_language,
            'target_language': target_language
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
