{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        تعديل المورد - {{ object.name }}
    {% else %}
        إضافة مورد جديد
    {% endif %}
    - {{ block.super }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{% url 'sales:supplier_list' %}">الموردين</a></li>
                <li class="breadcrumb-item active">
                    {% if object %}
                        تعديل المورد
                    {% else %}
                        إضافة مورد جديد
                    {% endif %}
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-truck"></i>
                    {% if object %}
                        تعديل المورد: {{ object.name }}
                    {% else %}
                        إضافة مورد جديد
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <i class="fas fa-building"></i> {{ form.name.label }}
                                {% if form.name.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.contact_person.id_for_label }}" class="form-label">
                                <i class="fas fa-user"></i> {{ form.contact_person.label }}
                            </label>
                            {{ form.contact_person }}
                            {% if form.contact_person.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.contact_person.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope"></i> {{ form.email.label }}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                <i class="fas fa-phone"></i> {{ form.phone.label }}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.phone.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.tax_number.id_for_label }}" class="form-label">
                                <i class="fas fa-receipt"></i> {{ form.tax_number.label }}
                            </label>
                            {{ form.tax_number }}
                            {% if form.tax_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.tax_number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.payment_terms.id_for_label }}" class="form-label">
                                <i class="fas fa-credit-card"></i> {{ form.payment_terms.label }}
                            </label>
                            {{ form.payment_terms }}
                            {% if form.payment_terms.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.payment_terms.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">
                            <i class="fas fa-map-marker-alt"></i> {{ form.address.label }}
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.address.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                        </div>
                        {% if form.is_active.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.is_active.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'sales:supplier_list' %}" class="btn btn-secondary" id="cancel-btn">
                            <i class="fas fa-arrow-left"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if object %}
                                تحديث المورد
                            {% else %}
                                حفظ المورد
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Check if opened in popup
    var isPopup = window.opener && window.opener !== window;
    
    if (isPopup) {
        // Hide navigation and modify layout for popup
        $('.navbar').hide();
        $('footer').hide();
        $('.container-fluid').removeClass('container-fluid').addClass('container-sm');
        
        // Change cancel button behavior
        $('#cancel-btn, a[href*="supplier_list"]').on('click', function(e) {
            e.preventDefault();
            window.close();
        });
    }
    
    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        
        // Check required fields
        $('[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        // Validate email format
        var email = $('#{{ form.email.id_for_label }}').val();
        if (email && !isValidEmail(email)) {
            $('#{{ form.email.id_for_label }}').addClass('is-invalid');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            return;
        }
        
        // If popup and form is valid, handle AJAX submission
        if (isPopup) {
            e.preventDefault();
            submitSupplierForm();
        }
    });
    
    // Real-time validation
    $('input').on('blur', function() {
        if ($(this).attr('required') && !$(this).val().trim()) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Email validation
    $('#{{ form.email.id_for_label }}').on('blur', function() {
        var email = $(this).val();
        if (email && !isValidEmail(email)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});

function isValidEmail(email) {
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function submitSupplierForm() {
    var formData = new FormData($('form')[0]);
    
    $.ajax({
        url: $('form').attr('action') || window.location.href,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                // Send supplier data to parent window
                window.opener.postMessage({
                    type: 'supplierAdded',
                    supplier: response.supplier
                }, '*');
                window.close();
            } else {
                // Handle form errors
                if (response.errors) {
                    displayFormErrors(response.errors);
                }
            }
        },
        error: function() {
            alert('حدث خطأ أثناء حفظ المورد');
        }
    });
}

function displayFormErrors(errors) {
    // Clear previous errors
    $('.is-invalid').removeClass('is-invalid');
    $('.invalid-feedback').remove();
    
    // Display new errors
    for (var field in errors) {
        var fieldElement = $('#id_' + field);
        fieldElement.addClass('is-invalid');
        fieldElement.after('<div class="invalid-feedback d-block">' + errors[field][0] + '</div>');
    }
}
</script>
{% endblock %}
