{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "تعديل الفئة" %} - {{ object.name }}
    {% else %}
        {% trans "إضافة فئة جديدة" %}
    {% endif %}
    - {{ block.super }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">{% trans "لوحة التحكم" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'sales:category_list' %}">{% trans "الفئات" %}</a></li>
                <li class="breadcrumb-item active">
                    {% if object %}
                        {% trans "تعديل الفئة" %}
                    {% else %}
                        {% trans "إضافة فئة جديدة" %}
                    {% endif %}
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mx-auto">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-folder"></i>
                    {% if object %}
                        {% trans "تعديل الفئة" %}: {{ object.name }}
                    {% else %}
                        {% trans "إضافة فئة جديدة" %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">
                            <i class="fas fa-tag"></i> {{ form.name.label }}
                            {% if form.name.field.required %}<span class="text-danger">*</span>{% endif %}
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.name.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            {% trans "اختر اسماً وصفياً للفئة يساعد في تنظيم المنتجات" %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            <i class="fas fa-align-left"></i> {{ form.description.label }}
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.description.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            {% trans "وصف مختصر للفئة وأنواع المنتجات التي تحتويها" %}
                        </div>
                    </div>

                    <!-- Preview Section -->
                    {% if object %}
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-info-circle"></i> {% trans "معلومات الفئة" %}
                        </h6>
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1">
                                        <strong>{% trans "تاريخ الإنشاء" %}:</strong> 
                                        {{ object.created_at|date:"Y-m-d H:i" }}
                                    </p>
                                    <p class="mb-0">
                                        <strong>{% trans "عدد المنتجات" %}:</strong> 
                                        {{ object.product_set.count }}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    {% if object.product_set.count > 0 %}
                                        <a href="{% url 'sales:product_list' %}?category={{ object.id }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> {% trans "عرض المنتجات" %}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">{% trans "لا توجد منتجات في هذه الفئة" %}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'sales:category_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "إلغاء" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if object %}
                                {% trans "تحديث الفئة" %}
                            {% else %}
                                {% trans "إضافة الفئة" %}
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt"></i> {% trans "إجراءات سريعة" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="{% url 'sales:category_list' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-list"></i> {% trans "عرض جميع الفئات" %}
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{% url 'sales:product_add' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-plus"></i> {% trans "إضافة منتج جديد" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        
        // Check required fields
        $('[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            showErrorMessage('{% trans "يرجى تصحيح الأخطاء في النموذج" %}');
        }
    });
    
    // Real-time validation
    $('input, textarea').on('blur', function() {
        if ($(this).attr('required') && !$(this).val().trim()) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Character counter for description
    $('#{{ form.description.id_for_label }}').on('input', function() {
        var maxLength = 500; // Adjust as needed
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;
        
        // Remove existing counter
        $(this).siblings('.char-counter').remove();
        
        // Add counter
        var counterClass = remaining < 50 ? 'text-warning' : 'text-muted';
        if (remaining < 0) counterClass = 'text-danger';
        
        $(this).after('<div class="char-counter small ' + counterClass + '">' + 
                     currentLength + ' / ' + maxLength + ' {% trans "حرف" %}</div>');
    });
    
    // Auto-focus on name field
    $('#{{ form.name.id_for_label }}').focus();
});
</script>
{% endblock %}
