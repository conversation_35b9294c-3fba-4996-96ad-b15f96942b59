{% load static %}
{% load translation_extras %}

<!-- Smart Language Switcher with Real-time Translation -->
<div class="smart-language-switcher" id="smart-lang-switcher">
    <div class="current-language" id="current-lang-display">
        <i class="fas fa-globe"></i>
        <span id="lang-name">{% get_language_name %}</span>
        <i class="fas fa-chevron-down"></i>
    </div>
    
    <div class="language-options" id="lang-options">
        {% if request.LANGUAGE_CODE != 'ar' %}
            <div class="lang-option" data-lang="ar" data-name="العربية">
                <i class="fas fa-language"></i>
                <span>العربية</span>
            </div>
        {% endif %}
        
        {% if request.LANGUAGE_CODE != 'fr' %}
            <div class="lang-option" data-lang="fr" data-name="Français">
                <i class="fas fa-language"></i>
                <span>Français</span>
            </div>
        {% endif %}
    </div>
</div>

<style>
.smart-language-switcher {
    position: fixed;
    top: 20px;
    {% if request.direction == 'rtl' %}
        left: 20px;
    {% else %}
        right: 20px;
    {% endif %}
    z-index: 9999;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.2);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 180px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.smart-language-switcher:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.4);
}

.current-language {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    color: white;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.2s ease;
    user-select: none;
}

.current-language:hover {
    background: rgba(255,255,255,0.1);
}

.current-language i:first-child {
    margin-{% if request.direction == 'rtl' %}left{% else %}right{% endif %}: 8px;
    font-size: 16px;
}

.current-language i:last-child {
    margin-{% if request.direction == 'rtl' %}right{% else %}left{% endif %}: 8px;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.smart-language-switcher.open .current-language i:last-child {
    transform: rotate(180deg);
}

.language-options {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

.smart-language-switcher.open .language-options {
    max-height: 200px;
}

.lang-option {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    border-top: 1px solid rgba(255,255,255,0.1);
    font-weight: 500;
    font-size: 14px;
}

.lang-option:hover {
    background: rgba(255,255,255,0.2);
    transform: translateX({% if request.direction == 'rtl' %}-5px{% else %}5px{% endif %});
}

.lang-option i {
    margin-{% if request.direction == 'rtl' %}left{% else %}right{% endif %}: 10px;
    font-size: 14px;
    opacity: 0.8;
}

.lang-option.switching {
    background: rgba(255,255,255,0.3);
    pointer-events: none;
}

.lang-option.switching::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-{% if request.direction == 'rtl' %}right{% else %}left{% endif %}: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .smart-language-switcher {
        top: 10px;
        {% if request.direction == 'rtl' %}
            left: 10px;
        {% else %}
            right: 10px;
        {% endif %}
        min-width: 150px;
    }
    
    .current-language,
    .lang-option {
        padding: 10px 15px;
        font-size: 13px;
    }
}

/* Animation for language switch */
.page-transition {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.translation-loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 20px 30px;
    border-radius: 10px;
    z-index: 10000;
    display: none;
}

.translation-loading.show {
    display: block;
}
</style>

<div class="translation-loading" id="translation-loading">
    <i class="fas fa-spinner fa-spin"></i>
    <span>{% trans_fallback "جاري تبديل اللغة..." %}</span>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const switcher = document.getElementById('smart-lang-switcher');
    const currentLang = document.getElementById('current-lang-display');
    const langOptions = document.getElementById('lang-options');
    const langName = document.getElementById('lang-name');
    const loadingDiv = document.getElementById('translation-loading');
    
    // Toggle dropdown
    currentLang.addEventListener('click', function(e) {
        e.stopPropagation();
        switcher.classList.toggle('open');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        switcher.classList.remove('open');
    });
    
    // Handle language switching
    document.querySelectorAll('.lang-option').forEach(function(option) {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const language = this.getAttribute('data-lang');
            const languageName = this.getAttribute('data-name');
            
            // Prevent multiple clicks
            if (this.classList.contains('switching')) {
                return;
            }
            
            // Visual feedback
            this.classList.add('switching');
            langName.textContent = languageName;
            switcher.classList.remove('open');
            
            // Show loading
            loadingDiv.classList.add('show');
            document.body.classList.add('page-transition');
            
            // Switch language
            switchLanguage(language).then(() => {
                // Hide loading and reload
                loadingDiv.classList.remove('show');
                document.body.classList.remove('page-transition');
                window.location.reload();
            }).catch(error => {
                console.error('Language switch error:', error);
                // Fallback: reload anyway
                window.location.reload();
            });
        });
    });
    
    // Language switching function
    function switchLanguage(language) {
        return new Promise((resolve, reject) => {
            // Method 1: Try AJAX request
            fetch('/set-language/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCsrfToken()
                },
                body: 'language=' + encodeURIComponent(language)
            })
            .then(response => {
                if (response.ok) {
                    resolve();
                } else {
                    // Method 2: URL parameter fallback
                    window.location.href = updateUrlParameter(window.location.href, 'language', language);
                }
            })
            .catch(error => {
                // Method 3: Session storage + reload
                sessionStorage.setItem('preferred_language', language);
                window.location.reload();
            });
        });
    }
    
    // Helper functions
    function getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    function updateUrlParameter(url, param, paramVal) {
        let newAdditionalURL = "";
        let tempArray = url.split("?");
        let baseURL = tempArray[0];
        let additionalURL = tempArray[1];
        let temp = "";
        
        if (additionalURL) {
            tempArray = additionalURL.split("&");
            for (let i = 0; i < tempArray.length; i++) {
                if (tempArray[i].split('=')[0] != param) {
                    newAdditionalURL += temp + tempArray[i];
                    temp = "&";
                }
            }
        }
        
        let rows_txt = temp + "" + param + "=" + paramVal;
        return baseURL + "?" + newAdditionalURL + rows_txt;
    }
    
    // Auto-hide after 5 seconds of inactivity
    let hideTimer;
    function resetHideTimer() {
        clearTimeout(hideTimer);
        hideTimer = setTimeout(() => {
            switcher.classList.remove('open');
        }, 5000);
    }
    
    switcher.addEventListener('mouseenter', resetHideTimer);
    switcher.addEventListener('mouseleave', resetHideTimer);
    
    // Initialize
    resetHideTimer();
});
</script>
