{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}قائمة الموردين - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">الموردين</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-truck"></i> قائمة الموردين
                </h5>
                <a href="{% url 'sales:supplier_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة مورد جديد
                </a>
            </div>
            <div class="card-body">
                <!-- Search Form -->
                <form method="get" class="mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" 
                                       placeholder="البحث عن مورد..." value="{{ request.GET.search }}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Suppliers Table -->
                {% if suppliers %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم المورد</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>الشخص المسؤول</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in suppliers %}
                            <tr>
                                <td>
                                    <strong>{{ supplier.name }}</strong>
                                </td>
                                <td>{{ supplier.email|default:"-" }}</td>
                                <td>{{ supplier.phone|default:"-" }}</td>
                                <td>{{ supplier.contact_person|default:"-" }}</td>
                                <td>
                                    {% if supplier.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ supplier.date_created|date:"Y-m-d" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'sales:supplier_detail' supplier.pk %}" 
                                           class="btn btn-sm btn-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'sales:supplier_edit' supplier.pk %}" 
                                           class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'sales:supplier_delete' supplier.pk %}" 
                                           class="btn btn-sm btn-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المورد؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد موردين</h5>
                    <p class="text-muted">ابدأ بإضافة مورد جديد</p>
                    <a href="{% url 'sales:supplier_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مورد جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
