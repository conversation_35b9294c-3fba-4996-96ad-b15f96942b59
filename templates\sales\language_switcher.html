{% load i18n %}
{% load translation_extras %}

<!-- Fast Language Switcher -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle language-indicator" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i>
        {% get_current_language_code as current_lang %}
        {% if current_lang == 'ar' %}العربية{% else %}Français{% endif %}
    </a>
    <ul class="dropdown-menu">
        {% get_available_languages as LANGUAGES %}
        {% get_current_language_code as current_lang %}
        {% for lang_code, lang_name in LANGUAGES %}
            {% if lang_code != current_lang %}
                <li>
                    <form method="post" action="/set-language/" class="d-inline fast-lang-form">
                        {% csrf_token %}
                        <input type="hidden" name="language" value="{{ lang_code }}">
                        <button type="submit" class="dropdown-item border-0 bg-transparent w-100 text-start">
                            <i class="fas fa-language"></i> {{ lang_name }}
                        </button>
                    </form>
                </li>
            {% endif %}
        {% endfor %}
    </ul>
</li>

<style>
.fast-lang-form button {
    cursor: pointer;
    transition: background-color 0.2s;
}
.fast-lang-form button:hover {
    background-color: #f8f9fa !important;
}
</style>
