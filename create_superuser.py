#!/usr/bin/env python
"""Create superuser script"""
import os
import sys
import django
from django.contrib.auth.hashers import make_password
import sqlite3
from datetime import datetime

def create_superuser():
    """Create superuser directly in database"""
    try:
        # Connect to database
        conn = sqlite3.connect('db.sqlite3')
        cursor = conn.cursor()
        
        # Check if superuser already exists
        cursor.execute("SELECT COUNT(*) FROM auth_user WHERE username = 'admin'")
        if cursor.fetchone()[0] > 0:
            print("Superuser 'admin' already exists")
            return True
        
        # Create superuser
        password_hash = make_password('admin123')
        now = datetime.now().isoformat()
        
        cursor.execute("""
            INSERT INTO auth_user 
            (password, last_login, is_superuser, username, first_name, last_name, 
             email, is_staff, is_active, date_joined)
            VALUES (?, NULL, 1, 'admin', '', '', '<EMAIL>', 1, 1, ?)
        """, (password_hash, now))
        
        conn.commit()
        conn.close()
        
        print("Superuser 'admin' created successfully!")
        print("Username: admin")
        print("Password: admin123")
        return True
        
    except Exception as e:
        print(f"Error creating superuser: {e}")
        return False

if __name__ == '__main__':
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
    django.setup()
    
    success = create_superuser()
    sys.exit(0 if success else 1)
