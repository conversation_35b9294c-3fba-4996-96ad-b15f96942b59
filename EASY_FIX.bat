@echo off
color 0A
echo ========================================
echo 🔧 حل سهل لمشكلة قاعدة البيانات
echo ========================================
echo.
echo اختر الحل المناسب:
echo.
echo 1. حل Django (بدون PyMySQL)
echo 2. حل phpMyAdmin (SQL)
echo 3. تثبيت PyMySQL ثم الحل
echo.
set /p choice="اختر (1/2/3): "

if "%choice%"=="1" goto django_fix
if "%choice%"=="2" goto phpmyadmin_fix
if "%choice%"=="3" goto install_pymysql
goto invalid

:django_fix
echo.
echo 🔧 تطبيق حل Django...
python django_fix.py
goto end

:phpmyadmin_fix
echo.
echo 📋 حل phpMyAdmin:
echo.
echo 1. افتح: http://localhost/phpmyadmin/
echo 2. اختر قاعدة البيانات: sales_system
echo 3. انقر تبويب SQL
echo 4. انسخ محتوى ملف SIMPLE_FIX.sql
echo 5. الصق والنقر Go
echo.
start http://localhost/phpmyadmin/
pause
goto end

:install_pymysql
echo.
echo 📦 تثبيت PyMySQL...
pip install PyMySQL
echo.
echo 🔧 تطبيق الحل...
python simple_fix.py
goto end

:invalid
echo.
echo ❌ اختيار غير صحيح!
pause
goto end

:end
echo.
echo 🚀 تشغيل النظام...
python manage.py runserver
pause
