{% load i18n %}
{% load translation_extras %}

<!-- Session-Based Language Switcher -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i> 
        <span id="current-lang-text">
            {% if request.session.django_language == 'fr' %}
                Français
            {% else %}
                العربية
            {% endif %}
        </span>
    </a>
    <ul class="dropdown-menu">
        {% if request.session.django_language == 'fr' %}
            <li>
                <a href="#" class="dropdown-item session-lang-switch" data-lang="ar" data-name="العربية">
                    <i class="fas fa-language"></i> العربية
                </a>
            </li>
        {% else %}
            <li>
                <a href="#" class="dropdown-item session-lang-switch" data-lang="fr" data-name="Français">
                    <i class="fas fa-language"></i> Français
                </a>
            </li>
        {% endif %}
    </ul>
</li>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Session-based language switching
    document.querySelectorAll('.session-lang-switch').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const language = this.getAttribute('data-lang');
            const langName = this.getAttribute('data-name');
            const currentLangText = document.getElementById('current-lang-text');
            
            // Immediate visual feedback
            currentLangText.textContent = langName;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
            
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/set-language/';
            form.style.display = 'none';
            
            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
            form.appendChild(csrfInput);
            
            // Add language
            const langInput = document.createElement('input');
            langInput.type = 'hidden';
            langInput.name = 'language';
            langInput.value = language;
            form.appendChild(langInput);
            
            // Add next URL
            const nextInput = document.createElement('input');
            nextInput.type = 'hidden';
            nextInput.name = 'next';
            nextInput.value = window.location.pathname;
            form.appendChild(nextInput);
            
            // Submit form
            document.body.appendChild(form);
            form.submit();
        });
    });
});
</script>

<style>
.session-lang-switch {
    cursor: pointer;
    transition: background-color 0.2s;
}
.session-lang-switch:hover {
    background-color: #f8f9fa !important;
}
.language-switcher .dropdown-toggle {
    transition: all 0.2s ease;
}
</style>
