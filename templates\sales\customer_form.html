{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "تعديل العميل" %} - {{ object.name }}
    {% else %}
        {% trans "إضافة عميل جديد" %}
    {% endif %}
    - {{ block.super }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">{% trans "لوحة التحكم" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'sales:customer_list' %}">{% trans "العملاء" %}</a></li>
                <li class="breadcrumb-item active">
                    {% if object %}
                        {% trans "تعديل العميل" %}
                    {% else %}
                        {% trans "إضافة عميل جديد" %}
                    {% endif %}
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i>
                    {% if object %}
                        {% trans "تعديل العميل" %}: {{ object.name }}
                    {% else %}
                        {% trans "إضافة عميل جديد" %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <i class="fas fa-user"></i> {{ form.name.label }}
                                {% if form.name.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope"></i> {{ form.email.label }}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                <i class="fas fa-phone"></i> {{ form.phone.label }}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.phone.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">
                            <i class="fas fa-map-marker-alt"></i> {{ form.address.label }}
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.address.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'sales:customer_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "إلغاء" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if object %}
                                {% trans "تحديث العميل" %}
                            {% else %}
                                {% trans "إضافة العميل" %}
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Check if opened in popup
    var isPopup = window.opener && window.opener !== window;

    if (isPopup) {
        // Hide navigation and modify layout for popup
        $('.navbar').hide();
        $('footer').hide();
        $('.container-fluid').removeClass('container-fluid').addClass('container-sm');

        // Change cancel button behavior
        $('#cancel-btn, a[href*="customer_list"]').on('click', function(e) {
            e.preventDefault();
            window.close();
        });
    }

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $('[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        // Validate email format
        var email = $('#{{ form.email.id_for_label }}').val();
        if (email && !isValidEmail(email)) {
            $('#{{ form.email.id_for_label }}').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            showErrorMessage('يرجى تصحيح الأخطاء في النموذج');
            return;
        }

        // If popup and form is valid, handle AJAX submission
        if (isPopup) {
            e.preventDefault();
            submitCustomerForm();
        }
    });
    
    // Real-time validation
    $('input').on('blur', function() {
        if ($(this).attr('required') && !$(this).val().trim()) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Email validation
    $('#{{ form.email.id_for_label }}').on('blur', function() {
        var email = $(this).val();
        if (email && !isValidEmail(email)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});

function isValidEmail(email) {
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function submitCustomerForm() {
    var formData = new FormData($('form')[0]);

    $.ajax({
        url: $('form').attr('action') || window.location.href,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                // Send customer data to parent window
                window.opener.postMessage({
                    type: 'customerAdded',
                    customer: response.customer
                }, '*');
                window.close();
            } else {
                // Handle form errors
                if (response.errors) {
                    displayFormErrors(response.errors);
                }
            }
        },
        error: function() {
            alert('حدث خطأ أثناء حفظ العميل');
        }
    });
}

function displayFormErrors(errors) {
    // Clear previous errors
    $('.is-invalid').removeClass('is-invalid');
    $('.invalid-feedback').remove();

    // Display new errors
    for (var field in errors) {
        var fieldElement = $('#id_' + field);
        fieldElement.addClass('is-invalid');
        fieldElement.after('<div class="invalid-feedback d-block">' + errors[field][0] + '</div>');
    }
}
</script>
{% endblock %}
