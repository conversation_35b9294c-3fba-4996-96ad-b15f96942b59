"""
Context processors for providing translation and language information to templates
"""

from django.conf import settings
from django.utils import translation
from sales.translation_engine import TranslationEngine

def translation_context(request):
    """
    Provide translation context to all templates
    """
    current_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
    
    return {
        'current_language': current_language,
        'language_code': current_language,
        'language_name': TranslationEngine.get_language_name(current_language),
        'text_direction': TranslationEngine.get_language_direction(current_language),
        'is_rtl': current_language == 'ar',
        'is_ltr': current_language != 'ar',
        'available_languages': settings.LANGUAGES,
        'translation_engine': TranslationEngine,
    }

def enhanced_language_context(request):
    """
    Enhanced language context with additional features
    """
    current_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
    direction = TranslationEngine.get_language_direction(current_language)
    
    # Language-specific CSS classes
    css_classes = {
        'direction': direction,
        'language': f'lang-{current_language}',
        'text_align': 'right' if direction == 'rtl' else 'left',
        'float_direction': 'right' if direction == 'rtl' else 'left',
    }
    
    # Bootstrap direction classes
    bootstrap_classes = {
        'text_start': 'text-end' if direction == 'rtl' else 'text-start',
        'text_end': 'text-start' if direction == 'rtl' else 'text-end',
        'float_start': 'float-end' if direction == 'rtl' else 'float-start',
        'float_end': 'float-start' if direction == 'rtl' else 'float-end',
        'ms_auto': 'me-auto' if direction == 'rtl' else 'ms-auto',
        'me_auto': 'ms-auto' if direction == 'rtl' else 'me-auto',
    }
    
    return {
        'lang': {
            'code': current_language,
            'name': TranslationEngine.get_language_name(current_language),
            'direction': direction,
            'is_rtl': direction == 'rtl',
            'is_ltr': direction == 'ltr',
            'css': css_classes,
            'bootstrap': bootstrap_classes,
        }
    }

def translation_helpers(request):
    """
    Helper functions for templates
    """
    def translate(text, target_language=None):
        if target_language is None:
            target_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
        return TranslationEngine.translate(text, target_language)
    
    def get_translations(target_language=None):
        if target_language is None:
            target_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
        return TranslationEngine.get_all_translations(target_language)
    
    return {
        'translate': translate,
        'get_translations': get_translations,
    }
