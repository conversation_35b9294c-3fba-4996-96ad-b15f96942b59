{% load translation_extras %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص تبديل اللغة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-bug"></i> تشخيص تبديل اللغة</h3>
                    </div>
                    <div class="card-body">
                        <!-- Current Language Info -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>معلومات اللغة الحالية:</h5>
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>كود اللغة:</strong> 
                                        {% get_current_language_code as current_lang %}
                                        <span class="badge bg-info">{{ current_lang }}</span>
                                    </li>
                                    <li class="list-group-item">
                                        <strong>اسم اللغة:</strong> 
                                        {% if current_lang == 'ar' %}
                                            <span class="badge bg-success">العربية</span>
                                        {% else %}
                                            <span class="badge bg-warning">Français</span>
                                        {% endif %}
                                    </li>
                                    <li class="list-group-item">
                                        <strong>اتجاه النص:</strong> 
                                        {% if current_lang == 'ar' %}
                                            <span class="badge bg-secondary">RTL (من اليمين لليسار)</span>
                                        {% else %}
                                            <span class="badge bg-secondary">LTR (من اليسار لليمين)</span>
                                        {% endif %}
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>اختبار الترجمات:</h5>
                                <ul class="list-group">
                                    <li class="list-group-item">{% trans_fallback "نظام إدارة المبيعات" %}</li>
                                    <li class="list-group-item">{% trans_fallback "لوحة التحكم" %}</li>
                                    <li class="list-group-item">{% trans_fallback "العملاء" %}</li>
                                    <li class="list-group-item">{% trans_fallback "المنتجات" %}</li>
                                    <li class="list-group-item">{% trans_fallback "الفواتير" %}</li>
                                </ul>
                            </div>
                        </div>

                        <hr>

                        <!-- Language Switching Tests -->
                        <h5>اختبارات تبديل اللغة:</h5>
                        
                        <!-- Method 1: GET Links -->
                        <div class="mb-3">
                            <h6>الطريقة 1: روابط GET</h6>
                            <div class="btn-group" role="group">
                                {% if current_lang != 'ar' %}
                                    <a href="/set-language/?language=ar" class="btn btn-primary">
                                        <i class="fas fa-language"></i> العربية (GET)
                                    </a>
                                {% endif %}
                                {% if current_lang != 'fr' %}
                                    <a href="/set-language/?language=fr" class="btn btn-success">
                                        <i class="fas fa-language"></i> Français (GET)
                                    </a>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Method 2: POST Forms -->
                        <div class="mb-3">
                            <h6>الطريقة 2: نماذج POST</h6>
                            <div class="btn-group" role="group">
                                {% if current_lang != 'ar' %}
                                    <form method="post" action="/set-language/" class="d-inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="ar">
                                        <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-language"></i> العربية (POST)
                                        </button>
                                    </form>
                                {% endif %}
                                {% if current_lang != 'fr' %}
                                    <form method="post" action="/set-language/" class="d-inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="fr">
                                        <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                        <button type="submit" class="btn btn-outline-success">
                                            <i class="fas fa-language"></i> Français (POST)
                                        </button>
                                    </form>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Method 3: JavaScript -->
                        <div class="mb-3">
                            <h6>الطريقة 3: JavaScript</h6>
                            <div class="btn-group" role="group">
                                {% if current_lang != 'ar' %}
                                    <button class="btn btn-info js-switch" data-lang="ar">
                                        <i class="fas fa-language"></i> العربية (JS)
                                    </button>
                                {% endif %}
                                {% if current_lang != 'fr' %}
                                    <button class="btn btn-warning js-switch" data-lang="fr">
                                        <i class="fas fa-language"></i> Français (JS)
                                    </button>
                                {% endif %}
                            </div>
                        </div>

                        <hr>

                        <!-- Navigation -->
                        <div class="text-center">
                            <a href="/" class="btn btn-secondary">
                                <i class="fas fa-home"></i> العودة للرئيسية
                            </a>
                            <a href="/test-language/" class="btn btn-secondary">
                                <i class="fas fa-vial"></i> صفحة الاختبار
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // JavaScript language switching
        document.querySelectorAll('.js-switch').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const language = this.getAttribute('data-lang');
                
                // Show loading
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
                this.disabled = true;
                
                // Redirect to language switch URL
                window.location.href = '/set-language/?language=' + language;
            });
        });
    });
    </script>
</body>
</html>
