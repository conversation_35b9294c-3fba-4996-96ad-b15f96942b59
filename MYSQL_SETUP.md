# إعداد قاعدة بيانات MySQL للنظام

## 📋 الخطوات المطلوبة:

### 1️⃣ تشغيل XAMPP/WAMP
- تأكد من تشغيل Apache و MySQL

### 2️⃣ إنشاء قاعدة البيانات
1. افتح phpMyAdmin: http://localhost/phpmyadmin/
2. انقر على "New" أو "جديد"
3. أنشئ قاعدة بيانات باسم: `sales_system`
4. اختر Collation: `utf8mb4_unicode_ci`

### 3️⃣ تطبيق الجداول
#### الطريقة الأولى - ملف SQL:
1. في phpMyAdmin، اختر قاعدة البيانات `sales_system`
2. انقر على تبويب "SQL"
3. انسخ محتوى ملف `create_mysql_tables.sql` والصقه
4. انقر "Go" أو "تنفيذ"

#### الطريقة الثانية - Command Line:
```cmd
mysql -u root -p sales_system < create_mysql_tables.sql
```

### 4️⃣ تشغيل النظام
```cmd
python manage.py runserver
```

## 🎯 بعد التشغيل:

### 📊 في إدارة Django:
- اذهب إلى: http://127.0.0.1:8000/admin/
- ستجد أقسام جديدة:
  - **Suppliers** (الموردين)
  - **Purchase Invoices** (فواتير الشراء)

### 🗄️ في phpMyAdmin:
- اذهب إلى: http://localhost/phpmyadmin/
- اختر قاعدة البيانات `sales_system`
- ستجد الجداول الجديدة:
  - `sales_supplier`
  - `sales_purchaseinvoice`
  - `sales_purchaseinvoiceitem`

## 🔧 استكشاف الأخطاء:

### ❌ خطأ في الاتصال بـ MySQL:
1. تأكد من تشغيل MySQL في XAMPP/WAMP
2. تحقق من كلمة مرور MySQL في ملف `.env`
3. تأكد من وجود قاعدة البيانات `sales_system`

### ❌ خطأ في الجداول:
1. تأكد من تطبيق ملف `create_mysql_tables.sql`
2. تحقق من وجود الجداول في phpMyAdmin

## 📱 بيانات تجريبية:
تم إضافة 3 موردين تجريبيين:
1. شركة التوريدات الذهبية
2. مؤسسة النور للتجارة  
3. شركة الصحراء للمواد الغذائية
