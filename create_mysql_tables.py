#!/usr/bin/env python
"""Create MySQL tables for suppliers and purchase invoices"""
import os
import sys
import django
import pymysql

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
django.setup()

def create_tables():
    """Create tables in MySQL database"""
    try:
        # Connect to MySQL
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='',  # Change if you have a password
            database='sales_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("Connected to MySQL database successfully!")
        
        # Create suppliers table
        print("Creating sales_supplier table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_supplier (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                email VARCHAR(254) NULL,
                phone VARCHAR(20) NULL,
                address TEXT NULL,
                contact_person VARCHAR(100) NULL,
                tax_number VARCHAR(50) NULL,
                payment_terms VARCHAR(100) NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                date_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Create purchase invoices table
        print("Creating sales_purchaseinvoice table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_purchaseinvoice (
                id INT AUTO_INCREMENT PRIMARY KEY,
                invoice_number VARCHAR(50) NOT NULL UNIQUE,
                supplier_id INT NOT NULL,
                user_id INT NOT NULL,
                date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                date_due DATE NOT NULL,
                payment_status VARCHAR(20) NOT NULL DEFAULT 'pending',
                notes TEXT NULL,
                discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
                tax_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
                FOREIGN KEY (supplier_id) REFERENCES sales_supplier(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES auth_user(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Create purchase invoice items table
        print("Creating sales_purchaseinvoiceitem table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_purchaseinvoiceitem (
                id INT AUTO_INCREMENT PRIMARY KEY,
                purchase_invoice_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (purchase_invoice_id) REFERENCES sales_purchaseinvoice(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES sales_product(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # Insert sample suppliers
        print("Inserting sample suppliers...")
        cursor.execute("""
            INSERT IGNORE INTO sales_supplier (name, email, phone, contact_person, is_active) VALUES
            ('شركة التوريدات الذهبية', '<EMAIL>', '+222-45-123456', 'أحمد ولد محمد', TRUE),
            ('مؤسسة النور للتجارة', '<EMAIL>', '+222-45-654321', 'فاطمة بنت أحمد', TRUE),
            ('شركة الصحراء للمواد الغذائية', '<EMAIL>', '+222-45-789012', 'محمد ولد عبدالله', TRUE)
        """)
        
        # Update Django migrations table
        print("Updating Django migrations...")
        cursor.execute("""
            INSERT IGNORE INTO django_migrations (app, name, applied) VALUES 
            ('sales', '0003_supplier_purchaseinvoice_purchaseinvoiceitem', NOW())
        """)
        
        # Commit changes
        connection.commit()
        
        # Show created tables
        cursor.execute("SHOW TABLES LIKE 'sales_%'")
        tables = cursor.fetchall()
        
        print("\nCreated tables:")
        for table in tables:
            print(f"- {table[0]}")
        
        cursor.close()
        connection.close()
        
        print("\nTables created successfully!")
        return True
        
    except Exception as e:
        print(f"Error creating tables: {e}")
        return False

if __name__ == '__main__':
    success = create_tables()
    sys.exit(0 if success else 1)
