-- حل مبسط بدون Foreign Keys
-- انسخ هذا الكود والصقه في phpMyAdmin

USE sales_system;

-- حذف الجداول إذا كانت موجودة
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS sales_purchaseinvoiceitem;
DROP TABLE IF EXISTS sales_purchaseinvoice;
DROP TABLE IF EXISTS sales_supplier;
SET FOREIGN_KEY_CHECKS = 1;

-- إنشاء جدول الموردين
CREATE TABLE sales_supplier (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    email VARCHAR(254) NULL,
    phone VARCHAR(20) NULL,
    address TEXT NULL,
    contact_person VARCHAR(100) NULL,
    tax_number VARCHAR(50) NULL,
    payment_terms VARCHAR(100) NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول فواتير الشراء
CREATE TABLE sales_purchaseinvoice (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_id INT NOT NULL,
    user_id INT NOT NULL,
    date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_due DATE NOT NULL,
    payment_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    notes TEXT NULL,
    discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    tax_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول عناصر فواتير الشراء
CREATE TABLE sales_purchaseinvoiceitem (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إدراج موردين تجريبيين
INSERT INTO sales_supplier (name, email, phone, contact_person, is_active) VALUES
('شركة التوريدات الذهبية', '<EMAIL>', '+222-45-123456', 'أحمد ولد محمد', TRUE),
('مؤسسة النور للتجارة', '<EMAIL>', '+222-45-654321', 'فاطمة بنت أحمد', TRUE),
('شركة الصحراء للمواد الغذائية', '<EMAIL>', '+222-45-789012', 'محمد ولد عبدالله', TRUE);

-- تحديث migrations
INSERT IGNORE INTO django_migrations (app, name, applied) VALUES 
('sales', '0003_supplier_purchaseinvoice_purchaseinvoiceitem', NOW());

-- التحقق من النتائج
SELECT 'تم إنشاء الجداول بنجاح!' as message;
SHOW TABLES LIKE 'sales_%';
SELECT COUNT(*) as 'عدد الموردين' FROM sales_supplier;

-- عرض بنية الجداول
DESCRIBE sales_supplier;
DESCRIBE sales_purchaseinvoice;
DESCRIBE sales_purchaseinvoiceitem;
