-- ========================================
-- فحص الجداول في قاعدة البيانات
-- ========================================
-- انسخ هذا الكود في phpMyAdmin للتحقق من الجداول

USE sales_system;

-- عرض جميع الجداول
SELECT 'جميع الجداول في قاعدة البيانات:' as message;
SHOW TABLES;

-- التحقق من وجود الجداول المطلوبة
SELECT 'التحقق من الجداول المطلوبة:' as message;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ موجود'
        ELSE '❌ غير موجود'
    END as 'sales_supplier'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME = 'sales_supplier';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ موجود'
        ELSE '❌ غير موجود'
    END as 'sales_purchaseinvoice'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME = 'sales_purchaseinvoice';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ موجود'
        ELSE '❌ غير موجود'
    END as 'sales_purchaseinvoiceitem'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME = 'sales_purchaseinvoiceitem';

-- عرض عدد الصفوف في كل جدول
SELECT 'عدد الصفوف في الجداول:' as message;
SELECT 
    TABLE_NAME as 'اسم الجدول',
    TABLE_ROWS as 'عدد الصفوف',
    CREATE_TIME as 'تاريخ الإنشاء'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME LIKE 'sales_%'
ORDER BY TABLE_NAME;

-- عرض بنية الجداول
SELECT 'بنية جدول الموردين:' as message;
SELECT 
    COLUMN_NAME as 'اسم العمود',
    DATA_TYPE as 'نوع البيانات',
    IS_NULLABLE as 'يقبل NULL',
    COLUMN_DEFAULT as 'القيمة الافتراضية'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'sales_system' AND TABLE_NAME = 'sales_supplier'
ORDER BY ORDINAL_POSITION;

-- التحقق من Foreign Keys
SELECT 'المفاتيح الخارجية:' as message;
SELECT 
    CONSTRAINT_NAME as 'اسم القيد',
    TABLE_NAME as 'الجدول',
    COLUMN_NAME as 'العمود',
    REFERENCED_TABLE_NAME as 'الجدول المرجعي',
    REFERENCED_COLUMN_NAME as 'العمود المرجعي'
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'sales_system' 
AND REFERENCED_TABLE_NAME IS NOT NULL
AND TABLE_NAME LIKE 'sales_%'
ORDER BY TABLE_NAME, CONSTRAINT_NAME;
