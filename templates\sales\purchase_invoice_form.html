{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        تعديل فاتورة الشراء - {{ object.invoice_number }}
    {% else %}
        إنشاء فاتورة شراء جديدة
    {% endif %}
    - {{ block.super }}
{% endblock %}

{% block extra_css %}
<style>
    .invoice-item-row {
        background-color: #f8f9fa;
    }
    
    .invoice-item-row:hover {
        background-color: #e9ecef;
    }
    
    .total-cell {
        font-weight: bold;
        color: #28a745;
    }
    
    .product-card {
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
    }
    
    .product-card:hover {
        border-color: #007bff;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 123, 255, 0.075);
        transform: translateY(-1px);
    }
    
    .search-product-row {
        border-left: none;
    }
    
    .input-group .form-control {
        border-right: none;
    }
    
    .input-group .btn {
        border-left: none;
    }
    
    #productSearchModal .modal-body {
        max-height: 500px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{% url 'sales:purchase_invoice_list' %}">فواتير الشراء</a></li>
                <li class="breadcrumb-item active">
                    {% if object %}
                        تعديل فاتورة الشراء
                    {% else %}
                        إنشاء فاتورة شراء جديدة
                    {% endif %}
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice-dollar"></i>
                    {% if object %}
                        تعديل فاتورة الشراء: {{ object.invoice_number }}
                    {% else %}
                        إنشاء فاتورة شراء جديدة
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Supplier and Basic Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.supplier.id_for_label }}" class="form-label">
                                    <i class="fas fa-truck"></i> {{ form.supplier.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    {{ form.supplier }}
                                    <button type="button" class="btn btn-success" id="add-supplier-btn" title="إضافة مورد جديد">
                                        <i class="fas fa-plus"></i> مورد جديد
                                    </button>
                                </div>
                                {% if form.supplier.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.supplier.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.date_due.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar"></i> {{ form.date_due.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.date_due }}
                                {% if form.date_due.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.date_due.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Payment and Discount Info -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.payment_status.id_for_label }}" class="form-label">
                                    <i class="fas fa-credit-card"></i> {{ form.payment_status.label }}
                                </label>
                                {{ form.payment_status }}
                                {% if form.payment_status.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.payment_status.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.discount_percentage.id_for_label }}" class="form-label">
                                    <i class="fas fa-percentage"></i> {{ form.discount_percentage.label }}
                                </label>
                                {{ form.discount_percentage }}
                                {% if form.discount_percentage.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.discount_percentage.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.tax_percentage.id_for_label }}" class="form-label">
                                    <i class="fas fa-receipt"></i> {{ form.tax_percentage.label }}
                                </label>
                                {{ form.tax_percentage }}
                                {% if form.tax_percentage.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.tax_percentage.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Purchase Invoice Items -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-list"></i> عناصر فاتورة الشراء
                            </h6>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-info" id="search-product-btn">
                                    <i class="fas fa-search"></i> البحث عن منتج
                                </button>
                                <button type="button" class="btn btn-sm btn-success" id="add-item">
                                    <i class="fas fa-plus"></i> إضافة عنصر
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            {{ formset.management_form }}
                            
                            <div class="table-responsive">
                                <table class="table table-bordered" id="purchase-invoice-items">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>سعر الوحدة</th>
                                            <th>الإجمالي</th>
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for form in formset %}
                                            <tr class="invoice-item-row">
                                                {% for hidden in form.hidden_fields %}
                                                    {{ hidden }}
                                                {% endfor %}
                                                
                                                <td>
                                                    <div class="input-group">
                                                        {{ form.product }}
                                                        <button type="button" class="btn btn-outline-info btn-sm search-product-row" title="البحث عن منتج">
                                                            <i class="fas fa-search"></i>
                                                        </button>
                                                    </div>
                                                    {% if form.product.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ form.product.errors.0 }}
                                                        </div>
                                                    {% endif %}
                                                </td>
                                                
                                                <td>
                                                    {{ form.quantity }}
                                                    {% if form.quantity.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ form.quantity.errors.0 }}
                                                        </div>
                                                    {% endif %}
                                                </td>
                                                
                                                <td>
                                                    {{ form.unit_price }}
                                                    {% if form.unit_price.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ form.unit_price.errors.0 }}
                                                        </div>
                                                    {% endif %}
                                                </td>
                                                
                                                <td class="total-cell">0.00</td>
                                                
                                                <td>
                                                    {% if not forloop.first %}
                                                        <button type="button" class="btn btn-danger btn-sm remove-item">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    {% endif %}
                                                    {% if form.DELETE %}
                                                        {{ form.DELETE }}
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Purchase Invoice Totals -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-calculator"></i> إجمالي فاتورة الشراء
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 offset-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>المجموع الفرعي:</strong></td>
                                            <td class="text-end"><span id="subtotal">0.00</span> أوقية</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الخصم:</strong></td>
                                            <td class="text-end"><span id="discount-amount">0.00</span> أوقية</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الضريبة:</strong></td>
                                            <td class="text-end"><span id="tax-amount">0.00</span> أوقية</td>
                                        </tr>
                                        <tr class="table-primary">
                                            <td><strong>الإجمالي النهائي:</strong></td>
                                            <td class="text-end"><strong><span id="total-amount">0.00</span> أوقية</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            <i class="fas fa-sticky-note"></i> {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.notes.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'sales:purchase_invoice_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if object %}
                                تحديث فاتورة الشراء
                            {% else %}
                                حفظ فاتورة الشراء
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize purchase invoice form
    initializePurchaseInvoiceForm();

    // Calculate initial totals
    calculatePurchaseInvoiceTotal();

    // Add Supplier Button
    $('#add-supplier-btn').on('click', function() {
        var supplierWindow = window.open('{% url "sales:supplier_add" %}', 'addSupplier', 'width=800,height=600,scrollbars=yes');

        // Listen for supplier added
        window.addEventListener('message', function(event) {
            if (event.data.type === 'supplierAdded') {
                // Refresh supplier dropdown
                refreshSupplierDropdown(event.data.supplier);
                supplierWindow.close();
            }
        });
    });

    // Search Product Button (main)
    $('#search-product-btn').on('click', function() {
        showProductSearchModal();
    });

    // Search Product Button (row)
    $(document).on('click', '.search-product-row', function() {
        var row = $(this).closest('tr');
        showProductSearchModal(row);
    });
});

// Function to refresh supplier dropdown
function refreshSupplierDropdown(newSupplier) {
    var supplierSelect = $('#{{ form.supplier.id_for_label }}');

    // Add new supplier to dropdown
    if (newSupplier) {
        supplierSelect.append(new Option(newSupplier.name, newSupplier.id, true, true));
        supplierSelect.trigger('change');
    }
}

// Function to initialize purchase invoice form
function initializePurchaseInvoiceForm() {
    // Product change event
    $(document).on('change', '.product-select', function() {
        var row = $(this).closest('tr');
        var productId = $(this).val();

        if (productId) {
            // Get product price
            $.ajax({
                url: '{% url "sales:get_product_price" %}',
                data: { 'product_id': productId },
                success: function(data) {
                    row.find('.price-input').val(data.price);
                    calculateRowTotal(row);
                    calculatePurchaseInvoiceTotal();
                }
            });
        }
    });

    // Quantity and price change events
    $(document).on('input', '.quantity-input, .price-input', function() {
        var row = $(this).closest('tr');
        calculateRowTotal(row);
        calculatePurchaseInvoiceTotal();
    });

    // Discount and tax change events
    $('#{{ form.discount_percentage.id_for_label }}, #{{ form.tax_percentage.id_for_label }}').on('input', function() {
        calculatePurchaseInvoiceTotal();
    });

    // Remove item button
    $(document).on('click', '.remove-item', function() {
        var row = $(this).closest('tr');
        row.find('input[name$="-DELETE"]').prop('checked', true);
        row.hide();
        calculatePurchaseInvoiceTotal();
    });
}

// Function to calculate row total
function calculateRowTotal(row) {
    var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
    var price = parseFloat(row.find('.price-input').val()) || 0;
    var total = quantity * price;

    row.find('.total-cell').text(total.toFixed(2));
}

// Function to calculate purchase invoice total
function calculatePurchaseInvoiceTotal() {
    var subtotal = 0;

    // Calculate subtotal
    $('#purchase-invoice-items tbody tr:visible').each(function() {
        var row = $(this);
        if (!row.find('input[name$="-DELETE"]').is(':checked')) {
            var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
            var price = parseFloat(row.find('.price-input').val()) || 0;
            subtotal += quantity * price;
        }
    });

    // Calculate discount and tax
    var discountPercentage = parseFloat($('#{{ form.discount_percentage.id_for_label }}').val()) || 0;
    var taxPercentage = parseFloat($('#{{ form.tax_percentage.id_for_label }}').val()) || 0;

    var discountAmount = (subtotal * discountPercentage) / 100;
    var taxAmount = ((subtotal - discountAmount) * taxPercentage) / 100;
    var totalAmount = subtotal - discountAmount + taxAmount;

    // Update display
    $('#subtotal').text(subtotal.toFixed(2));
    $('#discount-amount').text(discountAmount.toFixed(2));
    $('#tax-amount').text(taxAmount.toFixed(2));
    $('#total-amount').text(totalAmount.toFixed(2));
}
</script>
{% endblock %}
