#!/usr/bin/env python
"""
System Check Script for Sales Management System
نص فحص النظام لنظام إدارة المبيعات
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python():
    """Check Python version"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Need Python 3.8+")
        return False

def check_files():
    """Check if all required files exist"""
    print("\n📁 Checking required files...")
    
    required_files = [
        'manage.py',
        'requirements.txt',
        'sales_system/settings.py',
        'sales/models.py',
        'sales/views.py',
        'sales/urls.py',
        'templates/base.html',
        'static/css/style.css',
        'static/js/main.js',
    ]
    
    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def check_env_file():
    """Check .env file"""
    print("\n⚙️ Checking .env file...")
    
    if not Path('.env').exists():
        print("❌ .env file not found")
        print("📝 Please create .env file with:")
        print("""
SECRET_KEY=your-secret-key-here
DEBUG=True
DB_NAME=sales_system
DB_USER=root
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
        """)
        return False
    
    print("✅ .env file exists")
    
    # Check required variables
    required_vars = ['SECRET_KEY', 'DB_NAME', 'DB_USER', 'DB_HOST', 'DB_PORT']
    missing_vars = []
    
    try:
        with open('.env', 'r') as f:
            content = f.read()
            for var in required_vars:
                if f"{var}=" not in content:
                    missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️ Missing variables in .env: {', '.join(missing_vars)}")
            return False
        else:
            print("✅ All required variables found in .env")
            return True
            
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def check_django():
    """Check Django installation and configuration"""
    print("\n🎯 Checking Django...")
    
    try:
        # Check if Django is installed
        import django
        print(f"✅ Django {django.get_version()} installed")
        
        # Set Django settings
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
        django.setup()
        
        # Check Django configuration
        from django.core.management import execute_from_command_line
        from django.conf import settings
        
        print("✅ Django settings loaded")
        
        # Check database connection
        from django.db import connection
        cursor = connection.cursor()
        print("✅ Database connection successful")
        
        return True
        
    except ImportError:
        print("❌ Django not installed")
        return False
    except Exception as e:
        print(f"❌ Django configuration error: {e}")
        return False

def check_database():
    """Check database connection"""
    print("\n🗄️ Checking database...")
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
        import django
        django.setup()
        
        from django.db import connection
        from django.core.management import execute_from_command_line
        
        # Test connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        
        # Check if migrations are applied
        from django.db.migrations.executor import MigrationExecutor
        executor = MigrationExecutor(connection)
        plan = executor.migration_plan(executor.loader.graph.leaf_nodes())
        
        if plan:
            print("⚠️ Pending migrations found")
            print("💡 Run: python manage.py migrate")
            return False
        else:
            print("✅ All migrations applied")
            return True
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        print("💡 Check your database settings in .env file")
        return False

def check_static_files():
    """Check static files"""
    print("\n📦 Checking static files...")
    
    static_dirs = ['static/css', 'static/js', 'static/images']
    
    for dir_path in static_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} - Missing")
            return False
    
    # Check if logo exists
    if Path('static/images/logo.jpg').exists() or Path('logo.jpg').exists():
        print("✅ Logo file found")
    else:
        print("⚠️ Logo file not found")
    
    return True

def run_system_check():
    """Run Django system check"""
    print("\n🔍 Running Django system check...")
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'check'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Django system check passed")
            return True
        else:
            print("❌ Django system check failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running system check: {e}")
        return False

def main():
    """Main check function"""
    print("=" * 60)
    print("🚀 Sales Management System - System Check")
    print("🚀 نظام إدارة المبيعات - فحص النظام")
    print("=" * 60)
    
    checks = [
        ("Python Version", check_python),
        ("Required Files", check_files),
        ("Environment File", check_env_file),
        ("Static Files", check_static_files),
        ("Django Configuration", check_django),
        ("Database Connection", check_database),
        ("System Check", run_system_check),
    ]
    
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            if check_func():
                passed += 1
        except Exception as e:
            print(f"❌ Unexpected error in {name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 SUMMARY: {passed}/{total} checks passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All checks passed! Your system is ready to run.")
        print("🎉 جميع الفحوصات نجحت! النظام جاهز للتشغيل.")
        print("\n💡 Next steps:")
        print("1. Run: python manage.py createsuperuser")
        print("2. Run: python manage.py runserver")
        print("3. Open: http://127.0.0.1:8000")
    else:
        print("⚠️ Some checks failed. Please fix the issues above.")
        print("⚠️ بعض الفحوصات فشلت. يرجى إصلاح المشاكل أعلاه.")
        
        if passed >= total * 0.7:  # If 70% or more checks passed
            print("\n💡 You can try running the system anyway:")
            print("python manage.py runserver")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
