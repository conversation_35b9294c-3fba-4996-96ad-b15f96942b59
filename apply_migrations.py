#!/usr/bin/env python
"""Apply migrations and start server"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
django.setup()

from django.core.management import execute_from_command_line

def apply_migrations():
    """Apply pending migrations"""
    try:
        print("🔧 تطبيق migrations...")
        
        # Apply migrations
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✅ تم تطبيق migrations بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق migrations: {e}")
        return False

if __name__ == '__main__':
    success = apply_migrations()
    if success:
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("python manage.py runserver")
    else:
        print("\n💡 جرب تشغيل النظام مباشرة:")
        print("python manage.py runserver --skip-checks")
    
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
