{% extends "admin/base.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        نظام إدارة المبيعات
    </a>
</h1>
{% endblock %}

{% block nav-global %}
<div class="language-switcher">
    <span id="current-language">{{ LANGUAGE_CODE|default:'ar' }}</span>
    <div class="language-options">
        <button onclick="switchAdminLanguage('ar')" class="lang-btn {% if LANGUAGE_CODE == 'ar' %}active{% endif %}">العربية</button>
        <button onclick="switchAdminLanguage('fr')" class="lang-btn {% if LANGUAGE_CODE == 'fr' %}active{% endif %}">Français</button>
    </div>
</div>
{% endblock %}

{% block extrahead %}
<style>
    /* Basic Django Admin Styling - Original Look */
    
    /* Language switcher styling */
    .language-switcher {
        position: fixed;
        top: 10px;
        z-index: 1000;
        background: rgba(44, 62, 80, 0.9);
        padding: 8px 15px;
        border-radius: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        backdrop-filter: blur(10px);
    }
    
    [dir="rtl"] .language-switcher {
        left: 10px;
        right: auto;
    }
    
    [dir="ltr"] .language-switcher {
        right: 10px;
        left: auto;
    }
    
    .language-switcher #current-language {
        color: #ecf0f1;
        font-size: 12px;
        margin-right: 10px;
        font-weight: 600;
    }
    
    [dir="rtl"] .language-switcher #current-language {
        margin-right: 0;
        margin-left: 10px;
    }
    
    .language-options {
        display: inline-block;
    }
    
    .lang-btn {
        background: transparent;
        border: 1px solid #bdc3c7;
        color: #ecf0f1;
        padding: 4px 10px;
        margin: 0 2px;
        border-radius: 12px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .lang-btn:hover {
        background: #3498db;
        border-color: #3498db;
        transform: translateY(-1px);
    }
    
    .lang-btn.active {
        background: #2ecc71;
        border-color: #2ecc71;
        color: white;
        font-weight: 600;
    }
    
    /* RTL/LTR Support */
    [dir="rtl"] {
        text-align: right;
        direction: rtl;
    }
    
    [dir="ltr"] {
        text-align: left;
        direction: ltr;
    }
    
    /* Basic sidebar styling - Original Django look */
    #nav-sidebar {
        background: #417690;
        width: 280px;
    }
    
    /* Basic content styling - Original Django look */
    .main {
        margin-left: 280px;
    }
    
    [dir="rtl"] .main {
        margin-left: 0;
        margin-right: 280px;
    }
    
    /* Site name styling */
    #site-name a {
        color: #f4f4f4;
        text-decoration: none;
    }
    
    /* Basic header styling */
    #header {
        background: #417690;
        color: #ffc;
    }
    
    /* Basic breadcrumbs */
    .breadcrumbs {
        background: #79aec8;
        color: #fff;
    }
    
    /* Basic module styling */
    .module h2 {
        background: #7ca0aa;
        color: #fff;
    }
    
    /* Basic table styling */
    .results table {
        border-collapse: collapse;
        width: 100%;
    }
    
    .results table th {
        background: #f6f6f6;
        border-bottom: 1px solid #ddd;
    }
    
    .results table td {
        border-bottom: 1px solid #eee;
    }
</style>

<script>
    // Function to switch admin language
    function switchAdminLanguage(language) {
        // Set language in session via AJAX
        fetch('/set-language/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
            },
            body: 'language=' + language
        }).then(() => {
            // Reload the page to apply language changes
            window.location.reload();
        }).catch(error => {
            console.error('Error switching language:', error);
            // Fallback: redirect with language parameter
            window.location.href = window.location.pathname + '?language=' + language;
        });
    }

    // Apply basic direction on page load
    document.addEventListener('DOMContentLoaded', function() {
        const currentLang = '{{ LANGUAGE_CODE }}';
        const direction = currentLang === 'ar' ? 'rtl' : 'ltr';
        
        // Set document direction
        document.documentElement.setAttribute('dir', direction);
        document.documentElement.setAttribute('lang', currentLang);
        
        // Update language switcher
        const currentLanguageSpan = document.getElementById('current-language');
        if (currentLanguageSpan) {
            currentLanguageSpan.textContent = currentLang === 'ar' ? 'العربية' : 'Français';
        }
    });
</script>
{% endblock %}
