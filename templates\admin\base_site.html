{% extends "admin/base.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        نظام إدارة المبيعات
    </a>
</h1>
{% endblock %}

{% block nav-global %}
<div class="language-switcher">
    <span id="current-language">{{ LANGUAGE_CODE|default:'ar' }}</span>
    <div class="language-options">
        <button onclick="switchAdminLanguage('ar')" class="lang-btn {% if LANGUAGE_CODE == 'ar' %}active{% endif %}">العربية</button>
        <button onclick="switchAdminLanguage('fr')" class="lang-btn {% if LANGUAGE_CODE == 'fr' %}active{% endif %}">Français</button>
    </div>
</div>
{% endblock %}

{% block extrahead %}
    <!-- Dark Theme CSS for Admin -->
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/admin-dark-theme.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/global-dark-tables.css' %}">

<style>
    /* DARK THEME FOR DJANGO ADMIN */
    /* تطبيق الخلفية السوداء على واجهة الإدارة */

    /* Main body dark theme */
    body {
        background-color: #1a1a1a !important;
        color: #ffffff !important;
    }

    /* Header dark theme */
    #header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
        color: #ffffff !important;
        border-bottom: 3px solid #4a6741 !important;
    }

    #branding h1 {
        color: #ffffff !important;
        font-weight: 700 !important;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3) !important;
    }

    #branding h1 a:link,
    #branding h1 a:visited {
        color: #ffffff !important;
    }

    #user-tools {
        color: #ffffff !important;
    }

    #user-tools a {
        color: #74c0fc !important;
        font-weight: 600 !important;
    }

    #user-tools a:hover {
        color: #339af0 !important;
    }

    /* Breadcrumbs dark theme */
    .breadcrumbs {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
        color: #ffffff !important;
        border-bottom: 1px solid #4a6741 !important;
    }

    .breadcrumbs a {
        color: #74c0fc !important;
        font-weight: 600 !important;
    }

    .breadcrumbs a:hover {
        color: #339af0 !important;
    }

    /* Content area dark theme */
    #content {
        background-color: #1a1a1a !important;
        color: #ffffff !important;
    }

    /* Sidebar dark theme */
    #nav-sidebar {
        background-color: #2c3e50 !important;
        border-right: 2px solid #4a6741 !important;
        width: 280px;
    }

    #nav-sidebar .module {
        background-color: #34495e !important;
        border: 1px solid #4a6741 !important;
        border-radius: 8px !important;
        margin-bottom: 15px !important;
    }

    #nav-sidebar .module h2 {
        background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
        color: #ffffff !important;
        font-weight: 700 !important;
        padding: 12px 15px !important;
        border-radius: 8px 8px 0 0 !important;
    }

    #nav-sidebar .module ul {
        background-color: #34495e !important;
        padding: 10px 0 !important;
    }

    #nav-sidebar .module li {
        border-bottom: 1px solid #4a6741 !important;
    }

    #nav-sidebar .module li:last-child {
        border-bottom: none !important;
    }

    #nav-sidebar .module a {
        color: #74c0fc !important;
        padding: 10px 15px !important;
        display: block !important;
        text-decoration: none !important;
        font-weight: 500 !important;
    }

    #nav-sidebar .module a:hover {
        background-color: #4a90e2 !important;
        color: #ffffff !important;
        transform: translateX(5px) !important;
        transition: all 0.3s ease !important;
    }

    /* Modules dark theme */
    .module {
        background-color: #2c3e50 !important;
        border: 1px solid #4a6741 !important;
        border-radius: 8px !important;
        color: #ffffff !important;
    }

    .module h2,
    .module caption {
        background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
        color: #ffffff !important;
        font-weight: 700 !important;
        border-radius: 8px 8px 0 0 !important;
    }

    /* Dashboard modules */
    .dashboard .module {
        background-color: #34495e !important;
        border: 2px solid #4a6741 !important;
        border-radius: 12px !important;
        transition: all 0.3s ease !important;
    }

    .dashboard .module:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3) !important;
        border-color: #74c0fc !important;
    }

    .dashboard .module h2 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: #ffffff !important;
        text-align: center !important;
        padding: 15px !important;
        font-size: 16px !important;
        text-transform: uppercase !important;
        letter-spacing: 1px !important;
    }
    
    /* Language switcher styling */
    .language-switcher {
        position: fixed;
        top: 60px;
        z-index: 1000;
        background: rgba(44, 62, 80, 0.95);
        padding: 15px 30px;
        border-radius: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        backdrop-filter: blur(15px);
        border: 2px solid rgba(116, 192, 252, 0.3);
        min-width: 350px;
        width: 350px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    [dir="rtl"] .language-switcher {
        left: 20px;
        right: auto;
    }

    [dir="ltr"] .language-switcher {
        right: 20px;
        left: auto;
    }

    /* Responsive positioning */
    @media (max-width: 768px) {
        .language-switcher {
            top: 70px;
            left: 10px !important;
            right: 10px !important;
            min-width: auto;
            width: calc(100% - 20px);
            padding: 12px 20px;
        }

        .lang-btn {
            min-width: 60px;
            padding: 6px 12px;
            font-size: 12px;
        }

        .language-switcher #current-language {
            min-width: 60px;
            padding: 4px 10px;
            font-size: 12px;
        }
    }

    .language-switcher:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        border-color: rgba(116, 192, 252, 0.6);
    }
    
    .language-switcher #current-language {
        color: #ecf0f1;
        font-size: 14px;
        margin-right: 20px;
        font-weight: 700;
        background: rgba(52, 73, 94, 0.8);
        padding: 6px 15px;
        border-radius: 12px;
        border: 1px solid #74c0fc;
        min-width: 80px;
        text-align: center;
    }

    [dir="rtl"] .language-switcher #current-language {
        margin-right: 0;
        margin-left: 20px;
    }
    
    .language-options {
        display: flex;
        gap: 15px;
        align-items: center;
    }
    
    .lang-btn {
        background: transparent;
        border: 1px solid #bdc3c7;
        color: #ecf0f1;
        padding: 8px 18px;
        margin: 0 10px;
        border-radius: 18px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.3s ease;
        font-weight: 600;
        min-width: 90px;
        text-align: center;
    }
    
    .lang-btn:hover {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        border-color: #3498db;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
        color: #ffffff;
    }

    .lang-btn.active {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        border-color: #2ecc71;
        color: #ffffff;
        font-weight: 700;
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(46, 204, 113, 0.5);
    }
    
    /* RTL/LTR Support */
    [dir="rtl"] {
        text-align: right;
        direction: rtl;
    }
    
    [dir="ltr"] {
        text-align: left;
        direction: ltr;
    }
    
    /* Basic sidebar styling - Original Django look */
    #nav-sidebar {
        background: #417690;
        width: 280px;
    }
    
    /* Basic content styling - Original Django look */
    .main {
        margin-left: 280px;
    }
    
    [dir="rtl"] .main {
        margin-left: 0;
        margin-right: 280px;
    }
    
    /* Site name styling */
    #site-name a {
        color: #f4f4f4;
        text-decoration: none;
    }
    
    /* Basic header styling */
    #header {
        background: #417690;
        color: #ffc;
    }
    
    /* Basic breadcrumbs */
    .breadcrumbs {
        background: #79aec8;
        color: #fff;
    }
    
    /* Basic module styling */
    .module h2 {
        background: #7ca0aa;
        color: #fff;
    }
    
    /* DARK THEME TABLES */
    /* جداول بخلفية سوداء */

    .results table,
    #changelist table,
    #changelist-table,
    .changelist-results,
    .module table {
        background-color: #2c3e50 !important;
        border: 2px solid #4a6741 !important;
        border-radius: 8px !important;
        overflow: hidden !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
        border-collapse: separate !important;
        width: 100%;
    }

    .results table th,
    #changelist table th,
    #changelist-table th,
    .changelist-results th,
    .module table th {
        background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
        color: #ffffff !important;
        font-weight: 700 !important;
        text-transform: uppercase !important;
        font-size: 12px !important;
        letter-spacing: 1px !important;
        padding: 15px 12px !important;
        border: none !important;
        border-bottom: 2px solid #4a6741 !important;
    }

    .results table td,
    #changelist table td,
    #changelist-table td,
    .changelist-results td,
    .module table td {
        background-color: #2c3e50 !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        padding: 12px 10px !important;
        border-bottom: 1px solid #4a6741 !important;
        border-left: none !important;
        border-right: none !important;
        vertical-align: middle !important;
    }

    /* Alternating rows */
    .results table tbody tr:nth-child(even) td,
    #changelist table tbody tr:nth-child(even) td,
    #changelist-table tbody tr:nth-child(even) td,
    .changelist-results tbody tr:nth-child(even) td,
    .module table tbody tr:nth-child(even) td {
        background-color: #34495e !important;
    }

    /* Hover effects */
    .results table tbody tr:hover td,
    #changelist table tbody tr:hover td,
    #changelist-table tbody tr:hover td,
    .changelist-results tbody tr:hover td,
    .module table tbody tr:hover td {
        background-color: #4a90e2 !important;
        color: #ffffff !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3) !important;
        transition: all 0.3s ease !important;
    }

    /* First column (IDs) - Red */
    .results table td:first-child,
    #changelist table td:first-child,
    #changelist-table td:first-child,
    .changelist-results td:first-child {
        color: #ff6b6b !important;
        font-weight: 700 !important;
        font-size: 16px !important;
        font-family: 'Courier New', monospace !important;
    }

    /* Links in tables - Blue */
    .results table a,
    #changelist table a,
    #changelist-table a,
    .changelist-results a,
    .module table a {
        color: #74c0fc !important;
        text-decoration: none !important;
        font-weight: 600 !important;
    }

    .results table a:hover,
    #changelist table a:hover,
    #changelist-table a:hover,
    .changelist-results a:hover,
    .module table a:hover {
        color: #339af0 !important;
        text-decoration: underline !important;
    }

    /* Strong text - Yellow */
    .results table strong,
    #changelist table strong,
    #changelist-table strong,
    .changelist-results strong,
    .module table strong {
        color: #ffd43b !important;
        font-weight: 700 !important;
    }

    /* Action buttons */
    .addlink {
        background: linear-gradient(135deg, #51cf66 0%, #40c057 100%) !important;
        color: #ffffff !important;
        padding: 8px 16px !important;
        border-radius: 20px !important;
        text-decoration: none !important;
        font-weight: 600 !important;
        display: inline-block !important;
        margin: 10px 0 !important;
        transition: all 0.3s ease !important;
    }

    .addlink:hover {
        background: linear-gradient(135deg, #40c057 0%, #37b24d 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(81, 207, 102, 0.3) !important;
    }

    .changelink {
        color: #74c0fc !important;
        font-weight: 600 !important;
    }

    .changelink:hover {
        color: #339af0 !important;
    }

    /* Form inputs dark theme */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    textarea,
    select {
        background-color: #2c3e50 !important;
        color: #ffffff !important;
        border: 1px solid #4a6741 !important;
        border-radius: 4px !important;
        padding: 8px 12px !important;
    }

    input[type="text"]:focus,
    input[type="email"]:focus,
    input[type="password"]:focus,
    input[type="number"]:focus,
    textarea:focus,
    select:focus {
        background-color: #34495e !important;
        border-color: #74c0fc !important;
        box-shadow: 0 0 0 2px rgba(116, 192, 252, 0.3) !important;
    }

    /* Labels */
    label {
        color: #ffffff !important;
        font-weight: 600 !important;
    }

    /* Buttons */
    .button,
    input[type="submit"],
    input[type="button"],
    .submit-row input {
        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
        color: #ffffff !important;
        border: none !important;
        border-radius: 6px !important;
        padding: 10px 20px !important;
        font-weight: 600 !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .button:hover,
    input[type="submit"]:hover,
    input[type="button"]:hover,
    .submit-row input:hover {
        background: linear-gradient(135deg, #357abd 0%, #2968a3 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3) !important;
    }
</style>

<script>
    // Function to switch admin language
    function switchAdminLanguage(language) {
        // Set language in session via AJAX
        fetch('/set-language/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
            },
            body: 'language=' + language
        }).then(() => {
            // Reload the page to apply language changes
            window.location.reload();
        }).catch(error => {
            console.error('Error switching language:', error);
            // Fallback: redirect with language parameter
            window.location.href = window.location.pathname + '?language=' + language;
        });
    }

    // Apply basic direction on page load
    document.addEventListener('DOMContentLoaded', function() {
        const currentLang = '{{ LANGUAGE_CODE }}';
        const direction = currentLang === 'ar' ? 'rtl' : 'ltr';
        
        // Set document direction
        document.documentElement.setAttribute('dir', direction);
        document.documentElement.setAttribute('lang', currentLang);
        
        // Update language switcher
        const currentLanguageSpan = document.getElementById('current-language');
        if (currentLanguageSpan) {
            currentLanguageSpan.textContent = currentLang === 'ar' ? 'العربية' : 'Français';
        }

        // Apply dark theme enhancements
        enhanceAdminDarkTheme();
    });

    // Function to enhance admin dark theme
    function enhanceAdminDarkTheme() {
        // Enhance tables
        const tables = document.querySelectorAll('#changelist table, .results table, .module table');
        tables.forEach(function(table) {
            enhanceTableCells(table);
            addTableHoverEffects(table);
        });

        // Add loading animations
        addLoadingAnimations();
    }

    function enhanceTableCells(table) {
        const cells = table.querySelectorAll('td');

        cells.forEach(function(cell, index) {
            const content = cell.textContent.trim();
            const row = cell.parentElement;
            const cellIndex = Array.from(row.cells).indexOf(cell);

            // Apply base styling
            cell.style.backgroundColor = '#2c3e50';
            cell.style.color = '#ffffff';
            cell.style.borderColor = '#4a6741';
            cell.style.padding = '12px 10px';
            cell.style.fontWeight = '500';

            // Alternating rows
            const rowIndex = Array.from(row.parentElement.children).indexOf(row);
            if (rowIndex % 2 === 1) {
                cell.style.backgroundColor = '#34495e';
            }

            // Color coding
            if (cellIndex === 0) {
                // First column - Red
                cell.style.color = '#ff6b6b';
                cell.style.fontWeight = '700';
                cell.style.fontSize = '16px';
                cell.style.fontFamily = 'Courier New, monospace';
            } else if (content.includes('أوقية') || content.includes('MRU') || /^\d+\.?\d*$/.test(content)) {
                // Prices - Green
                cell.style.color = '#51cf66';
                cell.style.fontWeight = '700';
                cell.style.fontSize = '16px';
            } else if (cell.querySelector('a')) {
                // Links - Blue
                const link = cell.querySelector('a');
                link.style.color = '#74c0fc';
                link.style.textDecoration = 'none';
                link.style.fontWeight = '600';
            } else {
                // Regular text - Purple
                cell.style.color = '#da77f2';
                cell.style.fontWeight = '600';
            }
        });
    }

    function addTableHoverEffects(table) {
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach(function(row) {
            row.addEventListener('mouseenter', function() {
                const cells = row.querySelectorAll('td');
                cells.forEach(function(cell) {
                    cell.style.backgroundColor = '#4a90e2';
                    cell.style.color = '#ffffff';
                    cell.style.transform = 'translateY(-1px)';
                    cell.style.boxShadow = '0 4px 12px rgba(74, 144, 226, 0.3)';
                    cell.style.transition = 'all 0.3s ease';
                });
            });

            row.addEventListener('mouseleave', function() {
                const cells = row.querySelectorAll('td');
                const rowIndex = Array.from(row.parentElement.children).indexOf(row);

                cells.forEach(function(cell, cellIndex) {
                    // Restore background
                    if (rowIndex % 2 === 1) {
                        cell.style.backgroundColor = '#34495e';
                    } else {
                        cell.style.backgroundColor = '#2c3e50';
                    }

                    // Restore text colors
                    const content = cell.textContent.trim();
                    if (cellIndex === 0) {
                        cell.style.color = '#ff6b6b';
                    } else if (content.includes('أوقية') || content.includes('MRU')) {
                        cell.style.color = '#51cf66';
                    } else if (cell.querySelector('a')) {
                        cell.querySelector('a').style.color = '#74c0fc';
                    } else {
                        cell.style.color = '#da77f2';
                    }

                    cell.style.transform = 'translateY(0)';
                    cell.style.boxShadow = 'none';
                });
            });
        });
    }

    function addLoadingAnimations() {
        // Add fade-in animation to modules
        const modules = document.querySelectorAll('.dashboard .module');
        modules.forEach(function(module, index) {
            module.style.opacity = '0';
            module.style.transform = 'translateY(20px)';
            module.style.transition = 'all 0.6s ease';

            setTimeout(() => {
                module.style.opacity = '1';
                module.style.transform = 'translateY(0)';
            }, 100 + (index * 100));
        });

        // Add button click animations
        const buttons = document.querySelectorAll('.button, input[type="submit"], .addlink');
        buttons.forEach(function(button) {
            button.addEventListener('click', function() {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = 'scale(1)';
                }, 150);
            });
        });
    }
</script>
{% endblock %}

{% block extrajs %}
    {{ block.super }}
    <!-- Dark Theme JavaScript for Admin -->
    <script src="{% static 'admin/js/admin-enhancements.js' %}"></script>
    <script src="{% static 'js/table-enhancements.js' %}"></script>
{% endblock %}
