# 🚀 دليل التشغيل السريع - نظام إدارة المبيعات

## ⚡ التشغيل السريع (5 دقائق)

### 1️⃣ المتطلبات الأساسية
- ✅ Python 3.8+ مثبت
- ✅ MySQL Server يعمل
- ✅ متصفح ويب

### 2️⃣ إنشاء قاعدة البيانات
افتح MySQL وأنشئ قاعدة بيانات جديدة:
```sql
CREATE DATABASE sales_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3️⃣ إعداد ملف البيئة
أنشئ ملف `.env` في المجلد الرئيسي:
```env
SECRET_KEY=django-insecure-change-this-in-production
DEBUG=True
DB_NAME=sales_system
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_HOST=localhost
DB_PORT=3306
```

### 4️⃣ تشغيل النظام

#### Windows:
```bash
# انقر نقراً مزدوجاً على الملف أو اكتب في Command Prompt:
run.bat
```

#### Linux/Mac:
```bash
# في Terminal:
chmod +x run.sh
./run.sh
```

### 5️⃣ إنشاء مستخدم إداري
عندما يطلب منك النظام، أنشئ مستخدماً إدارياً:
```bash
python manage.py createsuperuser
```

### 6️⃣ تحميل بيانات تجريبية (اختياري)
عندما يسأل النظام، اختر `y` لتحميل بيانات تجريبية.

### 7️⃣ فتح النظام
افتح المتصفح واذهب إلى: **http://127.0.0.1:8000**

---

## 🎯 الخطوات الأولى بعد التشغيل

### 1. تسجيل الدخول
- استخدم بيانات المستخدم الإداري
- ستظهر لك لوحة التحكم

### 2. إضافة فئات المنتجات
- اذهب إلى "المنتجات" > "الفئات"
- أضف فئات مثل: إلكترونيات، ملابس، مواد غذائية

### 3. إضافة منتجات
- اذهب إلى "المنتجات" > "إضافة منتج"
- أضف بعض المنتجات مع الأسعار والكميات

### 4. إضافة عملاء
- اذهب إلى "العملاء" > "إضافة عميل"
- أضف بيانات العملاء

### 5. إنشاء فاتورة
- اذهب إلى "الفواتير" > "إنشاء فاتورة"
- اختر عميل وأضف منتجات
- احفظ واطبع الفاتورة

---

## 🔧 حل المشاكل الشائعة

### ❌ خطأ في قاعدة البيانات
```
django.db.utils.OperationalError: (2003, "Can't connect to MySQL server")
```
**الحل:**
- تأكد من تشغيل MySQL Server
- تحقق من بيانات الاتصال في ملف `.env`

### ❌ خطأ في Python
```
'python' is not recognized as an internal or external command
```
**الحل:**
- تأكد من تثبيت Python
- أضف Python إلى PATH

### ❌ خطأ في المتطلبات
```
No module named 'django'
```
**الحل:**
- تأكد من تفعيل البيئة الافتراضية
- شغل: `pip install -r requirements.txt`

### ❌ خطأ في الملفات الثابتة
```
You're using the staticfiles app without having set the STATIC_ROOT setting
```
**الحل:**
- شغل: `python manage.py collectstatic`

---

## 📱 الميزات الرئيسية

### ✨ إدارة العملاء
- إضافة وتعديل وحذف العملاء
- البحث في قاعدة البيانات
- عرض تاريخ المشتريات

### ✨ إدارة المنتجات
- تنظيم المنتجات في فئات
- تتبع المخزون التلقائي
- رفع صور المنتجات
- تنبيهات المخزون المنخفض

### ✨ إدارة الفواتير
- إنشاء فواتير احترافية
- حساب الضرائب والخصومات
- طباعة وتصدير الفواتير
- تتبع حالة الدفع

### ✨ التقارير
- إحصائيات المبيعات
- أفضل المنتجات والعملاء
- تقارير المخزون
- رسوم بيانية تفاعلية

### ✨ دعم متعدد اللغات
- العربية (افتراضي)
- الفرنسية
- واجهة RTL للعربية

### ✨ العملة الموريتانية
- دعم الأوقية الموريتانية (MRU)
- تنسيق العملة المحلي

---

## 🌟 نصائح للاستخدام الأمثل

### 💡 تنظيم البيانات
- أنشئ فئات واضحة للمنتجات
- استخدم أسماء وصفية للمنتجات
- احتفظ ببيانات العملاء محدثة

### 💡 إدارة المخزون
- راقب تنبيهات المخزون المنخفض
- حدث الكميات بانتظام
- استخدم التقارير لتتبع المبيعات

### 💡 الفواتير
- تحقق من البيانات قبل الحفظ
- استخدم الخصومات والضرائب حسب الحاجة
- احتفظ بنسخ من الفواتير المهمة

### 💡 الأمان
- غير كلمة مرور المستخدم الإداري
- احتفظ بنسخ احتياطية من قاعدة البيانات
- لا تشارك بيانات الدخول

---

## 📞 الدعم والمساعدة

### 📚 الوثائق
- اقرأ ملف `README.md` للتفاصيل الكاملة
- راجع ملف `start_project.md` للإعداد اليدوي

### 🐛 الإبلاغ عن المشاكل
- أنشئ issue في المشروع
- اذكر تفاصيل المشكلة ونظام التشغيل

### 💬 المجتمع
- شارك تجربتك مع النظام
- اقترح ميزات جديدة
- ساعد المستخدمين الآخرين

---

## 🎉 مبروك!

أنت الآن جاهز لاستخدام نظام إدارة المبيعات. استمتع بإدارة مبيعاتك بكفاءة!

**تذكر:** هذا النظام مصمم خصيصاً للسوق الموريتاني مع دعم العربية والفرنسية والأوقية الموريتانية.
