{% load static %}
{% load translation_extras %}

<!DOCTYPE html>
<html lang="{{ lang.code }}" dir="{{ lang.direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مبدل اللغة - Language Switcher Test</title>
    
    <!-- Bootstrap CSS -->
    {% if lang.is_rtl %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            margin: 20px auto;
            max-width: 800px;
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
        }
        
        .sample-text {
            font-size: 18px;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .current-lang-info {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Enhanced Language Switcher -->
    {% include 'sales/enhanced_language_switcher.html' %}
    
    <div class="test-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="display-4">
                <i class="fas fa-language"></i>
                اختبار مبدل اللغة المحسن
            </h1>
            <p class="lead">Test du Commutateur de Langue Amélioré</p>
        </div>
        
        <!-- Current Language Info -->
        <div class="current-lang-info">
            <h3>
                <i class="fas fa-globe"></i>
                اللغة الحالية / Langue Actuelle
            </h3>
            <p class="mb-0">
                <strong>{{ request.LANGUAGE_CODE|upper }}</strong> - 
                {% if request.LANGUAGE_CODE == 'ar' %}
                    العربية (Arabic)
                {% else %}
                    الفرنسية (Français)
                {% endif %}
            </p>
            <small>الاتجاه / Direction: {{ lang.direction|upper }}</small>
        </div>
        
        <!-- Sample Content for Translation Testing -->
        <div class="test-section">
            <h3>
                <i class="fas fa-file-text"></i>
                محتوى تجريبي للترجمة / Contenu d'Exemple pour Traduction
            </h3>
            
            <div class="sample-text">
                <h4>{% trans_fallback "نظام إدارة المبيعات" %}</h4>
                <p>{% trans_fallback "مرحباً بكم في نظام إدارة المبيعات المتطور الذي يدعم اللغتين العربية والفرنسية" %}</p>
            </div>
            
            <div class="sample-text">
                <h5>{% trans_fallback "الميزات الرئيسية" %}</h5>
                <ul>
                    <li>{% trans_fallback "إدارة العملاء" %}</li>
                    <li>{% trans_fallback "إدارة المنتجات" %}</li>
                    <li>{% trans_fallback "إنشاء الفواتير" %}</li>
                    <li>{% trans_fallback "التقارير والإحصائيات" %}</li>
                </ul>
            </div>
        </div>
        
        <!-- Navigation Menu Test -->
        <div class="test-section">
            <h3>
                <i class="fas fa-bars"></i>
                قائمة التنقل / Menu de Navigation
            </h3>
            
            <nav class="navbar navbar-expand-lg navbar-light bg-light rounded">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#">
                        <i class="fas fa-store"></i>
                        {% trans_fallback "نظام المبيعات" %}
                    </a>
                    
                    <div class="navbar-nav">
                        <a class="nav-link" href="#">
                            <i class="fas fa-home"></i>
                            {% trans_fallback "الرئيسية" %}
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-users"></i>
                            {% trans_fallback "العملاء" %}
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-box"></i>
                            {% trans_fallback "المنتجات" %}
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-file-invoice"></i>
                            {% trans_fallback "الفواتير" %}
                        </a>
                    </div>
                </div>
            </nav>
        </div>
        
        <!-- Buttons Test -->
        <div class="test-section">
            <h3>
                <i class="fas fa-mouse-pointer"></i>
                الأزرار / Boutons
            </h3>
            
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-plus"></i>
                        {% trans_fallback "إضافة" %}
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success w-100 mb-2">
                        <i class="fas fa-save"></i>
                        {% trans_fallback "حفظ" %}
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100 mb-2">
                        <i class="fas fa-edit"></i>
                        {% trans_fallback "تعديل" %}
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-danger w-100 mb-2">
                        <i class="fas fa-trash"></i>
                        {% trans_fallback "حذف" %}
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Form Test -->
        <div class="test-section">
            <h3>
                <i class="fas fa-wpforms"></i>
                نموذج تجريبي / Formulaire d'Exemple
            </h3>
            
            <form>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">{% trans_fallback "الاسم" %}</label>
                        <input type="text" class="form-control" placeholder="{% trans_fallback 'أدخل الاسم' %}">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{% trans_fallback "البريد الإلكتروني" %}</label>
                        <input type="email" class="form-control" placeholder="{% trans_fallback 'أدخل البريد الإلكتروني' %}">
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">{% trans_fallback "الملاحظات" %}</label>
                    <textarea class="form-control" rows="3" placeholder="{% trans_fallback 'أدخل الملاحظات' %}"></textarea>
                </div>
                
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        {% trans_fallback "إرسال" %}
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Cards Test -->
        <div class="test-section">
            <h3>
                <i class="fas fa-th-large"></i>
                البطاقات / Cartes
            </h3>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-chart-line"></i>
                                {% trans_fallback "المبيعات" %}
                            </h5>
                            <p class="card-text">{% trans_fallback "إجمالي المبيعات هذا الشهر" %}</p>
                            <h3 class="text-primary">15,750 MRU</h3>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-users"></i>
                                {% trans_fallback "العملاء" %}
                            </h5>
                            <p class="card-text">{% trans_fallback "عدد العملاء الجدد" %}</p>
                            <h3 class="text-success">42</h3>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-file-invoice"></i>
                                {% trans_fallback "الفواتير" %}
                            </h5>
                            <p class="card-text">{% trans_fallback "الفواتير المعلقة" %}</p>
                            <h3 class="text-warning">8</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="test-section">
            <h3>
                <i class="fas fa-info-circle"></i>
                تعليمات الاختبار / Instructions de Test
            </h3>
            
            <div class="alert alert-info">
                <h6>{% trans_fallback "كيفية الاختبار" %}:</h6>
                <ol>
                    <li>{% trans_fallback "انقر على زر اللغة في الأعلى" %}</li>
                    <li>{% trans_fallback "لاحظ تغيير المحتوى" %}</li>
                    <li>{% trans_fallback "تحقق من اتجاه النص" %}</li>
                    <li>{% trans_fallback "اختبر الترجمة التلقائية" %}</li>
                </ol>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight current language info
            const currentLangInfo = document.querySelector('.current-lang-info');
            if (currentLangInfo) {
                setInterval(() => {
                    currentLangInfo.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        currentLangInfo.style.transform = 'scale(1)';
                    }, 200);
                }, 3000);
            }
            
            // Add click effects to buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
