{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "باركود المنتج" %} - {{ product.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-barcode"></i> {% trans "باركود المنتج" %}: {{ product.name }}
        </h1>
    </div>
</div>

<!-- Product Info Card -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle"></i> {% trans "معلومات المنتج" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% trans "اسم المنتج" %}:</strong></td>
                                <td>{{ product.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "الفئة" %}:</strong></td>
                                <td>{{ product.category.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "السعر" %}:</strong></td>
                                <td>{{ product.price }} {% trans "أوقية" %}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "المخزون" %}:</strong></td>
                                <td>{{ product.stock_quantity }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded" style="max-height: 200px;">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode Card -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-barcode"></i> {% trans "الباركود" %}
                </h6>
            </div>
            <div class="card-body text-center">
                {% if product.barcode %}
                    <!-- Barcode Display -->
                    <div class="barcode-container mb-3">
                        <svg id="barcode"></svg>
                    </div>
                    
                    <!-- Barcode Number -->
                    <div class="barcode-number mb-3">
                        <h5 class="font-weight-bold">{{ product.barcode_display }}</h5>
                        <small class="text-muted">{{ product.barcode }}</small>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="btn-group-vertical w-100">
                        <a href="{% url 'sales:product_barcode_print' product.pk %}" class="btn btn-primary mb-2" target="_blank">
                            <i class="fas fa-print"></i> {% trans "طباعة الباركود" %}
                        </a>
                        <button class="btn btn-success mb-2" onclick="copyBarcode()">
                            <i class="fas fa-copy"></i> {% trans "نسخ الباركود" %}
                        </button>
                        <button class="btn btn-warning mb-2" onclick="regenerateBarcode()">
                            <i class="fas fa-sync"></i> {% trans "إنشاء باركود جديد" %}
                        </button>
                    </div>
                {% else %}
                    <!-- No Barcode -->
                    <div class="text-center">
                        <i class="fas fa-barcode fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">{% trans "لا يوجد باركود" %}</h5>
                        <p class="text-muted">{% trans "لم يتم إنشاء باركود لهذا المنتج بعد" %}</p>
                        <button class="btn btn-primary" onclick="generateBarcode()">
                            <i class="fas fa-plus"></i> {% trans "إنشاء باركود" %}
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Card -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-search"></i> {% trans "البحث بالباركود" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="barcodeSearch" placeholder="{% trans 'امسح أو اكتب الباركود هنا' %}" autofocus>
                            <button class="btn btn-primary" type="button" onclick="searchByBarcode()">
                                <i class="fas fa-search"></i> {% trans "بحث" %}
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div id="searchResult" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Back Button -->
<div class="row mt-4">
    <div class="col-12">
        <a href="{% url 'sales:product_detail' product.pk %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> {% trans "العودة للمنتج" %}
        </a>
        <a href="{% url 'sales:product_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-list"></i> {% trans "قائمة المنتجات" %}
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- JsBarcode Library -->
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>

<script>
// Generate barcode display
{% if product.barcode %}
document.addEventListener('DOMContentLoaded', function() {
    JsBarcode("#barcode", "{{ product.barcode }}", {
        format: "EAN13",
        width: 2,
        height: 100,
        displayValue: false,
        background: "#ffffff",
        lineColor: "#000000"
    });
});
{% endif %}

// Copy barcode to clipboard
function copyBarcode() {
    const barcode = "{{ product.barcode }}";
    navigator.clipboard.writeText(barcode).then(function() {
        alert('{% trans "تم نسخ الباركود إلى الحافظة" %}');
    });
}

// Generate new barcode
function generateBarcode() {
    if (confirm('{% trans "هل أنت متأكد من إنشاء باركود جديد؟" %}')) {
        fetch(`{% url 'sales:generate_barcode' product.pk %}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "حدث خطأ أثناء إنشاء الباركود" %}');
        });
    }
}

// Regenerate barcode
function regenerateBarcode() {
    if (confirm('{% trans "هل أنت متأكد من إنشاء باركود جديد؟ سيتم استبدال الباركود الحالي." %}')) {
        // First, clear the current barcode
        fetch(`{% url 'sales:generate_barcode' product.pk %}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        });
    }
}

// Search by barcode
function searchByBarcode() {
    const barcode = document.getElementById('barcodeSearch').value.trim();
    const resultDiv = document.getElementById('searchResult');
    
    if (!barcode) {
        resultDiv.innerHTML = '<div class="alert alert-warning">{% trans "يرجى إدخال الباركود" %}</div>';
        return;
    }
    
    fetch(`{% url 'sales:search_barcode' %}?barcode=${encodeURIComponent(barcode)}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <strong>${data.product.name}</strong><br>
                    <small>{% trans "الفئة" %}: ${data.product.category}</small><br>
                    <small>{% trans "السعر" %}: ${data.product.price} {% trans "أوقية" %}</small><br>
                    <small>{% trans "المخزون" %}: ${data.product.stock_quantity}</small>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<div class="alert alert-danger">{% trans "حدث خطأ أثناء البحث" %}</div>';
    });
}

// Enter key search
document.getElementById('barcodeSearch').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchByBarcode();
    }
});
</script>

<style>
.barcode-container {
    background: white;
    padding: 20px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
}

.barcode-number {
    font-family: 'Courier New', monospace;
}

.btn-group-vertical .btn {
    border-radius: 0.375rem !important;
}

#barcodeSearch {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
}
</style>
{% endblock %}
