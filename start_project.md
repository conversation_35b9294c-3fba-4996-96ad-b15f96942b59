# دليل التشغيل السريع - نظام إدارة المبيعات
# Quick Start Guide - Sales Management System

## المتطلبات الأساسية / Prerequisites

### 1. Python 3.8+
تأكد من تثبيت Python 3.8 أو أحدث
Make sure Python 3.8+ is installed

### 2. MySQL Server
تأكد من تشغيل خادم MySQL
Make sure MySQL server is running

## خطوات التشغيل / Setup Steps

### 1. إنشاء قاعدة البيانات / Create Database
```sql
CREATE DATABASE sales_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. تحديث ملف الإعدادات / Update Settings
قم بتحديث ملف `.env` بمعلومات قاعدة البيانات:
Update the `.env` file with your database information:

```env
SECRET_KEY=your-secret-key-here
DEBUG=True
DB_NAME=sales_system
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password
DB_HOST=localhost
DB_PORT=3306
```

### 3. تثبيت المتطلبات / Install Requirements

#### Windows:
```bash
# إنشاء البيئة الافتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
venv\Scripts\activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

#### Linux/Mac:
```bash
# إنشاء البيئة الافتراضية
python3 -m venv venv

# تفعيل البيئة الافتراضية
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 4. تطبيق قاعدة البيانات / Apply Database Migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء مستخدم إداري / Create Superuser
```bash
python manage.py createsuperuser
```

### 6. جمع الملفات الثابتة / Collect Static Files
```bash
python manage.py collectstatic
```

### 7. تشغيل الخادم / Run Server
```bash
python manage.py runserver
```

### 8. فتح المتصفح / Open Browser
اذهب إلى: http://127.0.0.1:8000
Go to: http://127.0.0.1:8000

## التشغيل السريع (Windows) / Quick Start (Windows)
يمكنك تشغيل الملف `run.bat` للتشغيل التلقائي
You can run the `run.bat` file for automatic setup

```bash
run.bat
```

## الميزات الرئيسية / Main Features

### 1. إدارة العملاء / Customer Management
- إضافة وتعديل وحذف العملاء
- البحث في قائمة العملاء
- عرض تفاصيل العميل وفواتيره

### 2. إدارة المنتجات / Product Management
- إدارة المنتجات والفئات
- تتبع المخزون
- رفع صور المنتجات

### 3. إدارة الفواتير / Invoice Management
- إنشاء فواتير جديدة
- حساب الضرائب والخصومات تلقائياً
- طباعة الفواتير
- تتبع حالة الدفع

### 4. التقارير / Reports
- تقارير المبيعات الشهرية
- أفضل المنتجات والعملاء
- إحصائيات المخزون

### 5. دعم متعدد اللغات / Multi-language Support
- العربية (افتراضي)
- الفرنسية

### 6. العملة الموريتانية / Mauritanian Currency
- دعم كامل للأوقية الموريتانية (MRU)

## استكشاف الأخطاء / Troubleshooting

### مشكلة قاعدة البيانات / Database Issues
```bash
# تحقق من الاتصال بقاعدة البيانات
python manage.py dbshell
```

### مشكلة الملفات الثابتة / Static Files Issues
```bash
# إعادة جمع الملفات الثابتة
python manage.py collectstatic --clear
```

### مشكلة الترجمة / Translation Issues
```bash
# إعادة تجميع ملفات الترجمة
python manage.py compilemessages
```

## الدعم / Support
للحصول على المساعدة، يرجى مراجعة ملف README.md أو إنشاء issue في المشروع.
For support, please check the README.md file or create an issue in the project.

## الترخيص / License
هذا المشروع مرخص تحت رخصة MIT
This project is licensed under the MIT License
