import pymysql
import sys

def fix_database():
    try:
        print("🔧 الاتصال بقاعدة البيانات...")
        
        # الاتصال بـ MySQL
        conn = pymysql.connect(
            host='localhost',
            user='root',
            password='',  # غير كلمة المرور إذا كانت مختلفة
            database='sales_system',
            charset='utf8mb4'
        )
        
        cursor = conn.cursor()
        print("✅ تم الاتصال بنجاح!")
        
        # حذف الجداول إذا كانت موجودة
        print("🗑️ حذف الجداول القديمة...")
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        cursor.execute("DROP TABLE IF EXISTS sales_purchaseinvoiceitem")
        cursor.execute("DROP TABLE IF EXISTS sales_purchaseinvoice")
        cursor.execute("DROP TABLE IF EXISTS sales_supplier")
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # إنشاء جدول الموردين
        print("📋 إنشاء جدول الموردين...")
        cursor.execute("""
            CREATE TABLE sales_supplier (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                email VARCHAR(254) NULL,
                phone VARCHAR(20) NULL,
                address TEXT NULL,
                contact_person VARCHAR(100) NULL,
                tax_number VARCHAR(50) NULL,
                payment_terms VARCHAR(100) NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                date_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """)
        
        # إنشاء جدول فواتير الشراء
        print("📋 إنشاء جدول فواتير الشراء...")
        cursor.execute("""
            CREATE TABLE sales_purchaseinvoice (
                id INT AUTO_INCREMENT PRIMARY KEY,
                invoice_number VARCHAR(50) NOT NULL UNIQUE,
                supplier_id INT NOT NULL,
                user_id INT NOT NULL,
                date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                date_due DATE NOT NULL,
                payment_status VARCHAR(20) NOT NULL DEFAULT 'pending',
                notes TEXT NULL,
                discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
                tax_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """)
        
        # إنشاء جدول عناصر فواتير الشراء
        print("📋 إنشاء جدول عناصر فواتير الشراء...")
        cursor.execute("""
            CREATE TABLE sales_purchaseinvoiceitem (
                id INT AUTO_INCREMENT PRIMARY KEY,
                purchase_invoice_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """)
        
        # إدراج موردين تجريبيين
        print("👥 إضافة موردين تجريبيين...")
        cursor.execute("""
            INSERT INTO sales_supplier (name, email, phone, contact_person, is_active) VALUES
            ('شركة التوريدات الذهبية', '<EMAIL>', '+222-45-123456', 'أحمد ولد محمد', TRUE),
            ('مؤسسة النور للتجارة', '<EMAIL>', '+222-45-654321', 'فاطمة بنت أحمد', TRUE),
            ('شركة الصحراء للمواد الغذائية', '<EMAIL>', '+222-45-789012', 'محمد ولد عبدالله', TRUE)
        """)
        
        # تحديث migrations
        cursor.execute("""
            INSERT IGNORE INTO django_migrations (app, name, applied) VALUES 
            ('sales', '0003_supplier_purchaseinvoice_purchaseinvoiceitem', NOW())
        """)
        
        conn.commit()
        
        # التحقق من النتائج
        cursor.execute("SHOW TABLES LIKE 'sales_%'")
        tables = cursor.fetchall()
        
        print("\n✅ تم إنشاء الجداول بنجاح:")
        for table in tables:
            print(f"  ✅ {table[0]}")
        
        cursor.execute("SELECT COUNT(*) FROM sales_supplier")
        count = cursor.fetchone()[0]
        print(f"\n👥 عدد الموردين: {count}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 تم إصلاح المشكلة بنجاح!")
        print("🚀 يمكنك الآن تشغيل النظام:")
        print("python manage.py runserver")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("\n💡 تأكد من:")
        print("1. تشغيل MySQL في XAMPP/WAMP")
        print("2. وجود قاعدة البيانات sales_system")
        print("3. صحة كلمة مرور MySQL")
        return False

if __name__ == '__main__':
    success = fix_database()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
