{% load i18n %}
{% load translation_extras %}

<!-- Instant Language Switcher - Zero Delay -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i> 
        {% get_current_language_code as current_lang %}
        {% if current_lang == 'ar' %}العربية{% else %}Français{% endif %}
    </a>
    <ul class="dropdown-menu">
        {% get_current_language_code as current_lang %}
        {% if current_lang == 'ar' %}
            <li>
                <a href="/set-language/?language=fr" class="dropdown-item instant-link">
                    <i class="fas fa-language"></i> Français
                </a>
            </li>
        {% else %}
            <li>
                <a href="/set-language/?language=ar" class="dropdown-item instant-link">
                    <i class="fas fa-language"></i> العربية
                </a>
            </li>
        {% endif %}
    </ul>
</li>

<script>
// Instant language switching - zero delay
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.instant-link').forEach(function(link) {
        link.addEventListener('click', function(e) {
            // Don't prevent default - let the link work naturally
            // Just add visual feedback
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
        });
    });
});
</script>

<style>
.instant-link {
    transition: none !important; /* Remove all transitions for instant response */
}
.instant-link:hover {
    background-color: #f8f9fa !important;
}
.language-switcher .dropdown-toggle {
    transition: none !important;
}
</style>
