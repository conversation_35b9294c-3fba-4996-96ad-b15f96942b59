# نظام إدارة المبيعات - Sales Management System

نظام إدارة مبيعات شامل مبني باستخدام Django مع دعم اللغتين العربية والفرنسية والعملة الموريتانية (MRU).

![Logo](static/images/logo.jpg)

## الميزات الرئيسية

### 🏢 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- البحث المتقدم في قاعدة بيانات العملاء
- عرض تاريخ المشتريات لكل عميل

### 📦 إدارة المنتجات
- إدارة المنتجات والفئات
- تتبع المخزون التلقائي
- رفع صور المنتجات
- تنبيهات المخزون المنخفض

### 🧾 إدارة الفواتير
- إنشاء فواتير احترافية
- حساب الضرائب والخصومات تلقائياً
- طباعة الفواتير بتصميم جميل
- تتبع حالة الدفع (مدفوعة/معلقة/ملغاة)

### 📊 التقارير والإحصائيات
- تقارير المبيعات الشهرية والسنوية
- أفضل المنتجات والعملاء
- إحصائيات المخزون
- لوحة تحكم تفاعلية

### 🌍 دعم متعدد اللغات
- العربية (افتراضي)
- الفرنسية
- سهولة إضافة لغات جديدة

### 💰 العملة الموريتانية
- دعم كامل للأوقية الموريتانية (MRU)
- تنسيق العملة المحلي

### 📱 واجهة مستخدم متجاوبة
- تصميم حديث ومتجاوب
- يعمل على جميع الأجهزة (كمبيوتر، تابلت، هاتف)
- واجهة سهلة الاستخدام

## متطلبات النظام

- Python 3.8+
- MySQL 5.7+
- pip (مدير حزم Python)
- متصفح ويب حديث

## 🚀 التشغيل السريع (Quick Start)

### للمستخدمين الجدد - تشغيل تلقائي

#### Windows:
```bash
# تشغيل الملف التلقائي
run.bat
```

#### Linux/Mac:
```bash
# تشغيل الملف التلقائي
./run.sh
```

هذه الملفات ستقوم بـ:
- إنشاء البيئة الافتراضية تلقائياً
- تثبيت جميع المتطلبات
- إعداد قاعدة البيانات
- تشغيل الخادم

**ملاحظة مهمة**: تأكد من إنشاء ملف `.env` بإعدادات قاعدة البيانات قبل التشغيل.

---

## التثبيت والإعداد اليدوي

### 1. إنشاء بيئة افتراضية

```bash
python -m venv venv
```

### 2. تفعيل البيئة الافتراضية

**Windows:**
```bash
venv\Scripts\activate
```

**Linux/Mac:**
```bash
source venv/bin/activate
```

### 3. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات

1. إنشاء قاعدة بيانات MySQL:
```sql
CREATE DATABASE sales_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. تحديث ملف `.env` بمعلومات قاعدة البيانات:
```
DB_NAME=sales_system
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password
DB_HOST=localhost
DB_PORT=3306
```

### 5. تطبيق migrations

```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. إنشاء مستخدم إداري

```bash
python manage.py createsuperuser
```

### 7. جمع الملفات الثابتة

```bash
python manage.py collectstatic
```

### 8. تشغيل الخادم

```bash
python manage.py runserver
```

الآن يمكنك الوصول للنظام عبر: http://127.0.0.1:8000

## 📖 دليل الاستخدام

### 🔐 تسجيل الدخول
1. اذهب إلى http://127.0.0.1:8000
2. استخدم بيانات المستخدم الإداري التي أنشأتها
3. ستظهر لك لوحة التحكم الرئيسية

### 👥 إدارة العملاء
1. من القائمة الرئيسية، اختر "العملاء"
2. **إضافة عميل جديد**: انقر على "إضافة عميل جديد"
3. **تعديل عميل**: انقر على أيقونة التعديل بجانب اسم العميل
4. **البحث**: استخدم مربع البحث للعثور على عميل معين
5. **عرض التفاصيل**: انقر على اسم العميل لعرض تفاصيله وفواتيره

### 📦 إدارة المنتجات والفئات
1. **إضافة فئات أولاً**:
   - اذهب إلى "المنتجات" > "الفئات"
   - أضف فئات مثل: إلكترونيات، ملابس، مواد غذائية
2. **إضافة المنتجات**:
   - اذهب إلى "المنتجات" > "إضافة منتج"
   - املأ البيانات: الاسم، الفئة، السعر، الكمية
   - ارفع صورة للمنتج (اختياري)
3. **إدارة المخزون**: تتبع الكميات وتنبيهات المخزون المنخفض

### 🧾 إنشاء وإدارة الفواتير
1. **إنشاء فاتورة جديدة**:
   - اذهب إلى "الفواتير" > "إنشاء فاتورة"
   - اختر العميل من القائمة
   - أضف المنتجات والكميات
   - النظام يحسب الأسعار تلقائياً
2. **إضافة خصومات وضرائب**:
   - أدخل نسبة الخصم (إن وجدت)
   - أدخل نسبة الضريبة (14% في موريتانيا)
3. **حفظ وطباعة**:
   - احفظ الفاتورة
   - اطبعها أو صدرها كـ PDF

### 📊 التقارير والإحصائيات
- **لوحة التحكم**: عرض سريع للإحصائيات الرئيسية
- **تقارير المبيعات**: مبيعات شهرية وسنوية
- **أفضل المنتجات**: المنتجات الأكثر مبيعاً
- **أفضل العملاء**: العملاء الأكثر شراءً
- **تقارير المخزون**: المنتجات قليلة المخزون

### 🌍 تغيير اللغة
يمكن تغيير اللغة من القائمة العلوية:
- **العربية** (افتراضي)
- **Français** (الفرنسية)

### 📱 استخدام النظام على الهاتف
- النظام متجاوب ويعمل على جميع الأجهزة
- يمكن استخدامه على الهاتف والتابلت
- جميع الميزات متاحة على الأجهزة المحمولة

## الملفات المهمة

- `sales/models.py`: نماذج قاعدة البيانات
- `sales/views.py`: منطق التطبيق
- `sales/forms.py`: نماذج الإدخال
- `templates/`: قوالب HTML
- `static/`: ملفات CSS و JavaScript
- `requirements.txt`: متطلبات Python

## المساهمة

لتطوير النظام:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المشروع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

---

## Features (English)

- **Customer Management**: Add, edit, and delete customers
- **Product Management**: Manage products and categories with inventory tracking
- **Invoice Management**: Create and print invoices with tax and discount calculations
- **Reports**: Sales reports and statistics
- **Multi-language Support**: Arabic and French
- **Mauritanian Currency**: Full support for Mauritanian Ouguiya (MRU)
- **Responsive UI**: Works on all devices

## Installation (English)

1. Create virtual environment: `python -m venv venv`
2. Activate: `venv\Scripts\activate` (Windows) or `source venv/bin/activate` (Linux/Mac)
3. Install requirements: `pip install -r requirements.txt`
4. Setup MySQL database and update `.env` file
5. Run migrations: `python manage.py migrate`
6. Create superuser: `python manage.py createsuperuser`
7. Run server: `python manage.py runserver`

Access the system at: http://127.0.0.1:8000
