{% load static %}
{% load i18n %}
{% load translation_extras %}
<!DOCTYPE html>
{% get_current_language_code as LANGUAGE_CODE %}
<html lang="{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}{% trans "نظام إدارة المبيعات" %}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    {% if LANGUAGE_CODE == 'ar' %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="{% static 'css/table-enhancements.css' %}">
    <link rel="stylesheet" href="{% static 'css/global-dark-tables.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- CSRF Token for AJAX requests -->
    {% csrf_token %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'sales:dashboard' %}">
                <img src="{% static 'images/logo.jpg' %}" alt="Logo" height="40" class="me-2">
                {% trans "نظام إدارة المبيعات" %}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if user.is_authenticated %}
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'sales:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> {% trans_fallback "لوحة التحكم" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users"></i> {% trans_fallback "العملاء" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'sales:customer_list' %}">{% trans_fallback "قائمة العملاء" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:customer_add' %}">{% trans_fallback "إضافة عميل" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-box"></i> {% trans_fallback "المنتجات" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'sales:product_list' %}">{% trans_fallback "قائمة المنتجات" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:product_add' %}">{% trans_fallback "إضافة منتج جديد" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'sales:category_list' %}">{% trans_fallback "الفئات" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:category_add' %}">{% trans_fallback "إضافة فئة" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-truck"></i> {% trans_fallback "الموردين" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'sales:supplier_list' %}">{% trans_fallback "قائمة الموردين" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:supplier_add' %}">{% trans_fallback "إضافة مورد جديد" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-file-invoice"></i> {% trans_fallback "الفواتير" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><h6 class="dropdown-header">{% trans_fallback "فواتير البيع" %}</h6></li>
                            <li><a class="dropdown-item" href="{% url 'sales:invoice_list' %}">{% trans_fallback "قائمة الفواتير" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:invoice_add' %}">{% trans_fallback "إضافة فاتورة جديدة" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">{% trans_fallback "فواتير الشراء" %}</h6></li>
                            <li><a class="dropdown-item" href="{% url 'sales:purchase_invoice_list' %}">{% trans_fallback "قائمة فواتير الشراء" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:purchase_invoice_add' %}">{% trans_fallback "إضافة فاتورة شراء" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-chart-bar"></i> {% trans_fallback "التقارير" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'sales:reports' %}">
                                <i class="fas fa-chart-pie"></i> {% trans_fallback "التقارير العامة" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'sales:advanced_reports' %}">
                                <i class="fas fa-chart-line"></i> {% trans_fallback "التقارير المتقدمة" %}
                            </a></li>
                        </ul>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Compact Language Switcher -->
                    {% include 'sales/compact_language_switcher.html' %}

                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'admin:index' %}">{% trans "لوحة الإدارة" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'sales:logout' %}">{% trans "تسجيل الخروج" %}</a></li>
                        </ul>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 {% trans "نظام إدارة المبيعات" %}. {% trans "جميع الحقوق محفوظة" %}.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/table-enhancements.js' %}"></script>

    {% block extra_js %}{% endblock %}

    <!-- Language Switching Enhancement -->
    <script>
    // Global translation dictionary
    {% get_translation_dict as translation_dict %}
    window.translations = {{ translation_dict|safe }};

    document.addEventListener('DOMContentLoaded', function() {
        // Remove transition class after page load
        setTimeout(() => {
            document.body.classList.remove('switching');
        }, 500);

        // Update Bootstrap CSS based on current direction
        updateBootstrapCSS();

        // Apply initial translations and direction
        const currentLang = document.documentElement.getAttribute('lang') || 'ar';
        const currentDir = document.documentElement.getAttribute('dir') || 'rtl';

        if (window.translations && window.translations[currentLang]) {
            translatePageContent(currentLang);
        }

        // Apply initial direction styles
        applyInitialDirectionStyles(currentDir);
    });

    // Function to apply initial direction styles with custom ordering
    function applyInitialDirectionStyles(direction) {
        const navbar = document.querySelector('.navbar');
        const navbarNav = document.querySelector('.navbar-nav');
        const navbarBrand = document.querySelector('.navbar-brand');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        const containerFluid = document.querySelector('.container-fluid');

        if (direction === 'rtl') {
            // Apply RTL styles
            if (navbarNav) {
                navbarNav.style.flexDirection = 'row-reverse';
                navbarNav.style.display = 'flex';
            }
            if (navbarBrand) {
                navbarBrand.style.marginRight = '0';
                navbarBrand.style.marginLeft = 'auto';
            }
            if (navbarCollapse) {
                navbarCollapse.style.justifyContent = 'flex-start';
            }
            if (containerFluid) {
                containerFluid.style.flexDirection = 'row-reverse';
            }

            // Apply custom ordering for RTL (Arabic) - نظام إدارة المبيعات بجانب لوحة التحكم
            const navItems = document.querySelectorAll('.nav-item:not(.language-switcher)');
            navItems.forEach(function(item, index) {
                // Custom order: نظام إدارة المبيعات بجانب لوحة التحكم
                const rtlOrder = [7, 6, 5, 4, 3, 2, 1]; // [نظام إدارة المبيعات، لوحة التحكم، العملاء، المنتجات، الموردين، الفواتير، التقارير]
                if (index < rtlOrder.length) {
                    item.style.order = rtlOrder[index];
                }
                item.style.marginLeft = '0.5rem';
                item.style.marginRight = '0';
            });

            // Language switcher always at the end for RTL
            const languageSwitcher = document.querySelector('.language-switcher');
            if (languageSwitcher) {
                languageSwitcher.style.order = '-1';
            }

            // Apply RTL to nav links
            document.querySelectorAll('.nav-link').forEach(function(link) {
                link.style.textAlign = 'right';
            });
        } else {
            // Apply LTR styles
            if (navbarNav) {
                navbarNav.style.flexDirection = 'row';
                navbarNav.style.display = 'flex';
            }
            if (navbarBrand) {
                navbarBrand.style.marginLeft = '0';
                navbarBrand.style.marginRight = 'auto';
            }
            if (navbarCollapse) {
                navbarCollapse.style.justifyContent = 'flex-end';
            }
            if (containerFluid) {
                containerFluid.style.flexDirection = 'row';
            }

            // Apply normal ordering for LTR (French)
            const navItems = document.querySelectorAll('.nav-item:not(.language-switcher)');
            navItems.forEach(function(item, index) {
                item.style.order = index + 1;
                item.style.marginRight = '0.5rem';
                item.style.marginLeft = '0';
            });

            // Language switcher always at the end for LTR
            const languageSwitcher = document.querySelector('.language-switcher');
            if (languageSwitcher) {
                languageSwitcher.style.order = '99';
            }

            // Apply LTR to nav links
            document.querySelectorAll('.nav-link').forEach(function(link) {
                link.style.textAlign = 'left';
            });
        }
    }

    // Function to translate page content
    function translatePageContent(language) {
        if (!window.translations || !window.translations[language]) return;

        const dict = window.translations[language];

        // Translate navigation links and all text elements
        document.querySelectorAll('a, button, span:not(.fas):not(.fa), h1, h2, h3, h4, h5, h6, p, td, th, label, .nav-link, .dropdown-item').forEach(function(element) {
            // Skip elements with icons but translate text parts
            if (element.querySelector('.fas, .fa')) {
                // For elements with icons, only translate the text part
                const textNodes = [];
                function getTextNodes(node) {
                    if (node.nodeType === Node.TEXT_NODE) {
                        textNodes.push(node);
                    } else {
                        for (let child of node.childNodes) {
                            getTextNodes(child);
                        }
                    }
                }
                getTextNodes(element);

                textNodes.forEach(function(textNode) {
                    const text = textNode.textContent.trim();
                    if (text && dict[text] && dict[text] !== text) {
                        textNode.textContent = dict[text];
                    }
                });
            } else {
                const text = element.textContent.trim();
                if (text && dict[text] && dict[text] !== text) {
                    element.textContent = dict[text];
                }
            }
        });

        // Translate dropdown headers
        document.querySelectorAll('.dropdown-header').forEach(function(element) {
            const text = element.textContent.trim();
            if (text && dict[text]) {
                element.textContent = dict[text];
            }
        });

        // Translate placeholders
        document.querySelectorAll('input[placeholder], textarea[placeholder]').forEach(function(element) {
            const placeholder = element.getAttribute('placeholder');
            if (placeholder && dict[placeholder]) {
                element.setAttribute('placeholder', dict[placeholder]);
            }
        });

        // Translate titles
        document.querySelectorAll('[title]').forEach(function(element) {
            const title = element.getAttribute('title');
            if (title && dict[title]) {
                element.setAttribute('title', dict[title]);
            }
        });
    }

    // Function to update Bootstrap CSS based on direction
    function updateBootstrapCSS() {
        const htmlRoot = document.getElementById('html-root');
        const currentDir = htmlRoot.getAttribute('dir');
        const currentLang = htmlRoot.getAttribute('lang');

        // Find existing Bootstrap CSS link
        const existingBootstrap = document.querySelector('link[href*="bootstrap"]');

        if (existingBootstrap) {
            let newHref;
            if (currentDir === 'rtl' || currentLang === 'ar') {
                newHref = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css';
            } else {
                newHref = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
            }

            // Only update if different
            if (existingBootstrap.href !== newHref) {
                existingBootstrap.href = newHref;
            }
        }
    }

    // Add keyboard shortcut for language switching (Alt + L)
    document.addEventListener('keydown', function(e) {
        if (e.altKey && e.key === 'l') {
            e.preventDefault();
            const languageDropdown = document.querySelector('.language-switcher .dropdown-toggle');
            if (languageDropdown) {
                languageDropdown.click();
            }
        }
    });

    // Listen for direction changes and update Bootstrap accordingly
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' &&
                (mutation.attributeName === 'dir' || mutation.attributeName === 'lang')) {
                updateBootstrapCSS();
            }
        });
    });

    // Start observing
    const htmlRoot = document.getElementById('html-root');
    if (htmlRoot) {
        observer.observe(htmlRoot, {
            attributes: true,
            attributeFilter: ['dir', 'lang']
        });
    }
    </script>
</body>
</html>
