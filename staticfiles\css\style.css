/* Custom styles for Sales Management System */

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

[dir="rtl"] .me-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

[dir="rtl"] .me-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

/* Enhanced RTL/LTR Support with Custom Order Reversal */
[dir="rtl"] .navbar-nav {
    flex-direction: row-reverse !important;
    display: flex !important;
}

[dir="ltr"] .navbar-nav {
    flex-direction: row !important;
    display: flex !important;
}

/* Custom ordering for RTL - Arabic (نظام إدارة المبيعات بجانب لوحة التحكم) */
[dir="rtl"] .nav-item:nth-child(1) { order: 7 !important; } /* نظام إدارة المبيعات - بجانب لوحة التحكم */
[dir="rtl"] .nav-item:nth-child(2) { order: 6 !important; } /* لوحة التحكم */
[dir="rtl"] .nav-item:nth-child(3) { order: 5 !important; } /* العملاء */
[dir="rtl"] .nav-item:nth-child(4) { order: 4 !important; } /* المنتجات */
[dir="rtl"] .nav-item:nth-child(5) { order: 3 !important; } /* الموردين */
[dir="rtl"] .nav-item:nth-child(6) { order: 2 !important; } /* الفواتير */
[dir="rtl"] .nav-item:nth-child(7) { order: 1 !important; } /* التقارير */

/* Normal ordering for LTR - French */
[dir="ltr"] .nav-item:nth-child(1) { order: 1; }
[dir="ltr"] .nav-item:nth-child(2) { order: 2; }
[dir="ltr"] .nav-item:nth-child(3) { order: 3; }
[dir="ltr"] .nav-item:nth-child(4) { order: 4; }
[dir="ltr"] .nav-item:nth-child(5) { order: 5; }
[dir="ltr"] .nav-item:nth-child(6) { order: 6; }
[dir="ltr"] .nav-item:nth-child(7) { order: 7; }

[dir="rtl"] .navbar-collapse {
    justify-content: flex-start !important;
}

[dir="ltr"] .navbar-collapse {
    justify-content: flex-end !important;
}

[dir="rtl"] .nav-item {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="ltr"] .nav-item {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

/* Container direction with flex reversal */
[dir="rtl"] .container-fluid {
    flex-direction: row-reverse !important;
}

[dir="ltr"] .container-fluid {
    flex-direction: row !important;
}

/* Navbar toggler positioning */
[dir="rtl"] .navbar-toggler {
    margin-left: 0 !important;
    margin-right: auto !important;
    order: 10;
}

[dir="ltr"] .navbar-toggler {
    margin-right: 0 !important;
    margin-left: auto !important;
    order: 10;
}

/* Dropdown menu positioning */
[dir="rtl"] .dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

[dir="ltr"] .dropdown-menu {
    left: 0 !important;
    right: auto !important;
}

/* Language switcher positioning */
[dir="rtl"] .language-switcher {
    order: -1 !important; /* Always at the end for RTL */
}

[dir="ltr"] .language-switcher {
    order: 99 !important; /* Always at the end for LTR */
}

/* Smooth transitions for direction changes */
.navbar, .navbar-nav, .navbar-brand, .navbar-collapse, .container-fluid, .nav-item, .nav-link {
    transition: all 0.3s ease !important;
}

/* Arabic Font */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

[lang="ar"] {
    font-family: 'Amiri', 'Times New Roman', serif;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 10px 10px 0 0 !important;
}

/* Button Styles */
.btn {
    border-radius: 5px;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
}

.table tbody tr:hover {
    background-color: #f8f9fc;
}

/* Enhanced Table Text Visibility */
.table tbody td {
    color: #ffffff !important;
    font-weight: 500;
    font-size: 14px;
    vertical-align: middle;
    background-color: #2c3e50 !important;
}

/* GLOBAL DARK THEME FOR ALL TABLES */
/* تطبيق الخلفية السوداء على جميع الجداول */

/* All table cells - basic styling */
table td,
table th,
.table td,
.table th,
.table-responsive td,
.table-responsive th,
.changelist-results td,
.changelist-results th,
#changelist-table td,
#changelist-table th,
.results td,
.results th {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
    border-color: #4a6741 !important;
    font-weight: 500;
    padding: 12px 8px;
}

/* Table headers with gradient */
table thead th,
.table thead th,
.table-responsive thead th,
.changelist-results thead th,
#changelist-table thead th,
.results thead th {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
    color: #ffffff !important;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
    border-bottom: 2px solid #4a6741 !important;
}

/* Alternating rows for ALL tables */
table tbody tr:nth-child(even) td,
.table tbody tr:nth-child(even) td,
.table-responsive tbody tr:nth-child(even) td,
.changelist-results tbody tr:nth-child(even) td,
#changelist-table tbody tr:nth-child(even) td,
.results tbody tr:nth-child(even) td {
    background-color: #34495e !important;
    color: #ffffff !important;
}

/* Hover effects for ALL tables */
table tbody tr:hover td,
.table tbody tr:hover td,
.table-responsive tbody tr:hover td,
.changelist-results tbody tr:hover td,
#changelist-table tbody tr:hover td,
.results tbody tr:hover td {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
    transition: all 0.3s ease;
}

/* ENHANCED TEXT COLORS FOR ALL TABLES */
/* ألوان النصوص المحسنة لجميع الجداول */

/* Numbers and IDs - Red */
table td:first-child,
.table td:first-child,
table td[class*="id"],
.table td[class*="id"],
table td:contains("22"),
table td:contains("48"),
table td:contains("44"),
table td:contains("95"),
table td:contains("29") {
    color: #ff6b6b !important;
    font-weight: 700;
    font-size: 16px;
    font-family: 'Courier New', monospace;
}

/* Prices and amounts - Green */
table td:contains("25000"),
table td:contains("100"),
table td:contains("200"),
table td:contains("50"),
table td:contains("30000"),
table td:contains("أوقية"),
table td:contains("MRU"),
table td:contains(".00"),
.table td:contains("25000"),
.table td:contains("100"),
.table td:contains("200"),
.table td:contains("50"),
.table td:contains("30000"),
.table td:contains("أوقية"),
.table td:contains("MRU"),
.table td:contains(".00") {
    color: #51cf66 !important;
    font-weight: 700;
    font-size: 16px;
}

/* Product names and text - Purple */
table td:contains("elektronik"),
table td:contains("CHOKLOT"),
table td:contains("DEL"),
.table td:contains("elektronik"),
.table td:contains("CHOKLOT"),
.table td:contains("DEL") {
    color: #da77f2 !important;
    font-weight: 600;
    font-size: 14px;
}

/* Product codes - Blue */
table td:contains("A07"),
table td:contains("Allow"),
table td:contains("Biscrem"),
table td:contains("biscuit"),
.table td:contains("A07"),
.table td:contains("Allow"),
.table td:contains("Biscrem"),
.table td:contains("biscuit") {
    color: #74c0fc !important;
    font-weight: 600;
    font-size: 14px;
    background-color: rgba(116, 192, 252, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

/* Light table with better contrast */
.table-light th {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    font-weight: 600;
    border-color: #dee2e6;
}

.table-light td {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
    border-color: #dee2e6;
}

/* Striped tables with enhanced visibility */
.table-striped tbody tr:nth-of-type(odd) td {
    background-color: #34495e !important;
    color: #ffffff !important;
}

.table-striped tbody tr:nth-of-type(even) td {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
}

/* Hover effects for better interaction */
.table-hover tbody tr:hover td {
    background-color: #4a6741 !important;
    color: #ffffff !important;
    transition: all 0.2s ease;
}

/* Bordered tables */
.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6 !important;
}

/* Table text alignment for RTL */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

[dir="ltr"] .table th,
[dir="ltr"] .table td {
    text-align: left;
}

/* Form Styles */
.form-control {
    border-radius: 5px;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    padding: 0.375rem 0.75rem;
    border-radius: 10px;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.navbar-brand img {
    border-radius: 50%;
}

/* Dashboard Statistics Cards */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .container-fluid {
        margin: 0;
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}

/* Invoice Styles */
.invoice-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px 10px 0 0;
}

.invoice-details {
    background-color: #f8f9fc;
    padding: 1.5rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.invoice-total {
    background-color: #e3f2fd;
    padding: 1rem;
    border-radius: 5px;
    border-left: 4px solid #2196f3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Animation */
.card, .btn {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* DJANGO ADMIN DARK THEME */
/* تطبيق الخلفية السوداء على جداول الإدارة */

.admin .table,
.changelist .table,
#changelist-table,
.module table,
.results table,
.changelist-results table {
    background-color: #2c3e50 !important;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #4a6741;
}

.admin .table thead th,
.changelist .table thead th,
#changelist-table thead th,
.module table thead th,
.results table thead th,
.changelist-results table thead th {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
    color: #ffffff !important;
    font-weight: 700;
    border: none !important;
    padding: 15px 12px;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
}

.admin .table tbody td,
.changelist .table tbody td,
#changelist-table tbody td,
.module table tbody td,
.results table tbody td,
.changelist-results table tbody td {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
    font-weight: 500;
    padding: 12px 10px;
    border-bottom: 1px solid #4a6741 !important;
}

.admin .table tbody tr:nth-child(even) td,
.changelist .table tbody tr:nth-child(even) td,
#changelist-table tbody tr:nth-child(even) td,
.module table tbody tr:nth-child(even) td,
.results table tbody tr:nth-child(even) td,
.changelist-results table tbody tr:nth-child(even) td {
    background-color: #34495e !important;
}

.admin .table tbody tr:hover td,
.changelist .table tbody tr:hover td,
#changelist-table tbody tr:hover td,
.module table tbody tr:hover td,
.results table tbody tr:hover td,
.changelist-results table tbody tr:hover td {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

/* Form table styling */
.form-table td,
.form-table th {
    color: #2c3e50 !important;
    font-weight: 500;
    padding: 10px 12px;
}

.form-table th {
    background-color: #f8f9fa !important;
    font-weight: 600;
}

/* Search results table */
.search-results table td {
    color: #2c3e50 !important;
    font-weight: 500;
}

.search-results table th {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    font-weight: 600;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Print styles for single page invoice */
@media print {
    @page {
        size: A4;
        margin: 0.5cm;
    }

    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    body {
        font-size: 10px !important;
        line-height: 1.2 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .no-print {
        display: none !important;
    }

    .invoice-container {
        max-width: 100% !important;
        margin: 0 !important;
        padding: 5px !important;
        transform: scale(0.85) !important;
        transform-origin: top left !important;
        width: 118% !important;
    }

    .invoice-header {
        margin-bottom: 8px !important;
        padding: 5px !important;
    }

    .invoice-header h1 {
        font-size: 16px !important;
        margin: 0 0 5px 0 !important;
    }

    .invoice-header h2 {
        font-size: 14px !important;
        margin: 0 0 3px 0 !important;
    }

    .invoice-info {
        font-size: 9px !important;
        margin-bottom: 8px !important;
    }

    .invoice-table {
        font-size: 9px !important;
        width: 100% !important;
        border-collapse: collapse !important;
        margin-bottom: 8px !important;
        page-break-inside: avoid !important;
    }

    .invoice-table th,
    .invoice-table td {
        padding: 3px 2px !important;
        font-size: 8px !important;
        border: 1px solid #ddd !important;
        line-height: 1.1 !important;
    }

    .invoice-table th {
        background-color: #f8f9fa !important;
        font-weight: bold !important;
        font-size: 9px !important;
    }

    .invoice-totals {
        font-size: 9px !important;
        margin-top: 5px !important;
    }

    .invoice-totals .total-row {
        font-size: 10px !important;
        font-weight: bold !important;
    }

    .invoice-footer {
        font-size: 8px !important;
        margin-top: 8px !important;
        page-break-inside: avoid !important;
    }

    /* تصغير المسافات */
    .row {
        margin: 0 !important;
    }

    .col-md-6, .col-md-12 {
        padding: 2px !important;
    }

    /* تصغير العناوين */
    h1, h2, h3, h4, h5, h6 {
        margin: 2px 0 !important;
        line-height: 1.1 !important;
    }

    /* إخفاء عناصر غير ضرورية */
    .btn, .navbar, .sidebar, .breadcrumb {
        display: none !important;
    }
}

/* Reports Page Styles */
.border-left-primary {
    border-left: 4px solid #4e73df !important;
}

.border-left-success {
    border-left: 4px solid #1cc88a !important;
}

.border-left-info {
    border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}

.border-left-danger {
    border-left: 4px solid #e74a3b !important;
}

.border-left-secondary {
    border-left: 4px solid #858796 !important;
}

.border-left-dark {
    border-left: 4px solid #5a5c69 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.badge {
    font-size: 0.75em;
    padding: 0.375rem 0.5rem;
}

.table-sm th,
.table-sm td {
    padding: 0.5rem;
    vertical-align: middle;
}

.chart-container {
    position: relative;
    height: 300px;
}

/* Stats Cards Animation */
.card-body {
    transition: all 0.3s ease;
}

.card:hover .card-body {
    background-color: #f8f9fc;
}

/* Chart Responsive */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Print Styles for Reports */
@media print {
    .no-print {
        display: none !important;
    }

    .card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .chart-container {
        height: 200px;
    }
}

/* Advanced Reports Styles */
.report-filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.report-filter-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.report-filter-card .form-label {
    color: white;
    font-weight: 600;
}

.report-filter-card .form-control,
.report-filter-card .form-select {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 8px;
}

.report-period-info {
    background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    color: white;
    border-radius: 10px;
}

.stats-card {
    transition: all 0.3s ease;
    border-radius: 15px;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

.stats-label {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.chart-card {
    border-radius: 15px;
    overflow: hidden;
}

.chart-card .card-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.table-card {
    border-radius: 15px;
    overflow: hidden;
}

.table-card .table {
    margin-bottom: 0;
}

.table-card .table thead th {
    background: #f8f9fc;
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}

/* Admin and Data Tables Enhanced Styling */
.admin-table {
    background-color: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff !important;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
    padding: 15px 12px;
    border: none;
}

.admin-table tbody td {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
    font-weight: 500;
    padding: 12px;
    border-bottom: 1px solid #4a6741;
    vertical-align: middle;
}

.admin-table tbody tr:nth-child(even) td {
    background-color: #34495e !important;
}

.admin-table tbody tr:hover td {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Product and Inventory Tables */
.product-table td {
    font-size: 14px;
    font-weight: 500;
}

.product-table .product-name {
    color: #2c3e50 !important;
    font-weight: 600;
}

.product-table .product-price {
    color: #27ae60 !important;
    font-weight: 600;
    font-size: 15px;
}

.product-table .product-category {
    color: #8e44ad !important;
    font-weight: 500;
}

.product-table .product-stock {
    font-weight: 600;
}

/* Invoice and Financial Tables */
.financial-table .amount {
    color: #27ae60 !important;
    font-weight: 600;
    font-size: 15px;
}

.financial-table .invoice-number {
    color: #3498db !important;
    font-weight: 600;
}

.financial-table .customer-name {
    color: #2c3e50 !important;
    font-weight: 500;
}

/* Status badges in tables */
.table .badge {
    font-size: 11px;
    font-weight: 600;
    padding: 6px 10px;
    border-radius: 15px;
}

/* Responsive table text */
@media (max-width: 768px) {
    .table tbody td {
        font-size: 12px;
        padding: 8px 6px;
    }

    .admin-table thead th {
        font-size: 11px;
        padding: 10px 8px;
    }
}

/* Enhanced text colors for dark table cells */
.table td strong,
.table td .text-primary,
.table td .text-success,
.table td .text-info,
.table td .text-warning,
.table td .text-danger {
    color: inherit !important;
}

/* Specific styling for different data types in dark tables */
.table td[class*="product-"],
.table td[class*="data-"] {
    font-weight: 600;
}

/* Price and number highlighting */
.table td:contains("أوقية"),
.table td:contains("MRU"),
.table td:contains(".00") {
    color: #51cf66 !important;
    font-weight: 700;
}

/* ID and code highlighting */
.table td:first-child {
    color: #ff6b6b !important;
    font-weight: 700;
}

/* ENHANCED BADGES AND ELEMENTS IN DARK TABLES */
/* الشارات والعناصر المحسنة في الجداول السوداء */

/* All badges in tables */
table .badge,
.table .badge,
.changelist .badge,
.results .badge {
    font-weight: 700;
    font-size: 11px;
    padding: 6px 12px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
}

.table .badge.bg-success,
table .badge.bg-success {
    background-color: #51cf66 !important;
    color: #ffffff !important;
    border-color: #40c057;
}

.table .badge.bg-danger,
table .badge.bg-danger {
    background-color: #ff6b6b !important;
    color: #ffffff !important;
    border-color: #ff5252;
}

.table .badge.bg-warning,
table .badge.bg-warning {
    background-color: #ffd43b !important;
    color: #2c3e50 !important;
    border-color: #ffcc02;
}

.table .badge.bg-info,
table .badge.bg-info {
    background-color: #74c0fc !important;
    color: #ffffff !important;
    border-color: #339af0;
}

.table .badge.bg-primary,
table .badge.bg-primary {
    background-color: #da77f2 !important;
    color: #ffffff !important;
    border-color: #cc5de8;
}

/* Links in dark tables */
table a,
.table a,
.changelist a,
.results a {
    color: #74c0fc !important;
    text-decoration: none;
    font-weight: 600;
}

table a:hover,
.table a:hover,
.changelist a:hover,
.results a:hover {
    color: #339af0 !important;
    text-decoration: underline;
}

/* Strong text in tables */
table strong,
.table strong,
.changelist strong,
.results strong {
    color: #ffd43b !important;
    font-weight: 700;
}

/* Small text in tables */
table small,
.table small,
.changelist small,
.results small {
    color: #ced4da !important;
    font-size: 11px;
}

/* Special styling for product data tables like in the screenshot */
.product-data-table {
    background-color: #ffffff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.product-data-table thead th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ffffff !important;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 1px;
    padding: 18px 15px;
    border: none;
    text-align: center;
}

.product-data-table tbody td {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
    font-weight: 600;
    font-size: 15px;
    padding: 15px;
    border-bottom: 1px solid #4a6741;
    text-align: center;
    vertical-align: middle;
}

.product-data-table tbody tr:nth-child(even) td {
    background-color: #34495e !important;
}

.product-data-table tbody tr:hover td {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
    transform: scale(1.02);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* Product ID/Number styling */
.product-data-table .product-id {
    color: #ff6b6b !important;
    font-weight: 700;
    font-size: 16px;
}

/* Product price styling */
.product-data-table .product-price {
    color: #51cf66 !important;
    font-weight: 700;
    font-size: 16px;
}

/* Product name/type styling */
.product-data-table .product-name {
    color: #da77f2 !important;
    font-weight: 600;
    font-size: 14px;
}

/* Product code styling */
.product-data-table .product-code {
    color: #74c0fc !important;
    font-weight: 600;
    font-size: 14px;
}

/* Dark theme alternative for product tables */
.product-data-table.dark-theme {
    background-color: #2c3e50;
}

.product-data-table.dark-theme thead th {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    color: #ecf0f1 !important;
}

.product-data-table.dark-theme tbody td {
    background-color: #34495e !important;
    color: #ecf0f1 !important;
    border-bottom: 1px solid #4a6741;
}

.product-data-table.dark-theme tbody tr:nth-child(even) td {
    background-color: #3c5a78 !important;
}

.product-data-table.dark-theme tbody tr:hover td {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
}

/* Compact table version for smaller screens */
.product-data-table.compact tbody td {
    padding: 10px 8px;
    font-size: 13px;
}

.product-data-table.compact thead th {
    padding: 12px 8px;
    font-size: 11px;
}

.badge-custom {
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
}

.quick-actions .btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quick-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Date Input Styling */
.form-control[type="date"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M1 4h14M1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4M1 4V2a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v2M5 2v2m6-2v2'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-value {
        font-size: 1.5rem;
    }

    .chart-card,
    .table-card,
    .stats-card {
        margin-bottom: 1rem;
    }

    .quick-actions .btn {
        margin-bottom: 0.5rem;
    }
}

/* Barcode Styles */
.barcode-container {
    background: white;
    padding: 20px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    text-align: center;
}

.barcode-number {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: bold;
    letter-spacing: 2px;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    display: inline-block;
    margin: 10px 0;
}

.barcode-display {
    background: white;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin: 10px 0;
}

.barcode-label {
    border: 1px solid #000;
    padding: 10px;
    margin: 5px;
    background: white;
    display: inline-block;
    text-align: center;
    font-family: Arial, sans-serif;
}

.barcode-search {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.barcode-search .form-control {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    border-radius: 25px;
}

.barcode-actions .btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    margin: 0.25rem;
    transition: all 0.3s ease;
}

.barcode-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.product-barcode-badge {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 4px;
}

/* Print Styles for Barcode */
@media print {
    .barcode-label {
        break-inside: avoid;
        margin: 0.2cm;
        border: 1px solid #000;
    }

    .no-print {
        display: none !important;
    }

    body {
        margin: 0;
        padding: 0;
    }
}

/* Language Support - RTL/LTR */
html[lang="ar"] {
    direction: rtl;
}

html[lang="fr"] {
    direction: ltr;
}

/* Language Switcher Styles */
.language-switcher .dropdown-item {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    transition: all 0.3s ease;
}

html[lang="ar"] .language-switcher .dropdown-item {
    text-align: right;
}

.language-switcher .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.language-switcher .dropdown-item:focus {
    outline: none;
    background-color: #e9ecef;
}

/* Navbar adjustments for different languages */
html[lang="fr"] .navbar-nav {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

html[lang="ar"] .navbar-nav {
    font-family: 'Segoe UI', 'Arabic UI Text', 'Tahoma', sans-serif;
}

/* Form adjustments for French */
html[lang="fr"] .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

html[lang="fr"] .btn {
    font-weight: 500;
}

/* Table adjustments */
html[lang="fr"] .table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Card title adjustments */
html[lang="fr"] .card-header h6 {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Responsive language adjustments */
@media (max-width: 768px) {
    .language-switcher .dropdown-menu {
        min-width: 120px;
    }

    html[lang="fr"] .navbar-brand {
        font-size: 1rem;
    }
}

/* Language indicator in navbar */
.language-indicator {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Smooth transitions for language switching */
.fade-transition {
    transition: opacity 0.3s ease-in-out;
}

.fade-transition.switching {
    opacity: 0.7;
}
