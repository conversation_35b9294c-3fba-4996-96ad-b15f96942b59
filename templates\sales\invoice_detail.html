{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "فاتورة" %} {{ invoice.invoice_number }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:dashboard' %}">{% trans "لوحة التحكم" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'sales:invoice_list' %}">{% trans "الفواتير" %}</a></li>
                <li class="breadcrumb-item active">{{ invoice.invoice_number }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <!-- Invoice Header -->
            <div class="invoice-header text-white">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <img src="{% static 'images/logo.jpg' %}" alt="Logo" class="me-3" style="width: 60px; height: 60px; border-radius: 50%;">
                            <div>
                                <h3 class="mb-0">{% trans "نظام إدارة المبيعات" %}</h3>
                                <p class="mb-0">{% trans "فاتورة مبيعات" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <h2 class="mb-0">{{ invoice.invoice_number }}</h2>
                        <p class="mb-0">{{ invoice.date_created|date:"Y-m-d" }}</p>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- Action Buttons -->
                <div class="row mb-4 no-print">
                    <div class="col-12">
                        <div class="btn-group" role="group">
                            <a href="{% url 'sales:invoice_print' invoice.pk %}" class="btn btn-primary" target="_blank">
                                <i class="fas fa-print"></i> {% trans "طباعة" %}
                            </a>
                            {% if invoice.payment_status == 'pending' %}
                                <a href="{% url 'sales:invoice_edit' invoice.pk %}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> {% trans "تعديل" %}
                                </a>
                            {% endif %}
                            <a href="{% url 'sales:invoice_delete' invoice.pk %}" class="btn btn-danger delete-confirm">
                                <i class="fas fa-trash"></i> {% trans "حذف" %}
                            </a>
                            <button onclick="window.print()" class="btn btn-secondary">
                                <i class="fas fa-print"></i> {% trans "طباعة سريعة" %}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Invoice Details -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user"></i> {% trans "بيانات العميل" %}
                            </h6>
                            <p class="mb-1"><strong>{% trans "الاسم" %}:</strong> {{ invoice.customer.name }}</p>
                            {% if invoice.customer.email %}
                                <p class="mb-1"><strong>{% trans "البريد الإلكتروني" %}:</strong> {{ invoice.customer.email }}</p>
                            {% endif %}
                            {% if invoice.customer.phone %}
                                <p class="mb-1"><strong>{% trans "الهاتف" %}:</strong> {{ invoice.customer.phone }}</p>
                            {% endif %}
                            {% if invoice.customer.address %}
                                <p class="mb-1"><strong>{% trans "العنوان" %}:</strong> {{ invoice.customer.address }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-file-invoice"></i> {% trans "تفاصيل الفاتورة" %}
                            </h6>
                            <p class="mb-1"><strong>{% trans "رقم الفاتورة" %}:</strong> {{ invoice.invoice_number }}</p>
                            <p class="mb-1"><strong>{% trans "تاريخ الإنشاء" %}:</strong> {{ invoice.date_created|date:"Y-m-d H:i" }}</p>
                            {% if invoice.date_due %}
                                <p class="mb-1"><strong>{% trans "تاريخ الاستحقاق" %}:</strong> {{ invoice.date_due|date:"Y-m-d" }}</p>
                            {% endif %}
                            <p class="mb-1"><strong>{% trans "المستخدم" %}:</strong> {{ invoice.user.username }}</p>
                            <p class="mb-1">
                                <strong>{% trans "حالة الدفع" %}:</strong>
                                {% if invoice.payment_status == 'paid' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> {% trans "مدفوعة" %}
                                    </span>
                                {% elif invoice.payment_status == 'pending' %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock"></i> {% trans "في الانتظار" %}
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times"></i> {% trans "ملغاة" %}
                                    </span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="table-responsive mb-4">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>{% trans "المنتج" %}</th>
                                <th>{% trans "الوصف" %}</th>
                                <th class="text-center">{% trans "الكمية" %}</th>
                                <th class="text-end">{% trans "سعر الوحدة" %}</th>
                                <th class="text-end">{% trans "الإجمالي" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in invoice_items %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>
                                    <strong>{{ item.product.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ item.product.category.name }}</small>
                                </td>
                                <td>{{ item.product.description|default:"-" }}</td>
                                <td class="text-center">{{ item.quantity }}</td>
                                <td class="text-end">{{ item.unit_price|floatformat:2 }} {% trans "أوقية" %}</td>
                                <td class="text-end">{{ item.total_price|floatformat:2 }} {% trans "أوقية" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Invoice Totals -->
                <div class="row">
                    <div class="col-md-6 offset-md-6">
                        <div class="invoice-total">
                            <table class="table table-sm mb-0">
                                <tr>
                                    <td><strong>{% trans "المجموع الفرعي" %}:</strong></td>
                                    <td class="text-end">{{ invoice.subtotal|floatformat:2 }} {% trans "أوقية" %}</td>
                                </tr>
                                {% if invoice.discount_percentage > 0 %}
                                <tr>
                                    <td><strong>{% trans "الخصم" %} ({{ invoice.discount_percentage }}%):</strong></td>
                                    <td class="text-end">-{{ invoice.discount_amount|floatformat:2 }} {% trans "أوقية" %}</td>
                                </tr>
                                {% endif %}
                                {% if invoice.tax_percentage > 0 %}
                                <tr>
                                    <td><strong>{% trans "الضريبة" %} ({{ invoice.tax_percentage }}%):</strong></td>
                                    <td class="text-end">{{ invoice.tax_amount|floatformat:2 }} {% trans "أوقية" %}</td>
                                </tr>
                                {% endif %}
                                <tr class="table-primary">
                                    <td><strong>{% trans "الإجمالي النهائي" %}:</strong></td>
                                    <td class="text-end"><strong>{{ invoice.total_amount|floatformat:2 }} {% trans "أوقية" %}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                {% if invoice.notes %}
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-sticky-note"></i> {% trans "ملاحظات" %}
                            </h6>
                            <p class="mb-0">{{ invoice.notes }}</p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Footer -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <hr>
                        <p class="text-muted mb-0">
                            {% trans "شكراً لتعاملكم معنا" %} | {% trans "نظام إدارة المبيعات" %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .invoice-header {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
</style>
{% endblock %}
