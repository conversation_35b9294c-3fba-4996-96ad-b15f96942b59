{% load i18n %}
{% load translation_extras %}

<!-- Ultra Fast Language Switcher -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i> 
        <span id="current-lang-display">
            {% get_current_language_code as current_lang %}
            {% if current_lang == 'ar' %}العربية{% else %}Français{% endif %}
        </span>
    </a>
    <ul class="dropdown-menu">
        {% get_current_language_code as current_lang %}
        {% if current_lang == 'ar' %}
            <li>
                <a href="#" class="dropdown-item ultra-fast-switch" data-lang="fr" data-name="Français">
                    <i class="fas fa-language"></i> Français
                </a>
            </li>
        {% else %}
            <li>
                <a href="#" class="dropdown-item ultra-fast-switch" data-lang="ar" data-name="العربية">
                    <i class="fas fa-language"></i> العربية
                </a>
            </li>
        {% endif %}
    </ul>
</li>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ultra fast language switching
    document.querySelectorAll('.ultra-fast-switch').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const language = this.getAttribute('data-lang');
            const langName = this.getAttribute('data-name');
            const currentDisplay = document.getElementById('current-lang-display');
            
            // Immediate visual feedback
            currentDisplay.textContent = langName;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
            
            // Change language in session immediately
            fetch('/set-language/?language=' + language, {
                method: 'GET',
                cache: 'no-cache'
            })
            .then(response => {
                // Reload page immediately without waiting
                window.location.reload();
            })
            .catch(error => {
                // Even on error, reload to ensure consistency
                window.location.reload();
            });
        });
    });
});
</script>

<style>
.ultra-fast-switch {
    transition: all 0.1s ease !important;
}
.ultra-fast-switch:hover {
    background-color: #f8f9fa !important;
    transform: translateX(2px);
}
.language-switcher .dropdown-toggle {
    transition: all 0.2s ease;
}
</style>
