{% load i18n %}
{% load translation_extras %}

<!-- Dynamic Language Switcher with Auto Direction -->
<li class="nav-item dropdown language-switcher">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-globe"></i>
        <span id="current-lang-display">
            {% if request.session.django_language == 'fr' %}
                Français
            {% else %}
                العربية
            {% endif %}
        </span>
    </a>
    <ul class="dropdown-menu" id="lang-dropdown">
        {% if request.session.django_language == 'fr' %}
            <li>
                <a href="#" class="dropdown-item dynamic-lang-switch" data-lang="ar" data-name="العربية" data-dir="rtl">
                    <i class="fas fa-language"></i> العربية
                </a>
            </li>
        {% else %}
            <li>
                <a href="#" class="dropdown-item dynamic-lang-switch" data-lang="fr" data-name="Français" data-dir="ltr">
                    <i class="fas fa-language"></i> Français
                </a>
            </li>
        {% endif %}
    </ul>
</li>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dynamic language switch with direction change
    document.querySelectorAll('.dynamic-lang-switch').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const language = this.getAttribute('data-lang');
            const langName = this.getAttribute('data-name');
            const direction = this.getAttribute('data-dir');
            const currentDisplay = document.getElementById('current-lang-display');
            const dropdown = document.getElementById('lang-dropdown');

            // Immediate visual feedback
            currentDisplay.textContent = langName;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';

            // Change page direction immediately
            document.documentElement.setAttribute('dir', direction);
            document.documentElement.setAttribute('lang', language);

            // Update dropdown content immediately
            if (language === 'ar') {
                dropdown.innerHTML = `
                    <li>
                        <a href="#" class="dropdown-item dynamic-lang-switch" data-lang="fr" data-name="Français" data-dir="ltr">
                            <i class="fas fa-language"></i> Français
                        </a>
                    </li>
                `;
            } else {
                dropdown.innerHTML = `
                    <li>
                        <a href="#" class="dropdown-item dynamic-lang-switch" data-lang="ar" data-name="العربية" data-dir="rtl">
                            <i class="fas fa-language"></i> العربية
                        </a>
                    </li>
                `;
            }

            // Re-attach event listeners to new dropdown items
            dropdown.querySelectorAll('.dynamic-lang-switch').forEach(function(newLink) {
                newLink.addEventListener('click', arguments.callee);
            });

            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/set-language/';
            form.style.display = 'none';

            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
            form.appendChild(csrfInput);

            // Add language
            const langInput = document.createElement('input');
            langInput.type = 'hidden';
            langInput.name = 'language';
            langInput.value = language;
            form.appendChild(langInput);

            // Add next URL
            const nextInput = document.createElement('input');
            nextInput.type = 'hidden';
            nextInput.name = 'next';
            nextInput.value = window.location.pathname;
            form.appendChild(nextInput);

            // Submit form
            document.body.appendChild(form);
            form.submit();
        });
    });
});
</script>

<style>
.dynamic-lang-switch {
    cursor: pointer;
    transition: background-color 0.2s;
}
.dynamic-lang-switch:hover {
    background-color: #f8f9fa !important;
}
.language-switcher .dropdown-toggle {
    transition: all 0.2s ease;
}

/* Smooth direction transition */
#html-root {
    transition: all 0.3s ease;
}

/* Direction-specific styles */
[dir="rtl"] .navbar-nav {
    flex-direction: row-reverse;
}

[dir="ltr"] .navbar-nav {
    flex-direction: row;
}

/* Language switching animation */
.switching {
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

/* Bootstrap RTL/LTR specific adjustments */
[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

[dir="ltr"] .dropdown-menu {
    left: 0;
    right: auto;
}
</style>
