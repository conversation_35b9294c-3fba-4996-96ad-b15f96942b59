"""
Custom middleware for handling translations and language switching
"""
from django.utils import translation
from django.conf import settings
from django.http import HttpResponseRedirect
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)

class LanguageMiddleware:
    """
    Custom middleware to handle language switching gracefully
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Get language from session or use default
        language = request.session.get('django_language', settings.LANGUAGE_CODE)

        # Validate language - only allow ar and fr
        if language not in ['ar', 'fr']:
            language = settings.LANGUAGE_CODE

        # Activate language globally
        translation.activate(language)
        request.LANGUAGE_CODE = language
        request.current_language = language

        # Set language in thread local for template access
        import threading
        if not hasattr(threading.current_thread(), 'language'):
            threading.current_thread().language = language

        response = self.get_response(request)

        # Set language header for debugging
        response['X-Current-Language'] = language
        response['Content-Language'] = language

        return response

class TranslationFallbackMiddleware:
    """
    Middleware to provide fallback translations when .mo files are missing
    """
    
    # Enhanced fallback translations with better coverage
    FALLBACK_TRANSLATIONS = {
        'fr': {
            # Navigation
            'نظام إدارة المبيعات': 'Système de Gestion des Ventes',
            'لوحة التحكم': 'Tableau de Bord',
            'العملاء': 'Clients',
            'المنتجات': 'Produits',
            'الفواتير': 'Factures',
            'التقارير': 'Rapports',
            'الموردين': 'Fournisseurs',
            'الفئات': 'Catégories',

            # Dashboard items
            'إجمالي العملاء': 'Total des Clients',
            'إجمالي المنتجات': 'Total des Produits',
            'إجمالي الفواتير': 'Total des Factures',
            'الفواتير المعلقة': 'Factures en Attente',
            'الفواتير الأخيرة': 'Factures Récentes',
            'منتجات قليلة المخزون': 'Produits à Stock Faible',
            'إجراءات سريعة': 'Actions Rapides',
            'إنشاء فاتورة جديدة': 'Créer une Nouvelle Facture',
            'إضافة عميل جديد': 'Ajouter un Nouveau Client',
            'إضافة منتج جديد': 'Ajouter un Nouveau Produit',
            'عرض التقارير': 'Voir les Rapports',

            # Actions
            'إضافة': 'Ajouter',
            'تعديل': 'Modifier',
            'حذف': 'Supprimer',
            'عرض': 'Voir',
            'بحث': 'Rechercher',
            'حفظ': 'Enregistrer',
            'إلغاء': 'Annuler',
            'طباعة': 'Imprimer',

            # Products
            'الباركود': 'Code-barres',
            'إنشاء تلقائي': 'Generation Automatique',
            'معاينة': 'Apercu',
            'السعر': 'Prix',
            'الكمية': 'Quantite',
            'الوصف': 'Description',
            'اسم المنتج': 'Nom du Produit',

            # Customers
            'اسم العميل': 'Nom du Client',
            'قائمة العملاء': 'Liste des Clients',
            'إضافة عميل': 'Ajouter un Client',

            # Invoices
            'رقم الفاتورة': 'Numero de Facture',
            'التاريخ': 'Date',
            'المبلغ': 'Montant',
            'الحالة': 'Statut',
            'مدفوعة': 'Payee',
            'في الانتظار': 'En Attente',

            # Currency
            'أوقية': 'Ouguiya',

            # System
            'تسجيل الخروج': 'Deconnexion',
            'لوحة الإدارة': 'Panneau d\'Administration',
            'جميع الحقوق محفوظة': 'Tous droits reserves',

            # Reports
            'التقارير المتقدمة': 'Rapports Avances',
            'يومي': 'Quotidien',
            'أسبوعي': 'Hebdomadaire',
            'شهري': 'Mensuel',
            'فترة مخصصة': 'Periode Personnalisee',
            'من تاريخ': 'Date de Debut',
            'إلى تاريخ': 'Date de Fin',
            'عرض التقرير': 'Afficher le Rapport',
            'إجمالي المبيعات': 'Total des Ventes',
            'عدد الفواتير': 'Nombre de Factures',
            'متوسط قيمة الفاتورة': 'Valeur Moyenne de Facture',

            # Barcode
            'طباعة الباركود': 'Imprimer Code-barres',
            'إدارة الباركود': 'Gestion Code-barres',
            'البحث بالباركود': 'Recherche par Code-barres',
            'تم إنشاء الباركود بنجاح': 'Code-barres genere avec succes',
            'خطأ في إنشاء الباركود': 'Erreur lors de la generation du code-barres',

            # Status messages
            'جاري التحميل...': 'Chargement...',
            'تم الحفظ بنجاح': 'Enregistre avec succes',
            'تم الحذف بنجاح': 'Supprime avec succes',
            'تم التحديث بنجاح': 'Mis a jour avec succes',
            'حدث خطأ': 'Une erreur s\'est produite',
            'العملية تمت بنجاح': 'Operation reussie',
            'لا توجد بيانات': 'Aucune donnee',
            'لا توجد نتائج': 'Aucun resultat',

            # Time
            'اليوم': 'Aujourd\'hui',
            'أمس': 'Hier',
            'هذا الأسبوع': 'Cette semaine',
            'هذا الشهر': 'Ce mois',
            'الأسبوع الماضي': 'La semaine derniere',
            'الشهر الماضي': 'Le mois dernier',
            'انقر على اللغة للتبديل الفوري': 'Cliquez sur la langue pour un changement instantane',
            'اختبار تبديل اللغة': 'Test de Changement de Langue',
            'اللغة الحالية': 'Langue Actuelle',
            'نصوص للاختبار': 'Textes de Test',
            'تبديل اللغة': 'Changer de Langue',
            'العودة للرئيسية': 'Retour a l\'Accueil',
        }
    }
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Apply fallback translations if needed
        current_language = getattr(request, 'LANGUAGE_CODE', settings.LANGUAGE_CODE)
        
        if current_language in self.FALLBACK_TRANSLATIONS:
            # This is a simple fallback - in a real implementation,
            # you might want to modify the response content
            pass
            
        return response

    @classmethod
    def get_translation(cls, text, language):
        """Get translation for text in specified language"""
        if language in cls.FALLBACK_TRANSLATIONS:
            return cls.FALLBACK_TRANSLATIONS[language].get(text, text)
        return text


class AdminLanguageMiddleware:
    """Middleware to handle language switching in Django Admin"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if this is an admin request
        if request.path.startswith('/admin/'):
            # Get language from session or default
            language = request.session.get('django_language', settings.LANGUAGE_CODE)

            # Check if language is being changed via URL parameter
            if 'language' in request.GET:
                new_language = request.GET.get('language')
                if new_language in ['ar', 'fr']:
                    language = new_language
                    request.session['django_language'] = language

            # Activate the language for admin
            translation.activate(language)
            request.LANGUAGE_CODE = language

            # Set direction for RTL/LTR
            if language == 'ar':
                request.direction = 'rtl'
            else:
                request.direction = 'ltr'

        response = self.get_response(request)

        return response
