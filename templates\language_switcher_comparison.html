{% load static %}
{% load translation_extras %}

<!DOCTYPE html>
<html lang="{{ lang.code }}" dir="{{ lang.direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>مقارنة مبدلات اللغة - Language Switcher Comparison</title>
    
    <!-- Bootstrap CSS -->
    {% if lang.is_rtl %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        
        .comparison-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .switcher-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
            position: relative;
            min-height: 150px;
        }
        
        .demo-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .demo-description {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 5px 0;
            color: #666;
        }
        
        .features-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .current-lang-display {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- CSRF Token for AJAX requests -->
    {% csrf_token %}
    
    <div class="comparison-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="display-4">
                <i class="fas fa-balance-scale"></i>
                مقارنة مبدلات اللغة
            </h1>
            <p class="lead">Comparaison des Commutateurs de Langue</p>
        </div>
        
        <!-- Current Language Info -->
        <div class="current-lang-display">
            <h4>
                <i class="fas fa-globe"></i>
                اللغة الحالية / Langue Actuelle: 
                <strong>{{ request.LANGUAGE_CODE|upper }}</strong>
            </h4>
            <p class="mb-0">
                {% if request.LANGUAGE_CODE == 'ar' %}
                    العربية (Arabic) - الاتجاه: من اليمين إلى اليسار
                {% else %}
                    الفرنسية (Français) - الاتجاه: من اليسار إلى اليمين
                {% endif %}
            </p>
        </div>
        
        <!-- Compact Switcher Demo -->
        <div class="switcher-demo">
            <div class="demo-title">
                <i class="fas fa-compress-alt"></i>
                المبدل المدمج - Compact Switcher
            </div>
            
            <div class="demo-description">
                <strong>الوصف:</strong> مبدل صغير ومدمج مع أزرار عمودية صغيرة<br>
                <strong>Description:</strong> Petit commutateur compact avec boutons verticaux
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <ul class="features-list">
                        <li>حجم صغير جداً ومدمج</li>
                        <li>أزرار عمودية مع أعلام الدول</li>
                        <li>مؤشر نشط صغير</li>
                        <li>مناسب للمساحات الضيقة</li>
                        <li>ترجمة سريعة للعناصر الرئيسية</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <!-- Compact Language Switcher -->
                    {% include 'sales/compact_language_switcher.html' %}
                </div>
            </div>
        </div>
        
        <!-- Enhanced Switcher Demo -->
        <div class="switcher-demo">
            <div class="demo-title">
                <i class="fas fa-expand-alt"></i>
                المبدل المحسن - Enhanced Switcher
            </div>
            
            <div class="demo-description">
                <strong>الوصف:</strong> مبدل متوسط الحجم مع تفاصيل أكثر<br>
                <strong>Description:</strong> Commutateur de taille moyenne avec plus de détails
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <ul class="features-list">
                        <li>حجم متوسط مع تفاصيل واضحة</li>
                        <li>عرض اسم اللغة ورمزها</li>
                        <li>فاصل متحرك بين الأزرار</li>
                        <li>مؤشر حالة مفصل</li>
                        <li>ترجمة شاملة للمحتوى</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <!-- Enhanced Language Switcher -->
                    <div style="position: relative; height: 100px;">
                        {% include 'sales/enhanced_language_switcher.html' %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bidirectional Switcher Demo -->
        <div class="switcher-demo">
            <div class="demo-title">
                <i class="fas fa-exchange-alt"></i>
                المبدل ثنائي الاتجاه - Bidirectional Switcher
            </div>
            
            <div class="demo-description">
                <strong>الوصف:</strong> مبدل متقدم مع لوحة منسدلة وميزات إضافية<br>
                <strong>Description:</strong> Commutateur avancé avec panneau déroulant et fonctionnalités supplémentaires
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <ul class="features-list">
                        <li>لوحة منسدلة مع معاينة الترجمة</li>
                        <li>إجراءات سريعة للاختبار</li>
                        <li>إحصائيات الترجمة</li>
                        <li>تأثيرات بصرية متقدمة</li>
                        <li>ميزات تفاعلية شاملة</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <!-- Bidirectional Language Switcher -->
                    <div style="position: relative; height: 100px;">
                        {% include 'sales/bidirectional_language_switcher.html' %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Comparison Table -->
        <div class="switcher-demo">
            <div class="demo-title">
                <i class="fas fa-table"></i>
                جدول المقارنة - Comparison Table
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>الميزة / Feature</th>
                            <th>المدمج / Compact</th>
                            <th>المحسن / Enhanced</th>
                            <th>ثنائي الاتجاه / Bidirectional</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>الحجم / Size</td>
                            <td><span class="badge bg-success">صغير جداً</span></td>
                            <td><span class="badge bg-warning">متوسط</span></td>
                            <td><span class="badge bg-info">كبير</span></td>
                        </tr>
                        <tr>
                            <td>سهولة الاستخدام / Ease of Use</td>
                            <td><span class="badge bg-success">ممتاز</span></td>
                            <td><span class="badge bg-success">ممتاز</span></td>
                            <td><span class="badge bg-warning">جيد</span></td>
                        </tr>
                        <tr>
                            <td>الميزات / Features</td>
                            <td><span class="badge bg-warning">أساسي</span></td>
                            <td><span class="badge bg-success">متقدم</span></td>
                            <td><span class="badge bg-success">شامل</span></td>
                        </tr>
                        <tr>
                            <td>سرعة الترجمة / Translation Speed</td>
                            <td><span class="badge bg-success">سريع</span></td>
                            <td><span class="badge bg-success">سريع</span></td>
                            <td><span class="badge bg-warning">متوسط</span></td>
                        </tr>
                        <tr>
                            <td>التوافق مع الأجهزة / Device Compatibility</td>
                            <td><span class="badge bg-success">ممتاز</span></td>
                            <td><span class="badge bg-success">ممتاز</span></td>
                            <td><span class="badge bg-warning">جيد</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Recommendations -->
        <div class="switcher-demo">
            <div class="demo-title">
                <i class="fas fa-lightbulb"></i>
                التوصيات - Recommendations
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h6><i class="fas fa-mobile-alt"></i> للأجهزة المحمولة</h6>
                        </div>
                        <div class="card-body">
                            <p><strong>المبدل المدمج</strong></p>
                            <p>الأفضل للمساحات الصغيرة والأجهزة المحمولة</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h6><i class="fas fa-desktop"></i> للمواقع العامة</h6>
                        </div>
                        <div class="card-body">
                            <p><strong>المبدل المحسن</strong></p>
                            <p>توازن مثالي بين الحجم والوظائف</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h6><i class="fas fa-cogs"></i> للتطبيقات المتقدمة</h6>
                        </div>
                        <div class="card-body">
                            <p><strong>المبدل ثنائي الاتجاه</strong></p>
                            <p>ميزات شاملة للمستخدمين المتقدمين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Links -->
        <div class="text-center mt-4">
            <h4>صفحات الاختبار / Test Pages</h4>
            <div class="btn-group" role="group">
                <a href="/language-switcher-test/" class="btn btn-primary">
                    <i class="fas fa-vial"></i> اختبار المبدلات
                </a>
                <a href="/translation-test/" class="btn btn-success">
                    <i class="fas fa-language"></i> اختبار الترجمة
                </a>
                <a href="/api-test/" class="btn btn-info">
                    <i class="fas fa-code"></i> اختبار API
                </a>
                <a href="/bidirectional-demo/" class="btn btn-warning">
                    <i class="fas fa-exchange-alt"></i> العرض التفاعلي
                </a>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight current language info
            const currentLangInfo = document.querySelector('.current-lang-display');
            if (currentLangInfo) {
                setInterval(() => {
                    currentLangInfo.style.transform = 'scale(1.01)';
                    setTimeout(() => {
                        currentLangInfo.style.transform = 'scale(1)';
                    }, 200);
                }, 5000);
            }
            
            // Add hover effects to demo sections
            document.querySelectorAll('.switcher-demo').forEach(demo => {
                demo.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                });
                
                demo.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
