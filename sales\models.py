from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import random
import string


class UserProfile(models.Model):
    """Extended user profile with language preference"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    preferred_language = models.CharField(
        max_length=10,
        choices=[
            ('ar', 'العربية'),
            ('fr', 'Français'),
        ],
        default='ar',
        verbose_name=_('اللغة المفضلة')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('ملف المستخدم')
        verbose_name_plural = _('ملفات المستخدمين')

    def __str__(self):
        return f"{self.user.username} - {self.get_preferred_language_display()}"


class Customer(models.Model):
    """نموذج العملاء"""
    name = models.CharField(_('الاسم'), max_length=200)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('عميل')
        verbose_name_plural = _('العملاء')
        ordering = ['name']

    def __str__(self):
        return self.name


class Category(models.Model):
    """نموذج فئات المنتجات"""
    name = models.CharField(_('اسم الفئة'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('فئة')
        verbose_name_plural = _('الفئات')
        ordering = ['name']

    def __str__(self):
        return self.name


class Product(models.Model):
    """نموذج المنتجات"""
    name = models.CharField(_('اسم المنتج'), max_length=200)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name=_('الفئة'))
    price = models.DecimalField(_('السعر'), max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    stock_quantity = models.PositiveIntegerField(_('الكمية في المخزن'), default=0)
    barcode = models.CharField(_('الباركود'), max_length=50, unique=True, blank=True, null=True)
    image = models.ImageField(_('صورة المنتج'), upload_to='products/', blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('منتج')
        verbose_name_plural = _('المنتجات')
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # إنشاء باركود تلقائي إذا لم يكن موجود
        if not self.barcode:
            self.barcode = self.generate_barcode()
        super().save(*args, **kwargs)

    def generate_barcode(self):
        """إنشاء باركود فريد للمنتج"""
        # إنشاء باركود من 13 رقم (EAN-13 format)
        while True:
            # أول 3 أرقام: كود البلد (616 للمغرب العربي)
            country_code = "616"
            # 4 أرقام للشركة
            company_code = f"{self.category.id:04d}"
            # 5 أرقام للمنتج
            product_code = f"{random.randint(10000, 99999)}"
            # رقم التحقق (سيتم حسابه)
            barcode_without_check = country_code + company_code + product_code

            # حساب رقم التحقق
            check_digit = self.calculate_check_digit(barcode_without_check)
            barcode = barcode_without_check + str(check_digit)

            # التأكد من أن الباركود فريد
            if not Product.objects.filter(barcode=barcode).exists():
                return barcode

    def calculate_check_digit(self, barcode):
        """حساب رقم التحقق للباركود EAN-13"""
        odd_sum = sum(int(barcode[i]) for i in range(0, 12, 2))
        even_sum = sum(int(barcode[i]) for i in range(1, 12, 2))
        total = odd_sum + (even_sum * 3)
        return (10 - (total % 10)) % 10

    @property
    def is_in_stock(self):
        return self.stock_quantity > 0

    @property
    def barcode_display(self):
        """عرض الباركود بتنسيق جميل"""
        if self.barcode and len(self.barcode) == 13:
            return f"{self.barcode[:1]} {self.barcode[1:7]} {self.barcode[7:13]}"
        return self.barcode


class Invoice(models.Model):
    """نموذج الفواتير"""
    PAYMENT_STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('paid', _('مدفوعة')),
        ('cancelled', _('ملغاة')),
    ]

    invoice_number = models.CharField(_('رقم الفاتورة'), max_length=20, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name=_('العميل'))
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('المستخدم'))
    date_created = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    date_due = models.DateField(_('تاريخ الاستحقاق'), blank=True, null=True)
    payment_status = models.CharField(_('حالة الدفع'), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    discount_percentage = models.DecimalField(_('نسبة الخصم'), max_digits=5, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))])
    tax_percentage = models.DecimalField(_('نسبة الضريبة'), max_digits=5, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))])

    class Meta:
        verbose_name = _('فاتورة')
        verbose_name_plural = _('الفواتير')
        ordering = ['-date_created']

    def __str__(self):
        return f"{self.invoice_number} - {self.customer.name}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            # Generate invoice number
            last_invoice = Invoice.objects.order_by('-id').first()
            if last_invoice:
                last_number = int(last_invoice.invoice_number.split('-')[-1])
                self.invoice_number = f"INV-{last_number + 1:06d}"
            else:
                self.invoice_number = "INV-000001"
        super().save(*args, **kwargs)

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.invoiceitem_set.all())

    @property
    def discount_amount(self):
        return (self.subtotal * self.discount_percentage) / 100

    @property
    def tax_amount(self):
        return ((self.subtotal - self.discount_amount) * self.tax_percentage) / 100

    @property
    def total_amount(self):
        return self.subtotal - self.discount_amount + self.tax_amount


class InvoiceItem(models.Model):
    """نموذج عناصر الفاتورة"""
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, verbose_name=_('الفاتورة'))
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name=_('المنتج'))
    quantity = models.PositiveIntegerField(_('الكمية'), validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(_('سعر الوحدة'), max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])

    class Meta:
        verbose_name = _('عنصر الفاتورة')
        verbose_name_plural = _('عناصر الفاتورة')

    def __str__(self):
        return f"{self.product.name} - {self.quantity}"

    @property
    def total_price(self):
        return self.quantity * self.unit_price

    def save(self, *args, **kwargs):
        if not self.unit_price:
            self.unit_price = self.product.price
        super().save(*args, **kwargs)


class Supplier(models.Model):
    """نموذج الموردين"""
    name = models.CharField(_('اسم المورد'), max_length=200)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    contact_person = models.CharField(_('الشخص المسؤول'), max_length=100, blank=True, null=True)
    tax_number = models.CharField(_('الرقم الضريبي'), max_length=50, blank=True, null=True)
    payment_terms = models.CharField(_('شروط الدفع'), max_length=100, blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    date_created = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    date_updated = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مورد')
        verbose_name_plural = _('الموردين')
        ordering = ['name']

    def __str__(self):
        return self.name


class PurchaseInvoice(models.Model):
    """نموذج فواتير الشراء"""
    PAYMENT_STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('partial', _('دفع جزئي')),
        ('paid', _('مدفوعة')),
        ('overdue', _('متأخرة')),
    ]

    invoice_number = models.CharField(_('رقم الفاتورة'), max_length=50, unique=True, blank=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name=_('المورد'))
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('المستخدم'))
    date_created = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    date_due = models.DateField(_('تاريخ الاستحقاق'))
    payment_status = models.CharField(_('حالة الدفع'), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    discount_percentage = models.DecimalField(_('نسبة الخصم'), max_digits=5, decimal_places=2, default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    tax_percentage = models.DecimalField(_('نسبة الضريبة'), max_digits=5, decimal_places=2, default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    class Meta:
        verbose_name = _('فاتورة شراء')
        verbose_name_plural = _('فواتير الشراء')
        ordering = ['-date_created']

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            # Generate invoice number
            last_invoice = PurchaseInvoice.objects.filter(
                invoice_number__startswith='PUR'
            ).order_by('-id').first()

            if last_invoice:
                last_number = int(last_invoice.invoice_number.replace('PUR-', ''))
                new_number = last_number + 1
            else:
                new_number = 1

            self.invoice_number = f'PUR-{new_number:06d}'

        super().save(*args, **kwargs)

    def __str__(self):
        return f"فاتورة شراء {self.invoice_number} - {self.supplier.name}"

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.purchaseinvoiceitem_set.all())

    @property
    def discount_amount(self):
        return (self.subtotal * self.discount_percentage) / 100

    @property
    def tax_amount(self):
        return ((self.subtotal - self.discount_amount) * self.tax_percentage) / 100

    @property
    def total_amount(self):
        return self.subtotal - self.discount_amount + self.tax_amount


class PurchaseInvoiceItem(models.Model):
    """نموذج عناصر فواتير الشراء"""
    purchase_invoice = models.ForeignKey(PurchaseInvoice, on_delete=models.CASCADE, verbose_name=_('فاتورة الشراء'))
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name=_('المنتج'))
    quantity = models.PositiveIntegerField(_('الكمية'), validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(_('سعر الوحدة'), max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])

    class Meta:
        verbose_name = _('عنصر فاتورة الشراء')
        verbose_name_plural = _('عناصر فواتير الشراء')

    def __str__(self):
        return f"{self.product.name} - {self.quantity}"

    @property
    def total_price(self):
        return self.quantity * self.unit_price

    def save(self, *args, **kwargs):
        if not self.unit_price:
            self.unit_price = self.product.price
        super().save(*args, **kwargs)
