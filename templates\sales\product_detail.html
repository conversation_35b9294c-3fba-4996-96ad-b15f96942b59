{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ product.name }} - {% trans "تفاصيل المنتج" %}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-box"></i> {% trans "تفاصيل المنتج" %}: {{ product.name }}
        </h1>
    </div>
</div>

<div class="row">
    <!-- Product Image -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-image"></i> {% trans "صورة المنتج" %}
                </h6>
            </div>
            <div class="card-body text-center">
                {% if product.image %}
                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded" style="max-height: 300px;">
                {% else %}
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 300px;">
                        <i class="fas fa-image fa-4x text-muted"></i>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Product Details -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle"></i> {% trans "معلومات المنتج" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% trans "اسم المنتج" %}:</strong></td>
                                <td>{{ product.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "الفئة" %}:</strong></td>
                                <td>{{ product.category.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "السعر" %}:</strong></td>
                                <td class="text-primary h5">{{ product.price }} {% trans "أوقية" %}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "المخزون" %}:</strong></td>
                                <td>
                                    {% if product.is_in_stock %}
                                        <span class="badge bg-success">{{ product.stock_quantity }} {% trans "متوفر" %}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{% trans "غير متوفر" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "الحالة" %}:</strong></td>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">{% trans "نشط" %}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{% trans "غير نشط" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% trans "الباركود" %}:</strong></td>
                                <td>
                                    {% if product.barcode %}
                                        <span class="font-monospace">{{ product.barcode }}</span>
                                        <br>
                                        <a href="{% url 'sales:product_barcode' product.pk %}" class="btn btn-sm btn-outline-success mt-1">
                                            <i class="fas fa-barcode"></i> {% trans "عرض الباركود" %}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">{% trans "لا يوجد باركود" %}</span>
                                        <br>
                                        <button class="btn btn-sm btn-primary mt-1" onclick="generateBarcode()">
                                            <i class="fas fa-plus"></i> {% trans "إنشاء باركود" %}
                                        </button>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "تاريخ الإنشاء" %}:</strong></td>
                                <td>{{ product.created_at|date:"d/m/Y H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "آخر تحديث" %}:</strong></td>
                                <td>{{ product.updated_at|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if product.description %}
                    <div class="mt-3">
                        <h6><strong>{% trans "الوصف" %}:</strong></h6>
                        <p class="text-muted">{{ product.description }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-tools"></i> {% trans "الإجراءات" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'sales:product_edit' product.pk %}" class="btn btn-warning w-100">
                            <i class="fas fa-edit"></i> {% trans "تعديل المنتج" %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        {% if product.barcode %}
                            <a href="{% url 'sales:product_barcode' product.pk %}" class="btn btn-success w-100">
                                <i class="fas fa-barcode"></i> {% trans "إدارة الباركود" %}
                            </a>
                        {% else %}
                            <button class="btn btn-success w-100" onclick="generateBarcode()">
                                <i class="fas fa-barcode"></i> {% trans "إنشاء باركود" %}
                            </button>
                        {% endif %}
                    </div>
                    <div class="col-md-3 mb-2">
                        {% if product.barcode %}
                            <a href="{% url 'sales:product_barcode_print' product.pk %}" class="btn btn-info w-100" target="_blank">
                                <i class="fas fa-print"></i> {% trans "طباعة الباركود" %}
                            </a>
                        {% else %}
                            <button class="btn btn-info w-100" disabled>
                                <i class="fas fa-print"></i> {% trans "طباعة الباركود" %}
                            </button>
                        {% endif %}
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'sales:product_delete' product.pk %}" class="btn btn-danger w-100 delete-confirm">
                            <i class="fas fa-trash"></i> {% trans "حذف المنتج" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Back Button -->
<div class="row mt-4">
    <div class="col-12">
        <a href="{% url 'sales:product_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> {% trans "العودة لقائمة المنتجات" %}
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Generate barcode function
function generateBarcode() {
    if (confirm('{% trans "هل تريد إنشاء باركود لهذا المنتج؟" %}')) {
        fetch(`{% url 'sales:generate_barcode' product.pk %}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{% trans "تم إنشاء الباركود بنجاح" %}');
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "حدث خطأ أثناء إنشاء الباركود" %}');
        });
    }
}

// Delete confirmation
document.addEventListener('DOMContentLoaded', function() {
    const deleteLinks = document.querySelectorAll('.delete-confirm');
    deleteLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (!confirm('{% trans "هل أنت متأكد من حذف هذا المنتج؟" %}')) {
                e.preventDefault();
            }
        });
    });
});
</script>

<style>
.font-monospace {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
}

.table-borderless td {
    padding: 0.5rem 0.75rem;
    vertical-align: middle;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
</style>
{% endblock %}
