#!/usr/bin/env python
"""Test database connection"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_system.settings')
django.setup()

def test_database():
    """Test database connection and models"""
    try:
        from sales.models import Customer, Supplier, Product, Category
        
        print("Testing database connection...")
        
        # Test Customer model
        customers = Customer.objects.all()
        print(f"Customers count: {customers.count()}")
        
        # Test Supplier model
        suppliers = Supplier.objects.all()
        print(f"Suppliers count: {suppliers.count()}")
        
        # Test Category model
        categories = Category.objects.all()
        print(f"Categories count: {categories.count()}")
        
        # Test Product model
        products = Product.objects.all()
        print(f"Products count: {products.count()}")
        
        print("Database connection successful!")
        return True
        
    except Exception as e:
        print(f"Database error: {e}")
        return False

if __name__ == '__main__':
    success = test_database()
    sys.exit(0 if success else 1)
