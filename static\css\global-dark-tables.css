/* GLOBAL DARK THEME FOR ALL TABLES */
/* تطبيق شامل للخلفية السوداء على جميع الجداول في النظام */

/* ========================================
   UNIVERSAL TABLE STYLING
   ======================================== */

/* Apply dark theme to ALL table elements */
table,
.table,
.table-responsive table,
.changelist-results,
#changelist-table,
.results,
.module table,
.form-table,
.inline-group table,
.tabular table,
.admin table {
    background-color: #2c3e50 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    border: 2px solid #4a6741 !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
}

/* ========================================
   TABLE HEADERS
   ======================================== */

/* All table headers */
table thead th,
table th,
.table thead th,
.table th,
.changelist-results thead th,
#changelist-table thead th,
.results thead th,
.module table thead th,
.form-table th,
.inline-group table th,
.tabular table th,
.admin table th {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%) !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    letter-spacing: 1px !important;
    padding: 15px 12px !important;
    border: none !important;
    border-bottom: 2px solid #4a6741 !important;
}

/* ========================================
   TABLE CELLS
   ======================================== */

/* All table cells */
table td,
.table td,
.changelist-results td,
#changelist-table td,
.results td,
.module table td,
.form-table td,
.inline-group table td,
.tabular table td,
.admin table td {
    background-color: #2c3e50 !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    padding: 12px 10px !important;
    border-bottom: 1px solid #4a6741 !important;
    border-left: none !important;
    border-right: none !important;
    vertical-align: middle !important;
}

/* ========================================
   ALTERNATING ROWS
   ======================================== */

/* Even rows */
table tbody tr:nth-child(even) td,
.table tbody tr:nth-child(even) td,
.changelist-results tbody tr:nth-child(even) td,
#changelist-table tbody tr:nth-child(even) td,
.results tbody tr:nth-child(even) td,
.module table tbody tr:nth-child(even) td,
.form-table tbody tr:nth-child(even) td,
.inline-group table tbody tr:nth-child(even) td,
.tabular table tbody tr:nth-child(even) td,
.admin table tbody tr:nth-child(even) td {
    background-color: #34495e !important;
}

/* ========================================
   HOVER EFFECTS
   ======================================== */

/* Hover effects for all tables */
table tbody tr:hover td,
.table tbody tr:hover td,
.changelist-results tbody tr:hover td,
#changelist-table tbody tr:hover td,
.results tbody tr:hover td,
.module table tbody tr:hover td,
.form-table tbody tr:hover td,
.inline-group table tbody tr:hover td,
.tabular table tbody tr:hover td,
.admin table tbody tr:hover td {
    background-color: #4a90e2 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3) !important;
    transition: all 0.3s ease !important;
}

/* ========================================
   COLORED TEXT BASED ON CONTENT
   ======================================== */

/* First column (usually IDs) - Red */
table td:first-child,
.table td:first-child,
.changelist-results td:first-child,
#changelist-table td:first-child,
.results td:first-child {
    color: #ff6b6b !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    font-family: 'Courier New', monospace !important;
}

/* Links in tables - Blue */
table a,
.table a,
.changelist-results a,
#changelist-table a,
.results a {
    color: #74c0fc !important;
    text-decoration: none !important;
    font-weight: 600 !important;
}

table a:hover,
.table a:hover,
.changelist-results a:hover,
#changelist-table a:hover,
.results a:hover {
    color: #339af0 !important;
    text-decoration: underline !important;
}

/* Strong text - Yellow */
table strong,
.table strong,
.changelist-results strong,
#changelist-table strong,
.results strong {
    color: #ffd43b !important;
    font-weight: 700 !important;
}

/* ========================================
   BADGES AND STATUS INDICATORS
   ======================================== */

/* All badges */
table .badge,
.table .badge,
.changelist-results .badge,
#changelist-table .badge,
.results .badge {
    font-weight: 700 !important;
    font-size: 11px !important;
    padding: 6px 12px !important;
    border-radius: 20px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border: 2px solid transparent !important;
}

.badge.bg-success {
    background-color: #51cf66 !important;
    color: #ffffff !important;
    border-color: #40c057 !important;
}

.badge.bg-danger {
    background-color: #ff6b6b !important;
    color: #ffffff !important;
    border-color: #ff5252 !important;
}

.badge.bg-warning {
    background-color: #ffd43b !important;
    color: #2c3e50 !important;
    border-color: #ffcc02 !important;
}

.badge.bg-info {
    background-color: #74c0fc !important;
    color: #ffffff !important;
    border-color: #339af0 !important;
}

.badge.bg-primary {
    background-color: #da77f2 !important;
    color: #ffffff !important;
    border-color: #cc5de8 !important;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
    table td,
    .table td {
        padding: 8px 6px !important;
        font-size: 12px !important;
    }
    
    table thead th,
    .table thead th {
        padding: 10px 8px !important;
        font-size: 10px !important;
    }
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
    table,
    .table {
        background-color: #ffffff !important;
        color: #000000 !important;
        border: 1px solid #000000 !important;
        box-shadow: none !important;
    }
    
    table td,
    .table td,
    table th,
    .table th {
        background-color: #ffffff !important;
        color: #000000 !important;
        border: 1px solid #000000 !important;
    }
}

/* ========================================
   SPECIAL OVERRIDES
   ======================================== */

/* Force dark theme even on elements with inline styles */
table[style*="background"],
.table[style*="background"] {
    background-color: #2c3e50 !important;
}

td[style*="background"],
th[style*="background"] {
    background-color: #2c3e50 !important;
}

/* Ensure text is always visible */
table *,
.table * {
    color: inherit !important;
}
