{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "لوحة التحكم" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt"></i> {% trans "لوحة التحكم" %}
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي العملاء" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "إجمالي المنتجات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_products }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "إجمالي الفواتير" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_invoices }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "الفواتير المعلقة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_invoices }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Invoices -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-file-invoice"></i> {% trans "الفواتير الأخيرة" %}
                </h6>
                <a href="{% url 'sales:invoice_list' %}" class="btn btn-primary btn-sm">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                {% if recent_invoices %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{% trans "رقم الفاتورة" %}</th>
                                    <th>{% trans "العميل" %}</th>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices %}
                                <tr>
                                    <td>
                                        <a href="{% url 'sales:invoice_detail' invoice.pk %}">
                                            {{ invoice.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ invoice.customer.name }}</td>
                                    <td>{{ invoice.date_created|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if invoice.payment_status == 'paid' %}
                                            <span class="badge bg-success">{% trans "مدفوعة" %}</span>
                                        {% elif invoice.payment_status == 'pending' %}
                                            <span class="badge bg-warning">{% trans "في الانتظار" %}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{% trans "ملغاة" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ invoice.total_amount|floatformat:2 }} {% trans "أوقية" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">{% trans "لا توجد فواتير حتى الآن" %}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Low Stock Products -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle"></i> {% trans "منتجات قليلة المخزون" %}
                </h6>
            </div>
            <div class="card-body">
                {% if low_stock_products %}
                    {% for product in low_stock_products %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-0">{{ product.name }}</h6>
                                <small class="text-muted">{{ product.category.name }}</small>
                            </div>
                            <span class="badge bg-warning">{{ product.stock_quantity }}</span>
                        </div>
                    {% endfor %}
                    <a href="{% url 'sales:product_list' %}" class="btn btn-warning btn-sm w-100">
                        {% trans "إدارة المخزون" %}
                    </a>
                {% else %}
                    <p class="text-muted">{% trans "جميع المنتجات متوفرة في المخزون" %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt"></i> {% trans "إجراءات سريعة" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'sales:invoice_add' %}" class="btn btn-primary w-100">
                            <i class="fas fa-plus"></i> {% trans "إنشاء فاتورة جديدة" %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'sales:customer_add' %}" class="btn btn-success w-100">
                            <i class="fas fa-user-plus"></i> {% trans "إضافة عميل جديد" %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'sales:product_add' %}" class="btn btn-info w-100">
                            <i class="fas fa-box"></i> {% trans "إضافة منتج جديد" %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'sales:reports' %}" class="btn btn-warning w-100">
                            <i class="fas fa-chart-bar"></i> {% trans "عرض التقارير" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .text-gray-800 {
        color: #5a5c69 !important;
    }
    .text-gray-300 {
        color: #dddfeb !important;
    }
</style>
{% endblock %}
