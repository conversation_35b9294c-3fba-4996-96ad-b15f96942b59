-- Create MySQL tables for suppliers and purchase invoices
USE sales_system;

-- Create suppliers table
CREATE TABLE IF NOT EXISTS sales_supplier (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    email VARCHAR(254) NULL,
    phone VARCHAR(20) NULL,
    address TEXT NULL,
    contact_person VARCHAR(100) NULL,
    tax_number VARCHAR(50) NULL,
    payment_terms VARCHAR(100) NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create purchase invoices table
CREATE TABLE IF NOT EXISTS sales_purchaseinvoice (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_id INT NOT NULL,
    user_id INT NOT NULL,
    date_created DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_due DATE NOT NULL,
    payment_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    notes TEXT NULL,
    discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    tax_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    FOREIGN KEY (supplier_id) REFERENCES sales_supplier(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES auth_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create purchase invoice items table
CREATE TABLE IF NOT EXISTS sales_purchaseinvoiceitem (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (purchase_invoice_id) REFERENCES sales_purchaseinvoice(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES sales_product(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample suppliers
INSERT IGNORE INTO sales_supplier (name, email, phone, contact_person, is_active) VALUES
('شركة التوريدات الذهبية', '<EMAIL>', '+222-45-123456', 'أحمد ولد محمد', TRUE),
('مؤسسة النور للتجارة', '<EMAIL>', '+222-45-654321', 'فاطمة بنت أحمد', TRUE),
('شركة الصحراء للمواد الغذائية', '<EMAIL>', '+222-45-789012', 'محمد ولد عبدالله', TRUE);

-- Update Django migrations table
INSERT IGNORE INTO django_migrations (app, name, applied) VALUES 
('sales', '0003_supplier_purchaseinvoice_purchaseinvoiceitem', NOW());

-- Show created tables
SHOW TABLES LIKE 'sales_%';
