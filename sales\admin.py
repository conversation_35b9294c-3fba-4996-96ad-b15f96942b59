from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from .models import Customer, Category, Product, Invoice, InvoiceItem, Supplier, PurchaseInvoice, PurchaseInvoiceItem

# Base admin class with dark theme
class DarkThemeAdminMixin:
    """
    Mixin to add dark theme CSS to admin classes
    """
    class Media:
        css = {
            'all': ('admin/css/admin-dark-theme.css',)
        }


@admin.register(Customer)
class CustomerAdmin(DarkThemeAdminMixin, admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'email', 'phone']
    ordering = ['name']


@admin.register(Category)
class CategoryAdmin(DarkThemeAdminMixin, admin.ModelAdmin):
    list_display = ['name', 'created_at']
    search_fields = ['name']
    ordering = ['name']


@admin.register(Product)
class ProductAdmin(DarkThemeAdminMixin, admin.ModelAdmin):
    list_display = ['name', 'category', 'price', 'stock_quantity', 'is_active', 'created_at']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']
    list_editable = ['price', 'stock_quantity', 'is_active']


class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 1
    fields = ['product', 'quantity', 'unit_price']

    class Media:
        css = {
            'all': ('admin/css/admin-dark-theme.css',)
        }


@admin.register(Invoice)
class InvoiceAdmin(DarkThemeAdminMixin, admin.ModelAdmin):
    list_display = ['invoice_number', 'customer', 'user', 'date_created', 'payment_status', 'total_amount']
    list_filter = ['payment_status', 'date_created', 'user']
    search_fields = ['invoice_number', 'customer__name']
    ordering = ['-date_created']
    inlines = [InvoiceItemInline]
    readonly_fields = ['invoice_number']

    def total_amount(self, obj):
        return f"{obj.total_amount:.2f} أوقية"
    total_amount.short_description = 'المبلغ الإجمالي'


@admin.register(Supplier)
class SupplierAdmin(DarkThemeAdminMixin, admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'contact_person', 'is_active', 'date_created']
    list_filter = ['is_active', 'date_created']
    search_fields = ['name', 'email', 'phone', 'contact_person']
    list_editable = ['is_active']
    readonly_fields = ['date_created', 'date_updated']
    ordering = ['name']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'contact_person', 'is_active')
        }),
        ('معلومات الاتصال', {
            'fields': ('email', 'phone', 'address')
        }),
        ('معلومات تجارية', {
            'fields': ('tax_number', 'payment_terms')
        }),
        ('تواريخ', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        }),
    )


class PurchaseInvoiceItemInline(admin.TabularInline):
    model = PurchaseInvoiceItem
    extra = 1
    fields = ['product', 'quantity', 'unit_price', 'total_price']
    readonly_fields = ['total_price']

    class Media:
        css = {
            'all': ('admin/css/admin-dark-theme.css',)
        }

    def total_price(self, obj):
        if obj.quantity and obj.unit_price:
            return f"{obj.quantity * obj.unit_price:.2f} أوقية"
        return "0.00 أوقية"
    total_price.short_description = 'الإجمالي'


@admin.register(PurchaseInvoice)
class PurchaseInvoiceAdmin(DarkThemeAdminMixin, admin.ModelAdmin):
    list_display = ['invoice_number', 'supplier', 'date_created', 'date_due', 'payment_status', 'get_total_amount']
    list_filter = ['payment_status', 'date_created', 'date_due', 'supplier']
    search_fields = ['invoice_number', 'supplier__name']
    readonly_fields = ['invoice_number', 'date_created', 'get_subtotal', 'get_discount_amount', 'get_tax_amount', 'get_total_amount']
    inlines = [PurchaseInvoiceItemInline]
    ordering = ['-date_created']

    fieldsets = (
        ('معلومات الفاتورة', {
            'fields': ('invoice_number', 'supplier', 'user', 'date_created')
        }),
        ('تفاصيل الدفع', {
            'fields': ('date_due', 'payment_status')
        }),
        ('الخصومات والضرائب', {
            'fields': ('discount_percentage', 'tax_percentage')
        }),
        ('الإجماليات', {
            'fields': ('get_subtotal', 'get_discount_amount', 'get_tax_amount', 'get_total_amount'),
            'classes': ('collapse',)
        }),
        ('ملاحظات', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )

    def get_subtotal(self, obj):
        return f"{obj.subtotal:.2f} أوقية"
    get_subtotal.short_description = 'المجموع الفرعي'

    def get_discount_amount(self, obj):
        return f"{obj.discount_amount:.2f} أوقية"
    get_discount_amount.short_description = 'مبلغ الخصم'

    def get_tax_amount(self, obj):
        return f"{obj.tax_amount:.2f} أوقية"
    get_tax_amount.short_description = 'مبلغ الضريبة'

    def get_total_amount(self, obj):
        return f"{obj.total_amount:.2f} أوقية"
    get_total_amount.short_description = 'المبلغ الإجمالي'

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.user = request.user
        super().save_model(request, obj, form, change)
