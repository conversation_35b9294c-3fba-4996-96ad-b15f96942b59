"""
API Middleware for handling CORS and JSON responses
"""

import json
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

class APIMiddleware(MiddlewareMixin):
    """
    Middleware to handle API requests properly
    """
    
    def process_request(self, request):
        """
        Process incoming API requests
        """
        # Check if this is an API request
        if request.path.startswith('/api/'):
            # Add CORS headers for API requests
            request.is_api_request = True
            
            # Handle preflight requests
            if request.method == 'OPTIONS':
                response = JsonResponse({'status': 'ok'})
                self.add_cors_headers(response)
                return response
        
        return None
    
    def process_response(self, request, response):
        """
        Process API responses
        """
        # Add CORS headers to API responses
        if hasattr(request, 'is_api_request') and request.is_api_request:
            self.add_cors_headers(response)
            
            # Ensure JSON content type
            if not response.get('Content-Type'):
                response['Content-Type'] = 'application/json; charset=utf-8'
        
        return response
    
    def add_cors_headers(self, response):
        """
        Add CORS headers to response
        """
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-CSRFToken'
        response['Access-Control-Allow-Credentials'] = 'true'

class JSONResponseMiddleware(MiddlewareMixin):
    """
    Middleware to ensure proper JSON responses for API endpoints
    """
    
    def process_exception(self, request, exception):
        """
        Handle exceptions in API requests
        """
        if hasattr(request, 'is_api_request') and request.is_api_request:
            return JsonResponse({
                'success': False,
                'error': str(exception),
                'type': exception.__class__.__name__
            }, status=500)
        
        return None
    
    def process_response(self, request, response):
        """
        Ensure API responses are properly formatted
        """
        if hasattr(request, 'is_api_request') and request.is_api_request:
            # If response is not JSON, wrap it
            if response.status_code == 404:
                return JsonResponse({
                    'success': False,
                    'error': 'API endpoint not found',
                    'path': request.path
                }, status=404)
            
            # If response is HTML (error page), convert to JSON
            content_type = response.get('Content-Type', '')
            if 'text/html' in content_type and response.status_code >= 400:
                return JsonResponse({
                    'success': False,
                    'error': f'Server error: {response.status_code}',
                    'path': request.path
                }, status=response.status_code)
        
        return response

class CSRFExemptAPIMiddleware(MiddlewareMixin):
    """
    Middleware to exempt API endpoints from CSRF protection
    """
    
    def process_request(self, request):
        """
        Exempt API requests from CSRF
        """
        if request.path.startswith('/api/'):
            setattr(request, '_dont_enforce_csrf_checks', True)
        
        return None
