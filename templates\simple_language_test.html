{% load translation_extras %}
<!DOCTYPE html>
{% load translation_extras %}
{% get_current_language_code as current_lang %}
<html lang="{{ current_lang }}" dir="{% if current_lang == 'ar' %}rtl{% else %}ltr{% endif %}" id="test-html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تبديل اللغة البسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-language"></i> اختبار تبديل اللغة البسيط</h3>
                    </div>
                    <div class="card-body">
                        <!-- Session Info -->
                        <div class="alert alert-info">
                            <h5>معلومات الجلسة:</h5>
                            <ul class="mb-0">
                                <li><strong>اللغة في الجلسة:</strong> 
                                    <span class="badge bg-primary">{{ request.session.django_language|default:"غير محددة" }}</span>
                                </li>
                                <li><strong>اللغة الحالية:</strong> 
                                    {% get_current_language_code as current_lang %}
                                    <span class="badge bg-success">{{ current_lang }}</span>
                                </li>
                                <li><strong>اللغة الافتراضية:</strong> 
                                    <span class="badge bg-secondary">ar</span>
                                </li>
                            </ul>
                        </div>

                        <!-- Translation Test -->
                        <div class="mb-4">
                            <h5 class="translatable">اختبار الترجمات</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-group">
                                        <li class="list-group-item translatable">نظام إدارة المبيعات</li>
                                        <li class="list-group-item translatable">لوحة التحكم</li>
                                        <li class="list-group-item translatable">العملاء</li>
                                        <li class="list-group-item translatable">قائمة العملاء</li>
                                        <li class="list-group-item translatable">إضافة عميل</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-group">
                                        <li class="list-group-item translatable">المنتجات</li>
                                        <li class="list-group-item translatable">الفواتير</li>
                                        <li class="list-group-item translatable">التقارير</li>
                                        <li class="list-group-item translatable">الموردين</li>
                                        <li class="list-group-item translatable">الفئات</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Language Switching -->
                        <div class="mb-4">
                            <h5>تبديل اللغة:</h5>
                            <div class="btn-group" role="group">
                                <!-- Arabic Button -->
                                <form method="post" action="/set-language/" class="d-inline final-test-form">
                                    {% csrf_token %}
                                    <input type="hidden" name="language" value="ar">
                                    <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                    <button type="submit" class="btn btn-primary btn-lg final-test-btn">
                                        <i class="fas fa-language"></i> العربية
                                    </button>
                                </form>

                                <!-- French Button -->
                                <form method="post" action="/set-language/" class="d-inline final-test-form">
                                    {% csrf_token %}
                                    <input type="hidden" name="language" value="fr">
                                    <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                    <button type="submit" class="btn btn-success btn-lg final-test-btn">
                                        <i class="fas fa-language"></i> Français
                                    </button>
                                </form>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">انقر على اللغة للتبديل الفوري</small>
                            </div>
                        </div>

                        <!-- Direct Links -->
                        <div class="mb-4">
                            <h5>روابط مباشرة:</h5>
                            <div class="btn-group" role="group">
                                <a href="/set-language/?language=ar" class="btn btn-outline-primary">
                                    <i class="fas fa-link"></i> العربية (GET)
                                </a>
                                <a href="/set-language/?language=fr" class="btn btn-outline-success">
                                    <i class="fas fa-link"></i> Français (GET)
                                </a>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="text-center">
                            <a href="/" class="btn btn-secondary">
                                <i class="fas fa-home"></i> العودة للرئيسية
                            </a>
                            <button onclick="location.reload()" class="btn btn-info">
                                <i class="fas fa-sync"></i> تحديث الصفحة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // Translation dictionary for test page
    const testTranslations = {
        'ar': {
            'اختبار تبديل اللغة البسيط': 'اختبار تبديل اللغة البسيط',
            'معلومات الجلسة': 'معلومات الجلسة',
            'اللغة في الجلسة': 'اللغة في الجلسة',
            'اللغة الحالية': 'اللغة الحالية',
            'اللغة الافتراضية': 'اللغة الافتراضية',
            'غير محددة': 'غير محددة',
            'اختبار الترجمات': 'اختبار الترجمات',
            'نظام إدارة المبيعات': 'نظام إدارة المبيعات',
            'لوحة التحكم': 'لوحة التحكم',
            'العملاء': 'العملاء',
            'قائمة العملاء': 'قائمة العملاء',
            'إضافة عميل': 'إضافة عميل',
            'المنتجات': 'المنتجات',
            'الفواتير': 'الفواتير',
            'التقارير': 'التقارير',
            'الموردين': 'الموردين',
            'الفئات': 'الفئات',
            'تبديل اللغة': 'تبديل اللغة',
            'العربية': 'العربية',
            'Français': 'Français',
            'روابط مباشرة': 'روابط مباشرة',
            'العودة للرئيسية': 'العودة للرئيسية',
            'تحديث الصفحة': 'تحديث الصفحة',
            'انقر على اللغة للتبديل الفوري': 'انقر على اللغة للتبديل الفوري'
        },
        'fr': {
            'اختبار تبديل اللغة البسيط': 'Test Simple de Changement de Langue',
            'معلومات الجلسة': 'Informations de Session',
            'اللغة في الجلسة': 'Langue en Session',
            'اللغة الحالية': 'Langue Actuelle',
            'اللغة الافتراضية': 'Langue par Défaut',
            'غير محددة': 'Non définie',
            'اختبار الترجمات': 'Test des Traductions',
            'نظام إدارة المبيعات': 'Système de Gestion des Ventes',
            'لوحة التحكم': 'Tableau de Bord',
            'العملاء': 'Clients',
            'قائمة العملاء': 'Liste des Clients',
            'إضافة عميل': 'Ajouter un Client',
            'المنتجات': 'Produits',
            'الفواتير': 'Factures',
            'التقارير': 'Rapports',
            'الموردين': 'Fournisseurs',
            'الفئات': 'Catégories',
            'تبديل اللغة': 'Changer de Langue',
            'العربية': 'العربية',
            'Français': 'Français',
            'روابط مباشرة': 'Liens Directs',
            'العودة للرئيسية': 'Retour à l\'Accueil',
            'تحديث الصفحة': 'Actualiser la Page',
            'انقر على اللغة للتبديل الفوري': 'Cliquez sur la langue pour un changement instantané'
        }
    };

    function translateTestPage(language) {
        const dict = testTranslations[language];
        if (!dict) return;

        // Translate all translatable elements
        document.querySelectorAll('.translatable, h1, h2, h3, h4, h5, h6, p, button, a, small').forEach(function(element) {
            // Skip elements with icons
            if (element.querySelector('.fas, .fa')) return;

            const text = element.textContent.trim();
            if (text && dict[text]) {
                element.textContent = dict[text];
            }
        });

        // Update session info badges
        document.querySelectorAll('.badge').forEach(function(badge) {
            const text = badge.textContent.trim();
            if (text && dict[text]) {
                badge.textContent = dict[text];
            }
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Add feedback for test buttons with direction change and translation
        document.querySelectorAll('.final-test-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const form = this.closest('form');
                const language = form.querySelector('input[name="language"]').value;

                // Show feedback when form is submitted
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تبديل...';
                this.disabled = true;

                // Change direction immediately for visual feedback
                const htmlRoot = document.getElementById('test-html-root');
                if (language === 'ar') {
                    htmlRoot.setAttribute('dir', 'rtl');
                    htmlRoot.setAttribute('lang', 'ar');
                } else {
                    htmlRoot.setAttribute('dir', 'ltr');
                    htmlRoot.setAttribute('lang', 'fr');
                }

                // Translate page content immediately
                translateTestPage(language);

                // Add switching class for animation
                document.body.classList.add('switching');
            });
        });

        // Initial translation based on current language
        const currentLang = document.getElementById('test-html-root').getAttribute('lang') || 'ar';
        translateTestPage(currentLang);
    });
    </script>

    <style>
    /* Direction transition styles */
    #test-html-root {
        transition: all 0.3s ease;
    }

    .switching {
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    /* RTL/LTR specific styles */
    [dir="rtl"] .btn-group {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .text-center {
        text-align: right !important;
    }

    [dir="ltr"] .text-center {
        text-align: left !important;
    }
    </style>
</body>
</html>
