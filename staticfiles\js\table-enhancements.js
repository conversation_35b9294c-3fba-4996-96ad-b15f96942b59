/* Table Enhancement JavaScript */
/* تحسينات الجداول بـ JavaScript */

document.addEventListener('DOMContentLoaded', function() {
    enhanceAllTables();
    setupTableInteractions();
});

function enhanceAllTables() {
    // Find all tables and apply enhancements
    const tables = document.querySelectorAll('table');
    
    tables.forEach(function(table) {
        // Skip if already enhanced
        if (table.classList.contains('table-enhanced')) {
            return;
        }
        
        // Add enhanced class
        table.classList.add('table-enhanced');
        
        // Enhance table cells based on content
        enhanceTableCells(table);
        
        // Add sorting capabilities if not already present
        addSortingCapabilities(table);
        
        // Add responsive behavior
        makeTableResponsive(table);
    });
}

function enhanceTableCells(table) {
    const cells = table.querySelectorAll('td');
    
    cells.forEach(function(cell) {
        const content = cell.textContent.trim();
        
        // Detect and style different data types
        if (isNumber(content)) {
            cell.classList.add('data-number');
        } else if (isPrice(content)) {
            cell.classList.add('data-price');
        } else if (isCode(content)) {
            cell.classList.add('data-code');
        } else if (content.length > 0) {
            cell.classList.add('data-text');
        }
        
        // Add status styling for common status words
        if (isStatus(content)) {
            addStatusStyling(cell, content);
        }
    });
}

function isNumber(str) {
    // Check if string is a pure number (like ID, quantity, etc.)
    return /^\d+$/.test(str) && str.length <= 6;
}

function isPrice(str) {
    // Check if string contains price indicators
    return /\d+\.?\d*\s*(أوقية|MRU|UM|ouguiya)/i.test(str) || 
           /^\d+\.?\d*$/.test(str) && parseFloat(str) > 10;
}

function isCode(str) {
    // Check if string looks like a code (mix of letters and numbers)
    return /^[A-Z0-9]{3,}$/i.test(str) && /\d/.test(str) && /[A-Z]/i.test(str);
}

function isStatus(str) {
    const statusWords = [
        'نشط', 'غير نشط', 'متوفر', 'غير متوفر', 'مدفوع', 'غير مدفوع',
        'مكتمل', 'معلق', 'ملغي', 'active', 'inactive', 'available', 
        'unavailable', 'paid', 'unpaid', 'completed', 'pending', 'cancelled'
    ];
    return statusWords.some(word => str.toLowerCase().includes(word.toLowerCase()));
}

function addStatusStyling(cell, content) {
    const activeWords = ['نشط', 'متوفر', 'مدفوع', 'مكتمل', 'active', 'available', 'paid', 'completed'];
    const inactiveWords = ['غير نشط', 'غير متوفر', 'غير مدفوع', 'ملغي', 'inactive', 'unavailable', 'unpaid', 'cancelled'];
    const pendingWords = ['معلق', 'pending'];
    
    if (activeWords.some(word => content.toLowerCase().includes(word.toLowerCase()))) {
        cell.classList.add('status-active');
    } else if (inactiveWords.some(word => content.toLowerCase().includes(word.toLowerCase()))) {
        cell.classList.add('status-inactive');
    } else if (pendingWords.some(word => content.toLowerCase().includes(word.toLowerCase()))) {
        cell.classList.add('status-pending');
    }
}

function addSortingCapabilities(table) {
    const headers = table.querySelectorAll('thead th');
    
    headers.forEach(function(header, index) {
        // Skip if header already has sorting
        if (header.classList.contains('sortable')) {
            return;
        }
        
        header.classList.add('sortable');
        header.style.cursor = 'pointer';
        
        header.addEventListener('click', function() {
            sortTable(table, index, header);
        });
    });
}

function sortTable(table, columnIndex, header) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Determine sort direction
    let ascending = true;
    if (header.classList.contains('asc')) {
        ascending = false;
        header.classList.remove('asc');
        header.classList.add('desc');
    } else {
        // Remove sorting classes from all headers
        table.querySelectorAll('thead th').forEach(h => {
            h.classList.remove('asc', 'desc');
        });
        header.classList.add('asc');
    }
    
    // Sort rows
    rows.sort(function(a, b) {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        // Try to parse as numbers first
        const aNum = parseFloat(aText.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bText.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return ascending ? aNum - bNum : bNum - aNum;
        }
        
        // Fall back to string comparison
        return ascending ? aText.localeCompare(bText) : bText.localeCompare(aText);
    });
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
    
    // Add animation
    tbody.style.opacity = '0.7';
    setTimeout(() => {
        tbody.style.opacity = '1';
    }, 200);
}

function makeTableResponsive(table) {
    // Wrap table in responsive container if not already wrapped
    if (!table.parentElement.classList.contains('table-responsive')) {
        const wrapper = document.createElement('div');
        wrapper.classList.add('table-responsive');
        table.parentElement.insertBefore(wrapper, table);
        wrapper.appendChild(table);
    }
    
    // Add mobile-friendly behavior
    if (window.innerWidth <= 768) {
        table.classList.add('compact');
    }
}

function setupTableInteractions() {
    // Add loading state functionality
    window.showTableLoading = function(tableSelector) {
        const table = document.querySelector(tableSelector);
        if (table) {
            table.classList.add('loading');
        }
    };
    
    window.hideTableLoading = function(tableSelector) {
        const table = document.querySelector(tableSelector);
        if (table) {
            table.classList.remove('loading');
        }
    };
    
    // Add row selection functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('tr') && e.target.closest('tbody')) {
            const row = e.target.closest('tr');
            const table = row.closest('table');
            
            // Toggle selection if table supports it
            if (table.classList.contains('selectable')) {
                row.classList.toggle('selected');
            }
        }
    });
    
    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        const focusedTable = document.querySelector('table:focus-within');
        if (!focusedTable) return;
        
        const rows = focusedTable.querySelectorAll('tbody tr');
        const currentRow = focusedTable.querySelector('tbody tr.focused');
        let currentIndex = currentRow ? Array.from(rows).indexOf(currentRow) : -1;
        
        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentIndex < rows.length - 1) {
                    if (currentRow) currentRow.classList.remove('focused');
                    rows[currentIndex + 1].classList.add('focused');
                    rows[currentIndex + 1].scrollIntoView({ block: 'nearest' });
                }
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                if (currentIndex > 0) {
                    if (currentRow) currentRow.classList.remove('focused');
                    rows[currentIndex - 1].classList.add('focused');
                    rows[currentIndex - 1].scrollIntoView({ block: 'nearest' });
                }
                break;
                
            case 'Enter':
                if (currentRow) {
                    const link = currentRow.querySelector('a');
                    if (link) link.click();
                }
                break;
        }
    });
}

// Auto-enhance tables when new content is loaded via AJAX
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const tables = node.querySelectorAll ? node.querySelectorAll('table') : [];
                    if (node.tagName === 'TABLE') {
                        enhanceAllTables();
                    } else if (tables.length > 0) {
                        enhanceAllTables();
                    }
                }
            });
        }
    });
});

// Start observing
observer.observe(document.body, {
    childList: true,
    subtree: true
});

// Window resize handler
window.addEventListener('resize', function() {
    const tables = document.querySelectorAll('table.table-enhanced');
    tables.forEach(function(table) {
        if (window.innerWidth <= 768) {
            table.classList.add('compact');
        } else {
            table.classList.remove('compact');
        }
    });
});

// Export functions for external use
window.TableEnhancements = {
    enhance: enhanceAllTables,
    showLoading: window.showTableLoading,
    hideLoading: window.hideTableLoading
};
